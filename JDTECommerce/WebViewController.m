//
//  WebViewController.m
//  JDTECommerce
//
//  Created by lvchenzhu.1 on 2025/7/9.
//

#import "WebViewController.h"

@import WebKit;

@interface WebViewController ()

@property (nonatomic, strong) WKWebView *webView;

@end

@implementation WebViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor whiteColor];
    
    // 使用Auto Layout来避免内容被导航栏遮挡
    self.webView = [[WKWebView alloc] initWithFrame:CGRectZero];
    self.webView.translatesAutoresizingMaskIntoConstraints = NO;
    
    // 设置User-Agent
    self.webView.customUserAgent = @"name=kaapp;versionName=v1.0.0;platform=iOS;schema=b2cApp";
    
    [self.view addSubview:self.webView];
    
    // 添加Auto Layout约束
    [NSLayoutConstraint activateConstraints:@[
        [self.webView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.webView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.webView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.webView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];
    
    if (self.urlString.length > 0) {
        NSURL *url = [NSURL URLWithString:self.urlString];
        
        // 设置cookie
        WKHTTPCookieStore *cookieStore = self.webView.configuration.websiteDataStore.httpCookieStore;
        
        // 创建b2c_c_lang cookie
        NSHTTPCookie *langCookie = [NSHTTPCookie cookieWithProperties:@{
            NSHTTPCookieName: @"b2c_c_lang",
            NSHTTPCookieValue: @"zh_CN",
            NSHTTPCookieDomain: @".jdt.com.cn",
            NSHTTPCookiePath: @"/",
        }];
        
        // 创建x-jnos-token-c cookie
        NSHTTPCookie *tokenCookie = [NSHTTPCookie cookieWithProperties:@{
            NSHTTPCookieName: @"x-jnos-token-c",
            NSHTTPCookieValue: @"dEOpZgRs13e.N0FYYcWEWHZ.GG4wP0Jv81Qrr6S5Is4i2",
            NSHTTPCookieDomain: @".jdt.com.cn",
            NSHTTPCookiePath: @"/",
        }];
        
        // 添加cookie到store
        [cookieStore setCookie:langCookie completionHandler:nil];
        [cookieStore setCookie:tokenCookie completionHandler:^{
            // 在设置完cookie后加载请求
            NSURLRequest *request = [NSURLRequest requestWithURL:url];
            [self.webView loadRequest:request];
            
        }];
    }
}

@end
