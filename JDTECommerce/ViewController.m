//
//  ViewController.m
//  JDTECommerce
//
//  Created by lvchenzhu.1 on 2025/5/26.
//

#import "ViewController.h"
#import "NewProductDetailController.h"
#import "JDISVShoppingCartController.h"
#import "WebViewController.h"

#import <JDBRouterModule/JDRouter.h>

@import JDISVPlatformModule;
@import JDTInfrastructureModule;

@interface ViewController () <UITextFieldDelegate, UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITextField *tokenTextField;
@property (nonatomic, strong) UIButton *loginBtn;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray<NSDictionary *> *menuItems;

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"轻量版商城Demo";
    
    [self setupMenuItems];
    [self setupUI];
    [self loadTokenFromUserDefaults];
}

- (void)setupMenuItems {
    self.menuItems = @[
        @{@"title": @"商详", @"action": @"gotoProductDetailVC"},
        @{@"title": @"购物车(含结算页)", @"action": @"gotoCartVC"},
        @{@"title": @"订单", @"action": @"gotoOrderVC"},
        @{@"title": @"地址", @"action": @"gotoAddressVC"},
        @{@"title": @"售后", @"action": @"gotoAfterSaleVC"},
        @{@"title": @"个人中心(H5)", @"action": @"gotoWebViewVC"}
    ];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    // Token输入框
    self.tokenTextField = [[UITextField alloc] init];
    self.tokenTextField.borderStyle = UITextBorderStyleRoundedRect;
    self.tokenTextField.placeholder = @"请输入您的 token";
    self.tokenTextField.clearButtonMode = UITextFieldViewModeWhileEditing;
    self.tokenTextField.delegate = self;
    [self.view addSubview:self.tokenTextField];

    // 登录按钮
    self.loginBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.loginBtn setTitle:@"登录（您还未登录）" forState:UIControlStateNormal];
    self.loginBtn.backgroundColor = [UIColor systemBlueColor];
    [self.loginBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.loginBtn.layer.cornerRadius = 8;
    self.loginBtn.clipsToBounds = YES;
    [self.loginBtn addTarget:self action:@selector(onClickLoginBtn) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.loginBtn];

    // TableView
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    self.tableView.rowHeight = 60;
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"MenuCell"];
    [self.view addSubview:self.tableView];

    [self setupConstraints];
}

- (void)setupConstraints {
    self.tokenTextField.translatesAutoresizingMaskIntoConstraints = NO;
    self.loginBtn.translatesAutoresizingMaskIntoConstraints = NO;
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;

    [NSLayoutConstraint activateConstraints:@[
        // Token输入框约束
        [self.tokenTextField.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:20],
        [self.tokenTextField.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.tokenTextField.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.tokenTextField.heightAnchor constraintEqualToConstant:44],

        // 登录按钮约束
        [self.loginBtn.topAnchor constraintEqualToAnchor:self.tokenTextField.bottomAnchor constant:20],
        [self.loginBtn.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.loginBtn.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.loginBtn.heightAnchor constraintEqualToConstant:44],

        // TableView约束
        [self.tableView.topAnchor constraintEqualToAnchor:self.loginBtn.bottomAnchor constant:20],
        [self.tableView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.tableView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.tableView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor]
    ]];
}

- (void)loadTokenFromUserDefaults {
    NSString *token = [[NSUserDefaults standardUserDefaults] objectForKey:@"kUDTestToken"];
    if (token.length > 0) {
        self.tokenTextField.text = token;
        [self.loginBtn setTitle:@"登录（您已登录）" forState:UIControlStateNormal];
    }
}

#pragma mark - UITableView DataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.menuItems.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MenuCell" forIndexPath:indexPath];

    NSDictionary *menuItem = self.menuItems[indexPath.row];
    cell.textLabel.text = menuItem[@"title"];
    cell.textLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    cell.textLabel.textColor = [UIColor systemBlueColor];
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    cell.selectionStyle = UITableViewCellSelectionStyleDefault;

    return cell;
}

#pragma mark - UITableView Delegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    NSDictionary *menuItem = self.menuItems[indexPath.row];
    NSString *action = menuItem[@"action"];

    SEL selector = NSSelectorFromString(action);
    if ([self respondsToSelector:selector]) {
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Warc-performSelector-leaks"
        [self performSelector:selector];
        #pragma clang diagnostic pop
    }
}

#pragma mark - Navigation Methods

- (void)gotoProductDetailVC {
    NewProductDetailController *vc = [[NewProductDetailController alloc] init];
    // 小程序的商品(24680315)
    vc.sku = @"41041997";
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)gotoCartVC {
    JDISVShoppingCartController *vc = [[JDISVShoppingCartController alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)gotoOrderVC {
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/orderListMainController",[[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeOrderList]] arg:@{@"order_list_type": @0} error:nil completion:^(UIViewController * viewController) {
        if(viewController){
            viewController.hidesBottomBarWhenPushed = NO;
            [self.navigationController pushViewController:viewController animated:YES];
//            [JDRouter openURL:@"router://KSALoginModule/changeTabbarStatus" arg:@{@"tag":@(233)} error:nil completion:nil];
        }
    }];
}

- (void)gotoAddressVC {
    NSMutableDictionary *arg = [NSMutableDictionary dictionary];
    [arg addEntriesFromDictionary:@{ @"canSelect": @0 }];
    [arg addEntriesFromDictionary:@{ @"backVC": self }];
    
    NSString *addressListViewControllerURL = [NSString stringWithFormat:@"router://%@/addressListViewController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
    [JDRouter openURL:addressListViewControllerURL arg:[arg copy] error:nil completion:^(NSDictionary *object) {
        UIViewController *vc = [object objectForKey:@"viewController"];
        if (vc) {
            [self.navigationController pushViewController:vc animated:YES];
        }
    }];
}

- (void)gotoAfterSaleVC {
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/getAfterSaleApplyListViewController",[JDISV_RESOURCE_MANAGER moduleNameWithModuleType:JDISVModuleTypeAfterSales]] arg:nil error:nil completion:^(id  _Nullable object) {
        if ([object isKindOfClass:[UIViewController class]]) {
            UIViewController *vc = object;
            if (vc) {
                [self.navigationController pushViewController:vc animated:YES];
            }
        }
    }];
}

- (void)gotoWebViewVC {
    // 设置要打开的 H5 链接
//    NSString *h5Url = @"https://www.jd.com"; // 您可以替换为需要的 H5 链接
//
//    // 配置参数
//    NSDictionary *param = @{
//        @"text_url": h5Url ?: @""
//    };
//
//    // 获取 WebView 模块名称
//    NSString *webModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView];
//    NSString *routerStr = [NSString stringWithFormat:@"router://%@/getWebViewController", webModule];
//
//    // 使用 JDRouter 跳转到 WebView 控制器
//    [JDRouter openURL:routerStr arg:param error:nil completion:^(UIViewController *webViewController) {
//        if (webViewController) {
//            webViewController.hidesBottomBarWhenPushed = YES;
//            [self.navigationController pushViewController:webViewController animated:YES];
//        }
//    }];
    WebViewController *webVC = [[WebViewController alloc] init];
    webVC.urlString = @"http://m.jdx-ka-v2-dev.building2-dev.jdt.com.cn/user/center";
    [self.navigationController pushViewController:webVC animated:YES];
}

#pragma mark - Login Methods

- (void)onClickLoginBtn {
    NSString *token = self.tokenTextField.text;
    [self.tokenTextField resignFirstResponder];

    if (token.length > 0) {
        [[OOPNetworkManager sharedManager] setCommonHeaders:@{
            @"x-jnos-token-c": token
        }];
        [[OOPAdvancedNetworkManager sharedManager] setCommonHeaders:@{
            @"x-jnos-token-c": token
        }];
        [self.loginBtn setTitle:@"登录（您已登录）" forState:UIControlStateNormal];
        [[NSUserDefaults standardUserDefaults] setObject:token forKey:@"kUDTestToken"];
    } else {
        [self.loginBtn setTitle:@"登录（您还未登录）" forState:UIControlStateNormal];
        [[OOPNetworkManager sharedManager] setCommonHeaders:@{
            @"x-jnos-token-c": @""
        }];
        [[OOPAdvancedNetworkManager sharedManager] setCommonHeaders:@{
            @"x-jnos-token-c": @""
        }];
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"kUDTestToken"];
    }
}

#pragma mark - <UITextFieldDelegate>
- (void)textFieldDidEndEditing:(UITextField *)textField {
    [[NSUserDefaults standardUserDefaults] setObject:textField.text forKey:@"kUDTestToken"];
    if (textField.text.length == 0) {
        [self.loginBtn setTitle:@"登录（您还未登录）" forState:UIControlStateNormal];
        [[OOPNetworkManager sharedManager] setCommonHeaders:@{
            @"x-jnos-token-c": @""
        }];
        [[OOPAdvancedNetworkManager sharedManager] setCommonHeaders:@{
            @"x-jnos-token-c": @""
        }];
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}

@end
