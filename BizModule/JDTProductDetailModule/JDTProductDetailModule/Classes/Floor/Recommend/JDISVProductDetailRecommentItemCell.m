//
//  JDISVProductDetailRecommentItemCell.m
//  JDISVProductDetailSDKModule
//
//  Created by 罗静 on 2021/12/22.
//

#import "JDISVProductDetailRecommentItemCell.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVKAUIKitModule/KAPriceLabel.h>
#import <JDISVKAUIKitModule/KALabel.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import "UIImage+JDISVPDImage.h"
#import "JDISVProductDetailRecommentItemModel.h"
#import "PDRecommendModel.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

@interface JDISVProductDetailRecommentItemCell ()

@property (nonatomic, strong) UIView *customContentView;

@property (nonatomic, strong) UIImageView *coverImageView;

@property (nonatomic, strong) UILabel *nameLabel;

@property (nonatomic, strong) KAPriceLabel *priceLabel;

@property (nonatomic, strong) KAPriceLabel *originPriceLabel;

@property (nonatomic, strong) UIButton *addCartBtn;

//@property (nonatomic, strong) JDISVProductDetailRecommentItemModel *model;
@property (nonatomic, strong) PDRecommendItemModel *model;

//@property (nonatomic, strong) KALabel *seckillTag; // 秒杀标签
//@property (nonatomic, strong) KALabel *zjMarkTag; // 直降标签

//@property (nonatomic, strong) KALabel *couponsMarkTag; // 优惠券标签
//@property (nonatomic, strong) KALabel *mitemjiajiagouTag; //换购标签
//@property (nonatomic, strong) KALabel *fullminusTag; //满减标签
//@property (nonatomic, strong) KALabel *buygift; //满减标签

@property (nonatomic, strong) KALabel *presaleTag; //预售标签
@property (nonatomic, strong) JDISVPriceSigPic * priceSig;
//@property (nonatomic, strong) UIView *promotionContentView; //砍价、拼团、秒杀标签
//@property (nonatomic, strong) UIImageView *promotionTagImgView;
//@property (nonatomic, strong) UILabel *promotionTag;

@property (nonatomic, strong) UIStackView *tagStackView;

@end

@implementation JDISVProductDetailRecommentItemCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self){
        
        [self.contentView addSubview:self.customContentView];
        [self.customContentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(self.contentView);
        }];
        
        [self.customContentView addSubview:self.coverImageView];
        [self.coverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.trailing.mas_equalTo(0);
            make.height.mas_equalTo(self.coverImageView.mas_width).multipliedBy(1);
        }];
        
        [self.customContentView addSubview:self.presaleTag];
        [self.presaleTag mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.bottom.mas_equalTo(self.coverImageView).mas_offset(0);;
            make.height.mas_equalTo(24);
        }];
        
        [self.customContentView addSubview:self.nameLabel];
        [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(12);
            make.top.mas_equalTo(self.coverImageView.mas_bottom).mas_offset(6);
            make.trailing.mas_equalTo(-12);
            make.height.mas_equalTo(39);
        }];
        
        [self.customContentView addSubview:self.tagStackView];
        [self.tagStackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(12);
            make.top.mas_equalTo(self.nameLabel.mas_bottom).mas_offset(6);
            make.height.mas_equalTo(14);
        }];
        
        [self.customContentView addSubview:self.priceLabel];
        [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(12);
            make.top.mas_equalTo(self.tagStackView.mas_bottom).mas_offset(6);
            make.height.mas_equalTo(18);
        }];
        
        [self.customContentView addSubview:self.originPriceLabel];
        [self.originPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(12);
            make.top.mas_equalTo(self.priceLabel.mas_bottom).mas_offset(0);
            make.height.mas_equalTo(15);
        }];
        
        [self.customContentView addSubview:self.addCartBtn];
        [self.addCartBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(-15);
            make.top.mas_equalTo(self.priceLabel.mas_top).mas_offset(0);
            make.width.height.mas_equalTo(34);
        }];
        
//        [self.couponsMarkTag mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.width.mas_equalTo(42);
//            make.height.mas_equalTo(14);
//        }];
//
//        [self.mitemjiajiagouTag mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.width.mas_equalTo(32);
//            make.height.mas_equalTo(14);
//        }];
//
//        [self.fullminusTag mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.width.mas_equalTo(32);
//            make.height.mas_equalTo(14);
//        }];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(itemClicked)];
        [self.customContentView addGestureRecognizer:tap];
    }
    return self;
}

- (void)itemClicked {
    if (self.clickedBlock) {
        self.clickedBlock(self.model.skuId);
    }
}

- (void)addCartClick{
    if (self.AddCartClickedBlock) {
        self.AddCartClickedBlock(self.model.skuId);
    }
}

- (void)configModel:(PDRecommendItemModel *)model {
    
    self.model = model;
    
    UIImage *placeHolder = [PlatformService getDefaultPlaceholderImage:JDISVWebImagePlaceHolderTypeSmall];
    
    NSString *imageUrl = [PlatformService getCompleteImageUrl:model.imageUrl moduleType:JDISVModuleTypeProductDetail];
    imageUrl = [imageUrl stringByReplacingOccurrencesOfString:@".sa/da/" withString:@".sa/da/s560x560_"];
    
    [self.coverImageView jdcd_setImage:imageUrl placeHolder:placeHolder contentMode:UIViewContentModeScaleAspectFit];
    
    self.nameLabel.text = model.skuName;
    
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    [self.priceLabel configTextWithPrice:model.salePrice.floatValue type:KAPriceTypeP3 color:color];

    if (model.originalPrice && model.originalPrice.length) {
        NSMutableAttributedString *totolPrice = [[NSMutableAttributedString alloc] init];
        [totolPrice KA_renderWithMiddleLinePrice:model.originalPrice.floatValue];
        self.originPriceLabel.attributedText = totolPrice;
    } else {
        self.originPriceLabel.text = nil;
    }
    
    /**
         * virtualsuite， 虚拟组套
         * fullminus，满减
         * mitemjiajiagou，加价购
         * mspd，秒杀
         * slash，砍价
         * ispt，拼团
         * presale，预售
         * coupon，优惠券
         * nosinglebuy, 商品单独销售
     
         */
    
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    CGFloat maxWidth = UIScreen.mainScreen.bounds.size.width/2 -w3-12;
    CGFloat stackW = 0;
    for (UIView* subView in self.tagStackView.arrangedSubviews){
        [self.tagStackView removeArrangedSubview:subView];
        [subView removeFromSuperview];
    }
    
    BOOL ismspd = NO,ispresale = NO,isslash = NO,isispt = NO;
    //wt_todo_for_test
//    self.model.content.tags = @[@"fullminus",@"coupon",@"buygift",@"mitemjiajiagou"];
//    for (NSString *str in self.model.content.tags) {
//        if ([str isEqualToString:@"fullminus"]) {
//            if (KSAAPP && self.tagStackView.arrangedSubviews.count > 0) {
//                continue;//KSA只展示第一个
//            }
//            KALabel* label = [KALabel buttonWithType:UIButtonTypeCustom];
//            
//            CGSize  size = [label renderASCouponTag:ProductDetailSDKL(@"base_pd_tag_mjMark")];
//            if(stackW + size.width <= maxWidth){
//                stackW += size.width;
//                [self.tagStackView addArrangedSubview:label];
//                [label mas_makeConstraints:^(MASConstraintMaker *make) {
//                    make.width.mas_equalTo(size.width);
//                    make.height.mas_equalTo(size.height);
//                }];
//            }
//        } else if ([str isEqualToString:@"mitemjiajiagou"]) {
//            if (KSAAPP && self.tagStackView.arrangedSubviews.count > 0) {
//                continue;//KSA只展示第一个
//            }
//            KALabel* label = [KALabel buttonWithType:UIButtonTypeCustom];
//            CGSize  size = [label renderASCouponTag:ProductDetailSDKL(@"base_pd_tag_addby")];
//            if(stackW + size.width <= maxWidth){
//                stackW += size.width;
//                [self.tagStackView addArrangedSubview:label];
//                [label mas_makeConstraints:^(MASConstraintMaker *make) {
//                    make.width.mas_equalTo(size.width);
//                    make.height.mas_equalTo(size.height);
//                }];
//            }
//        } else if ([str isEqualToString:@"mspd"]) {
//            ismspd = YES;
//        } else if ([str isEqualToString:@"slash"]) {
//            isslash = YES;
//        } else if ([str isEqualToString:@"ispt"]) {
//            isispt = YES;
//        } else if ([str isEqualToString:@"presale"]) {
//            ispresale = YES;
//        } else if ([str isEqualToString:@"coupon"]) {
//            if (KSAAPP && self.tagStackView.arrangedSubviews.count > 0) {
//                continue;//KSA只展示第一个
//            }
//            KALabel* label = [KALabel buttonWithType:UIButtonTypeCustom];
//            CGSize  size = [label renderASCouponTag:ProductDetailSDKL(@"base_pd_tag_coupon")];
//            if(stackW + size.width <= maxWidth){
//                stackW += size.width;
//                [self.tagStackView addArrangedSubview:label];
//                [label mas_makeConstraints:^(MASConstraintMaker *make) {
//                    make.width.mas_equalTo(size.width);
//                    make.height.mas_equalTo(size.height);
//                }];
//            }
//        } else if([str isEqualToString:@"buygift"]) {
//            if (KSAAPP && self.tagStackView.arrangedSubviews.count > 0) {
//                continue;//KSA只展示第一个
//            }
//            KALabel* label = [KALabel buttonWithType:UIButtonTypeCustom];
//            CGSize  size = [label renderASCouponTag:ProductDetailSDKL(@"base_pd_tag_buygift")];
//            if(stackW + size.width <= maxWidth){
//                stackW += size.width;
//                [self.tagStackView addArrangedSubview:label];
//                [label mas_makeConstraints:^(MASConstraintMaker *make) {
//                    make.width.mas_equalTo(size.width);
//                    make.height.mas_equalTo(size.height);
//                }];
//            }
//        }
//    }
   
    self.tagStackView.frame = CGRectMake(12, self.bounds.size.height-14-3, stackW, 13);

    [self.priceSig removeFromSuperview];
    self.priceSig = nil;
    self.addCartBtn.hidden = NO;
    if (isispt) {
        self.addCartBtn.hidden = YES;//砍价拼团预售商品不显示加车
        self.priceSig = [JDISVPriceSigPic  SigPicWithTypeKSA:JDISVPriceSigSecCollage];
    } else if (ismspd) {
        self.priceSig = [JDISVPriceSigPic  SigPicWithTypeKSA:JDISVPriceSigSecKill];
    } else if (isslash) {
        self.addCartBtn.hidden = YES;//砍价拼团预售商品不显示加车
        self.priceSig = [JDISVPriceSigPic  SigPicWithTypeKSA:JDISVPriceSigSecSlash];
    }
    if(self.priceSig){
        self.originPriceLabel.hidden = YES;//有营销标就不展示划线价
        [self.contentView addSubview:self.priceSig];
        [self.priceSig mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.priceLabel).mas_offset(0);
            make.top.mas_equalTo(self.priceLabel.mas_bottom).mas_offset(2);
            make.width.mas_equalTo(self.priceSig.sigWidth);
            make.height.mas_equalTo(SigHeightKSA);
        }];
        [self.priceSig makeMasKSA];
    }else{
        self.originPriceLabel.hidden = NO;
    }
    if ([model.originalPrice isEqualToString:model.salePrice]) {
        // 原价和划线价相同时，不显示划线价
        self.originPriceLabel.hidden = YES;
    } else {
        self.originPriceLabel.hidden = NO;
    }
    if (ispresale) {
        self.addCartBtn.hidden = YES;//砍价拼团预售商品不显示加车
        self.presaleTag.hidden = NO;
    } else {
        self.presaleTag.hidden = YES;
    }
    
    // 轻量版不展示折扣率
//    [self.coverImageView ksa_setPriceOff:self.model.discountRate.stringValue];
}

#pragma mark - getter
- (UIView *)customContentView {
    if (!_customContentView) {
        _customContentView = [[UIView alloc] init];
        _customContentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        CGFloat r1 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R1"];
        _customContentView.layer.cornerRadius = 12;
        _customContentView.layer.masksToBounds = YES;
    }
    return _customContentView;
}
- (UIImageView *)coverImageView {
    if (!_coverImageView) {
        _coverImageView  = [[UIImageView alloc] init];
    }
    return _coverImageView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _nameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _nameLabel.numberOfLines = 2;
        _nameLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _nameLabel;
}

- (KAPriceLabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[KAPriceLabel alloc] init];
    }
    return _priceLabel;
}

- (KAPriceLabel *)originPriceLabel {
    if (!_originPriceLabel) {
        _originPriceLabel = [[KAPriceLabel alloc] init];
    }
    return _originPriceLabel;
}

-(UIButton *)addCartBtn{
    if (!_addCartBtn) {
        _addCartBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        UIImage *img2 = [UIImage ka_iconWithName:JDIF_ICON_CART_LINE imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
        [_addCartBtn setImage:img2 forState:UIControlStateNormal];
        _addCartBtn.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        _addCartBtn.layer.cornerRadius = 17.0;
        [_addCartBtn addTarget:self action:@selector(addCartClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _addCartBtn;
}

//- (KALabel *)seckillTag {
//    if (!_seckillTag) {
//        _seckillTag = [KALabel kaLabelWithType:KALabelTypeL3 title:ProductDetailSDKL(@"pd_base_floor_label_superdeal") cornerRadius:3.f];
//    }
//    return _seckillTag;
//}

//- (KALabel *)zjMarkTag {
//    if (!_zjMarkTag) {
//        _zjMarkTag = [KALabel kaLabelWithType:KALabelTypeL3 title:ProductDetailSDKL(@"base_pd_tag_promo_discount") cornerRadius:3.f];
//    }
//    return _zjMarkTag;
//}

//- (KALabel *)couponsMarkTag {
//    if (!_couponsMarkTag) {
//        _couponsMarkTag = [KALabel kaLabelWithType:KALabelTypeL3 title:ProductDetailSDKL(@"pd_base_floor_label_coupon") cornerRadius:3.f];
//        _couponsMarkTag.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9" alpha:0.2];
//    }
//    return _couponsMarkTag;
//}
//
//- (KALabel *)mitemjiajiagouTag {
//    if (!_mitemjiajiagouTag) {
//        _mitemjiajiagouTag = [KALabel kaLabelWithType:KALabelTypeL3 title:ProductDetailSDKL(@"base_pd_tag_promo_additional") cornerRadius:3.f];
//        _mitemjiajiagouTag.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9" alpha:0.2];
//    }
//    return _mitemjiajiagouTag;
//}
//
//- (KALabel *)fullminusTag {
//    if (!_fullminusTag) {
//        _fullminusTag = [KALabel kaLabelWithType:KALabelTypeL3 title:ProductDetailSDKL(@"base_pd_tag_mjMark") cornerRadius:3.f];
//        _fullminusTag.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9" alpha:0.2];
//    }
//    return _fullminusTag;
//}

- (KALabel *)presaleTag {
    if (!_presaleTag) {
        _presaleTag = [KALabel kaLabelWithType:KALabelTypeL3 title:ProductDetailSDKL(@"pd_base_floor_label_presale") cornerRadius:0.f];
        _presaleTag.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9" alpha:0.6];
        [_presaleTag setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _presaleTag.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    }
    return _presaleTag;
}

- (UIStackView *)tagStackView {
    if (!_tagStackView) {
        _tagStackView = [[UIStackView alloc] init];
        _tagStackView.axis = UILayoutConstraintAxisHorizontal;
        _tagStackView.spacing = 4.f;
    }
    return _tagStackView;
}

@end

