//
//  JDISVPDReviewFloor.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2022/2/18.
//

#import "JDISVPDReviewFloor.h"
#import "JDISVPDPFlowLayout.h"
#import "BQPDPReviewTagCollectionViewCell.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import "UIImage+JDISVPDImage.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import "JDISVPDDReviewPopupViewController.h"
#import "JDISVPDDStarView.h"
#import "JDISVPDReviewFloorModule.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

@interface JDISVPDReviewFloor ()<UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (weak, nonatomic) IBOutlet UIView *container;
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet UILabel *goodReputationRating;
@property (weak, nonatomic) IBOutlet UIImageView *moreImageView;
@property (weak, nonatomic) IBOutlet UIImageView *commentatorImageView;
@property (weak, nonatomic) IBOutlet UILabel *commentatorName;
@property (weak, nonatomic) IBOutlet UILabel *ratingLabel;
@property (weak, nonatomic) IBOutlet UILabel *dateLabel;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UILabel *contentTextLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentTextLabelHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentTextLabelBottomLayout;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentTextLabel_collectionviewSpace;
@property (weak, nonatomic) IBOutlet UIView *foldView;
@property (weak, nonatomic) IBOutlet UILabel *foldLabel;
@property (weak, nonatomic) IBOutlet UIImageView *foldArrow;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *avatarTopLayout; // 商详页61 评论18
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *avatarLeading;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *dateTrailing;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *containerLeading;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *containerTop;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *containerTrailing;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *containerBottom;

@property (nonatomic, strong) JDISVPDDStarView *starView;

@property (nonatomic, strong) JDISVPDReviewFloorModule *viewModel;
@property (nonatomic, strong) BQPDPReviewItemViewModel *review;

@end

@implementation JDISVPDReviewFloor

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    self.titleLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.moreImageView.contentMode = UIViewContentModeCenter;
    self.moreImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.goodReputationRating.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    self.goodReputationRating.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    self.commentatorImageView.layer.cornerRadius = 20;
    self.commentatorName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.ratingLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    self.ratingLabel.text = ProductDetailSDKL(@"pd_base_floor_comment_point");
    self.dateLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    self.dateLabel.font = [UIFont jdcd_lightJDFontWithSize:12];
    
    JDISVPDPFlowLayout *layout = [[JDISVPDPFlowLayout alloc] init];
    layout.minimumLineSpacing = 12;
    layout.minimumInteritemSpacing = 12;
    layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    [self.collectionView setCollectionViewLayout:layout animated:NO];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    [self.collectionView registerClass:[BQPDPReviewTagCollectionViewCell class] forCellWithReuseIdentifier:@"BQPDPReviewTagCollectionViewCell"];
    
//    self.commentatorImageView.image = [UIImage isv_pd_imageNamed:@"isv_pd_review_avator_ph"];
    
    self.foldLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    self.foldLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    self.foldView.hidden = YES;
    @weakify(self)
    [self.foldView jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
        @strongify(self)
        self.viewModel.isFold = !self.viewModel.isFold;
        JDCDISVAction *action = [JDCDISVAction actionWithType:kJDCDISVFloorUpdateViewAction];
        [self isv_sendAction:action];
    }];
    
    JDISVPDDStarView *starView = [[JDISVPDDStarView alloc] initWithFrame:CGRectMake(0, 0, 58, 10)];
    starView.userInteractionEnabled = NO;
    starView.starImage = [[UIImage isv_pd_imageNamed:@"isv_pd_review_star_shine"] jdcd_imageWithTintColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    starView.emptyImage = [UIImage isv_pd_imageNamed:@"isv_pd_review_star_gray"];
    starView.spacing = 2;
    [self.container addSubview:starView];
    [starView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(58);
        make.height.mas_equalTo(10);
        make.centerY.equalTo(self.ratingLabel);
        make.leading.equalTo(self.ratingLabel.mas_trailing).offset(4);
    }];
    self.starView = starView;
    
    self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    self.collectionView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)configReviewItem:(BQPDPReviewItemViewModel *)review {
    self.review = review;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel{
    self.viewModel = floorModel;
    self.review = self.viewModel.review;

    if (self.review == nil) {
        self.titleLabel.text = ProductDetailSDKL(@"base_pd_comment_empty");
        self.goodReputationRating.hidden = YES;
        return;
    } else {
        self.titleLabel.text = [NSString stringWithFormat:ProductDetailSDKL(@"pd_base_floor_comment_title"), self.viewModel.commentCount.intValue];
        self.goodReputationRating.hidden = NO;
    }
    if (self.viewModel.goodReputationRating) {
        self.goodReputationRating.text = [NSString stringWithFormat:ProductDetailSDKL(@"pd_base_floor_comment_applause_rate"), round(self.viewModel.goodReputationRating.doubleValue)];
    } else {
        self.goodReputationRating.text = @"";
    }

    [self.commentatorImageView jdcd_setImage:self.review.imageUrl placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderAvatar] contentMode:(UIViewContentModeScaleAspectFit)];
    self.commentatorName.text = self.review.name;
    self.dateLabel.text = self.review.dateString;
    self.collectionView.hidden = self.review.tags.count == 0;
    self.starView.rating = self.review.rating.floatValue;

    if (!self.viewModel) {
        // UI
//        self.backgroundColor = [UIColor clearColor];
//        self.contentView.backgroundColor = [UIColor clearColor];
//        self.container.backgroundColor = [UIColor whiteColor];
        self.titleLabel.hidden = YES;
        self.goodReputationRating.hidden = YES;
        self.moreImageView.hidden = YES;
        self.avatarTopLayout.constant = 18;
        self.avatarLeading.constant = 12;
        self.dateTrailing.constant = 12;
        self.containerLeading.constant = 12;
        self.containerTrailing.constant = 12;
        self.containerTop.constant = 6;
        self.containerBottom.constant = 6;
//        self.container.layer.masksToBounds = YES;
//        self.container.layer.cornerRadius = 8;
    }
    
    [self.collectionView reloadData];

    if (self.review.contentAttributedStr) {
        self.contentTextLabel.hidden = NO;
        self.contentTextLabel.attributedText = self.review.contentAttributedStr;
        self.contentTextLabel.lineBreakMode = NSLineBreakByTruncatingTail;
        self.contentTextLabel.numberOfLines = 0;
        self.contentTextLabelHeight.constant = self.review.contentTextLabelNormalHeight;
        self.contentTextLabelBottomLayout.constant = 18;
        self.contentTextLabel_collectionviewSpace.constant = 12;
        if (self.viewModel.isShowFoldView) {
            self.foldView.hidden = NO;
            self.contentTextLabelBottomLayout.constant = 4 + 17 + 18;
            if (self.viewModel.isFold) {
                self.contentTextLabelHeight.constant = self.review.contentTextLabelFoldHeight;
                self.foldLabel.text = ProductDetailSDKL(@"pd_base_floor_pack_up");
                self.foldArrow.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_UP_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
            } else {
                self.contentTextLabelHeight.constant = self.review.contentTextLabelNormalHeight;
                self.foldLabel.text = ProductDetailSDKL(@"pd_base_floor_expansion");
                self.foldArrow.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_DOWN_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
            }
        }
    } else {
        self.contentTextLabel.hidden = YES;
        self.contentTextLabel.text = nil;
        self.contentTextLabelHeight.constant = 0;
        self.contentTextLabelBottomLayout.constant = 18;
        self.contentTextLabel_collectionviewSpace.constant = 0;
        self.foldView.hidden = YES;
    }
}

#pragma mark - delegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.review.tags.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    BQPDPReviewTagCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"BQPDPReviewTagCollectionViewCell" forIndexPath:indexPath];
    cell.itemTitle.text = self.review.tags[indexPath.item];
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSString *size = self.review.tagSizeArray[indexPath.item];
    return CGSizeFromString(size);
}

#pragma mark - event
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]) {


//        JDISVPDDReviewPopupViewController *vc = [JDISVPDDReviewPopupViewController new];
//        vc.skuId = self.viewModel.skuId;
//        vc.rightText = [NSString stringWithFormat:ProductDetailSDKL(@"pd_base_floor_comment_applause_rate"), round(self.viewModel.goodReputationRating.doubleValue)];
//        [controller.navigationController pushViewController:vc animated:YES];
        // TODO:Juice  评价改为 H5 实现
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:@"评价页改为 H5, 尚未实现"];
        
        return YES;
    }
    return NO;
}

@end
