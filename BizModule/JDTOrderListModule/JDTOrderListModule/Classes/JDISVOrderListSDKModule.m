//
//  JDISVOrderListSDKModule.m
//
//
// 组件输出类, 可引入JDRouter组件, 进行组件间通信

#import <Foundation/Foundation.h>
#import "JDISVOrderListSDKModule.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

#import "JDISVOrderListBaseMainController.h"
#import "JDISVOrderListBaseMainViewModel.h"
#import "JDISVOrderListBaseListViewModel.h"

#import "JDISVOrderListMainViewController.h"
#import "JDISVOrderListMainViewModel.h"
#import "ORDERTrackController.h"

@implementation JDISVOrderListSDKModule

JDROUTER_EXTERN_METHOD(JDISVOrderListSDKModule, getTestContainerController, arg, callback) {
    NSArray *orderListTypes = @[
        [JDISVOrderListTypeModel orderListTypeOf:0 categoryTitle:OrderListL(@"isv_order_tab_title_all_order")],
        [JDISVOrderListTypeModel orderListTypeOf:1 categoryTitle:OrderListL(@"isv_order_tab_title_wait_to_pay")],
        [JDISVOrderListTypeModel orderListTypeOf:2 categoryTitle:OrderListL(@"isv_order_tab_title_wait_receive")],
        [JDISVOrderListTypeModel orderListTypeOf:3 categoryTitle:OrderListL(@"isv_order_tab_title_finished")],
        [JDISVOrderListTypeModel orderListTypeOf:4 categoryTitle:OrderListL(@"isv_order_tab_title_cancelled")],
    
    ];
    JDISVOrderListBaseMainViewModel *viewModel = [[JDISVOrderListBaseMainViewModel alloc] initWithOrderListTypeModels:orderListTypes subViewModelClassName:nil];
    viewModel.categoryViewModel.heightOfCategoryView = 50.f;
    JDISVOrderListBaseMainController *vc = [[JDISVOrderListBaseMainController alloc] initWith:viewModel];
    vc.title = viewModel.orderListMainControllerTitle;
    vc.hidesBottomBarWhenPushed = YES;
    return vc;
}

// 0 全部订单  1 待支付订单 2 待收货订单  3已完成订单 4已取消订单
JDROUTER_EXTERN_METHOD(JDISVOrderListSDKModule, orderListMainController, arg, callback) {
#if DEBUG
//    NSDictionary *config = [JDISV_RESOURCE_MANAGER dictionaryForResourceType:JDISVResourceTypeOrderListConfigGeojson error:nil];
//    NSArray *tabs = [config objectForKey:@"tabs"];
    NSArray *tabs = @[
      @{
        @"state" : @(0),
        @"title" : OrderListL(@"isv_order_tab_title_all_order"),
        @"type" : @(0) // 全部
      },
      @{
          @"state" : @(1),
          @"title" : OrderListL(@"isv_order_tab_title_wait_to_pay"),
          @"type" : @(1) // 待支付
      },
      @{
          @"state" : @(15),
          @"title" : @"待发货",
          @"type" : @(15) // 待发货
      },
      @{
          @"state" : @(20),
          @"title" : OrderListL(@"isv_order_tab_title_wait_receive"),
          @"type" : @(2) // 待收货
      },
      @{
          @"state" : @(-1),
          @"title" : OrderListL(@"isv_order_tab_title_cancelled"),
          @"type" : @(4) // 已取消
      },
      @{
          @"state" : @(30),
          @"title" : OrderListL(@"isv_order_tab_title_finished"),
          @"type" : @(3) // 已完成
      }
    ];
    NSMutableArray *array = [NSMutableArray array];
    NSNumber *listType = [arg objectForKey:@"order_list_type"];
    NSUInteger idx = 0;
    for (int i = 0; i<tabs.count; i++) {
        NSDictionary *tab = tabs[i];
        NSNumber *type = tab[@"type"];
        if (listType) {
            if ([listType isEqual:type]) {
                idx = i;
            }
        } else {
            if ([type integerValue] == 0) {
                idx = i;
            }
        }
        JDISVOrderListTypeModel *model = [JDISVOrderListTypeModel orderListTypeOf:[tab[@"state"] integerValue] categoryTitle:tab[@"title"]];
        [array addObject:model];
    }
    
    NSArray *orderListTypes = [array copy];
    JDISVOrderListMainViewModel *viewModel = [[JDISVOrderListMainViewModel alloc] initWithOrderListTypeModels:orderListTypes subViewModelClassName:@"JDISVOrderListSubViewModel"];
    viewModel.currentSelectedIndex = idx;
    viewModel.categoryViewModel.heightOfCategoryView = 50.f;
    JDISVOrderListMainViewController *vc = [[JDISVOrderListMainViewController alloc] initWith:viewModel];
    vc.title = viewModel.orderListMainControllerTitle;
    vc.shouldHiddenNavigationBar = [arg[@"shouldHiddenNavigationBar"] boolValue];
    vc.hidesBottomBarWhenPushed = YES;
    if (callback) {
        callback(vc);
    }
#else
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeLogin)];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
    [JDRouter openURL:router arg:nil error:nil completion:^(id  _Nullable object) {
//        NSDictionary *config = [JDISV_RESOURCE_MANAGER dictionaryForResourceType:JDISVResourceTypeOrderListConfigGeojson error:nil];
//        NSArray *tabs = [config objectForKey:@"tabs"];
        NSArray *tabs = @[
            @{
              @"state" : @(4096),
              @"title" : OrderListL(@"isv_order_tab_title_all_order"),
              @"type" : @(0)
            },
            @{
                @"state" : @(1),
                @"title" : OrderListL(@"isv_order_tab_title_wait_to_pay"),
                @"type" : @(1)
            },
            @{
                @"state" : @(128),
                @"title" : OrderListL(@"isv_order_tab_title_wait_receive"),
                @"type" : @(2)
            },
            @{
                @"state" : @(-1),
                @"title" : OrderListL(@"isv_order_tab_title_cancelled"),
                @"type" : @(4)
            },
            @{
                @"state" : @(1024),
                @"title" : OrderListL(@"isv_order_tab_title_finished"),
                @"type" : @(3)
            }
          ];
        NSMutableArray *array = [NSMutableArray array];
        NSNumber *listType = [arg objectForKey:@"order_list_type"];
        NSUInteger idx = 0;
        for (int i = 0; i<tabs.count; i++) {
            NSDictionary *tab = tabs[i];
            NSNumber *type = tab[@"type"];
            if (listType) {
                if ([listType isEqual:type]) {
                    idx = i;
                }
            } else {
                if ([type integerValue] == 0) {
                    idx = i;
                }
            }
            JDISVOrderListTypeModel *model = [JDISVOrderListTypeModel orderListTypeOf:[tab[@"state"] integerValue] categoryTitle:tab[@"title"]];
            [array addObject:model];
        }
        
        NSArray *orderListTypes = [array copy];
        JDISVOrderListMainViewModel *viewModel = [[JDISVOrderListMainViewModel alloc] initWithOrderListTypeModels:orderListTypes subViewModelClassName:@"JDISVOrderListSubViewModel"];
        viewModel.currentSelectedIndex = idx;
        viewModel.categoryViewModel.heightOfCategoryView = 50.f;
        JDISVOrderListMainViewController *vc = [[JDISVOrderListMainViewController alloc] initWith:viewModel];
        vc.title = viewModel.orderListMainControllerTitle;
        vc.shouldHiddenNavigationBar = [arg[@"shouldHiddenNavigationBar"] boolValue];
        vc.hidesBottomBarWhenPushed = YES;
        if (callback) {
            callback(vc);
        }
    }];
#endif
    return nil;
}

//获取订单列表类名
JDROUTER_EXTERN_METHOD(JDISVOrderListSDKModule, getOrderListVCClassName, arg, callback) {
    NSString *strClass = NSStringFromClass([JDISVOrderListMainViewController class]);
    return strClass;
}

JDROUTER_EXTERN_METHOD(JDISVOrderListSDKModule, getOrderTrackViewController, arg, callback) {
    ORDERTrackContentType type = [[arg objectForKey:@"contentType"] integerValue];
    NSArray *trackDataArr = [arg objectForKey:@"trackDataList"];
    NSArray *progressDataArr = [arg objectForKey:@"progressDataList"];
    ORDERTrackController *vc = [[ORDERTrackController alloc] initWithType:type];
    if (trackDataArr.count > 0) {
        vc.shipmentDataArr = trackDataArr;
    }
    if (progressDataArr.count > 0) {
        vc.progressDataArr = progressDataArr;
        vc.orderId = [arg objectForKey:@"orderId"];
    }
    return vc;
}

@end
