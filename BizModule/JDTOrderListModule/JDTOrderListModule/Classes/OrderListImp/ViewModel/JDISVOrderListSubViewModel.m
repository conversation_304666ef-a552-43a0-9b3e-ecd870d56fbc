//
//  JDISVOrderListSubViewModel.m
//  JDISVOrderListSDKModule
//
//  Created by gongyang2 on 2021/10/15.
//

#import "JDISVOrderListSubViewModel.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorConfigManager.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import "JDISVOrderListMacro.h"

#import "JDISVOrderListCardViewModel.h"
#import "JDISVOrderListCellViewModel.h"

#import "JDISVOrderListResModel.h"
#import "JDISVOrderListBaseModel.h"
#import "JDISVCommonTools.h"
@import JDTCommonToolModule;
@import JDTInfrastructureModule;

@interface JDISVOrderListSubViewModel ()
@property (nonatomic, assign) NSTimeInterval startTimeMTA;
@property (nonatomic, assign) NSTimeInterval endTimeMTA;
@end

@implementation JDISVOrderListSubViewModel

- (instancetype)initWithOrderListType:(NSInteger)orderListType
{
    self = [super initWithOrderListType:orderListType];
    if (self) {
        self.orderListType = orderListType;
        self.originDataArray = [NSMutableArray array];
    }
    return self;
}

- (void)resetData {
    [super resetData];
    
    [self.originDataArray removeAllObjects];
}

- (NSURLSessionDataTask *)requestOrderListDataWithOrderType:(NSInteger)orderListType page:(NSNumber *)page pageSize:(NSNumber *)pageSize success:(void (^)(__kindof JDISVOrderListBaseModel * _Nullable))successBlock failure:(void (^)(NSError * _Nonnull))failureBlock {
    
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    
    [param setObject:@(orderListType).stringValue forKey:@"state"];
    [param setObject:pageSize.stringValue forKey:@"size"];
    [param setObject:page.stringValue forKey:@"page"];
    
    [param setObject:@"cn_ybxt_b2c" forKey:@"businessTag"];
    [param setObject:@"405" forKey:@"buId"];
    [param setObject:@"1" forKey:@"businessOrderType"];
    
    if (self.currentSelectedOrderKindType == 2) {
        //本地生活订单种类
        [param setObject:@[@(75)] forKey:@"typeList"];
    }
    
    self.startTimeMTA = [[NSDate date] timeIntervalSince1970];
    NSURLSessionDataTask *task = [PlatformService request:(JDCDHTTPSessionRequestTypeGet) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"orderList" function:@"" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        //        responseObject = [JDISVOrderListSubViewModel readLocalFileWithName:@"mockOrderGroupBuy"];
        
        JDISVOrderListResModel *res = [JDISVOrderListResModel yy_modelWithDictionary:responseObject];
        if (res.success) {
            JDISVOrderListBaseModel *model = [JDISVOrderListBaseModel new];
            model.orderArray = res.value.orderInfoDTOS;
            model.totalCount = res.value.totalCount;
            successBlock(model);
            //接口加载时长埋点
            self.endTimeMTA = [[NSDate date] timeIntervalSince1970];
            NSTimeInterval diffT = self.endTimeMTA-self.startTimeMTA;
            NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
            NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
            [JDRouter openURL:url arg:@{@"name":@"OrderListPageLoadPerf",@"param":@{@"time":@(diffT)}} error:nil completion:nil];
        } else {
            failureBlock(error ?: [NSError errorWithDomain:@"OrderList" code:0 userInfo:nil]);
        }
    }];
    
    return task;
}

+ (NSDictionary *)readLocalFileWithName:(NSString *)name {
    // 获取文件路径
    NSString *path = [[NSBundle mainBundle] pathForResource:name ofType:@"json"];
    // 将文件数据化
    NSData *data = [[NSData alloc] initWithContentsOfFile:path];
    // 对数据进行JSON格式化并返回字典形式
    return [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
}

//- (void)requestOrderListPageDataSuccessProcessWithOrderModel:(__kindof JDISVOrderListBaseModel *)model subscriber:(id<RACSubscriber>)subscriber {
//    
//    [self processOrderModel:model];
//    
//    [subscriber sendNext:nil];
//}

- (void)requestOrderListPageDataSuccessProcessWithOrderListModel:(__kindof ORDERListModel * )model subscriber:(id<RACSubscriber>  )subscriber {
    [self processOrderListModel:model];
    [subscriber sendNext:nil];
}

- (void)processOrderListModel:(ORDERListModel *)model {
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:KaOrderListPageId];
    NSMutableArray *temp = [NSMutableArray array];
    CGFloat w1 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    for (ORDERListItemModel *orderItem in model.orderList) {
        JDISVOrderListCardViewModel *vm = [self cardViewModelFromOrderInfo2:orderItem config:config];
        JDISVOrderListCellViewModel *cellvm = [JDISVOrderListCellViewModel new];
        cellvm.height = [vm height] + w1;
        cellvm.cellIdentifier = kCardCell;
        cellvm.model = vm;
        cellvm.orderListItem = orderItem;
        [temp addObject:cellvm];
    }
    [self.originDataArray addObjectsFromArray:temp];
    
    [temp removeAllObjects];
    CGFloat w2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    for (JDISVOrderListCellViewModel *vm in self.originDataArray) {
        [temp addObject:vm];
        if (![vm isEqual:self.originDataArray.lastObject]) {
            JDISVOrderListBaseCellViewModel *space = [JDISVOrderListBaseCellViewModel new];
            space.cellIdentifier = kEmptyCell;
            space.height = w2;
            [temp addObject:space];
        }
    }
    self.cellViewModels = temp;
    
    self.noMoreData = self.originDataArray.count >= model.totalNum;
}

//- (void)processOrderModel:(__kindof JDISVOrderListBaseModel *)model {
//    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:KaOrderListPageId];
//    
//    NSMutableArray *temp = [NSMutableArray array];
//    CGFloat w1 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
//    for (JDISVOrderListOrderInfoDTO *order in model.orderArray) {
//        JDISVOrderListCardViewModel *vm = [self cardViewModelFromOrderInfo:order config:config];
//        JDISVOrderListCellViewModel *cellvm = [JDISVOrderListCellViewModel new];
//        cellvm.height = [vm height] + w1;
//        cellvm.cellIdentifier = kCardCell;
//        cellvm.model = vm;
////        cellvm.order = order;
//        [temp addObject:cellvm];
//    }
//    [self.originDataArray addObjectsFromArray:temp];
//    
//    [temp removeAllObjects];
//    CGFloat w2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
//    for (JDISVOrderListCellViewModel *vm in self.originDataArray) {
//        [temp addObject:vm];
//        if (![vm isEqual:self.originDataArray.lastObject]) {
//            JDISVOrderListBaseCellViewModel *space = [JDISVOrderListBaseCellViewModel new];
//            space.cellIdentifier = kEmptyCell;
//            space.height = w2;
//            [temp addObject:space];
//        }
//    }
//    self.cellViewModels = temp;
//    
//    self.noMoreData = self.originDataArray.count >= model.totalCount;
//}

- (JDISVOrderListCardViewModel *)cardViewModelFromOrderInfo2:(ORDERListItemModel *)order config:(NSDictionary *)config {
//    ORDERListItemModel *dto3 = order;
    JDISVOrderListCardViewModel *vm = [JDISVOrderListCardViewModel new];
    vm.shopName = order.shopName;
    vm.statusName = order.orderState.stateName;
//    vm.statusType = order.orderState.state;
    vm.stateType = order.orderState.state;
    if (vm.stateType == ORDERListItemStateTypeCanceled) {
        vm.statusColor = JDISVOrderListCardStatusColorDefault;
    } else if (vm.stateType == ORDERListItemStateTypeFinished) {
        vm.statusColor = JDISVOrderListCardStatusColorDefault;
    } else {
        vm.statusColor = JDISVOrderListCardStatusColorRed;
    }
    //待支付, 接口返回的是剩余毫秒数,转成时间戳
    NSString *currentTimeintervalStr = [JDISVCommonTools getNowTimeTimestamp];
    NSString *remainingPaymentTimeStr = [NSString stringWithFormat:@"%0.f",(order.remainingPayTime + currentTimeintervalStr.doubleValue)];
    vm.remainingPaymentTime = remainingPaymentTimeStr;

    vm.totalPrice = order.orderAmount.totalAmount.stringValue;
    
    //product
    NSInteger totalCount = 0;
    NSMutableArray *temp = [NSMutableArray array];
    for (ORDERListItemSkuDetailModel *sku in order.orderItem) {
        JDISVOrderListCardProductModel *product = [JDISVOrderListCardProductModel new];
        product.title = sku.skuName;
        product.imageUrlString = sku.skuImgUrl;
        product.originalPrice = sku.originalPrice;
        totalCount = totalCount + sku.num.integerValue;
        [temp addObject:product];
    }
    vm.totalCount = totalCount;
    vm.productArray = [temp copy];
    
    //button
    NSMutableArray *buttonArr = @[].mutableCopy;
    for (ORDERListItemButtonModel *button in order.buttons) {
        JDISVOrderListCardButtonModel *model = [JDISVOrderListCardButtonModel new];
        model.actionTitle = button.showLabel;
//        model.buttonInfoDTO = btn;
        model.buttonModel = button;
        
        switch (button.showLabelId) {
            case ORDERListItemButtonTypeToPay: {
                model.type = JDISVOrderListCardButtonTypeRedFill;
                break;
            }
            case ORDERListItemButtonTypeQueryLogistics:
            case ORDERListItemButtonTypeCancelOrder: {
                model.type = JDISVOrderListCardButtonTypeDefault;
                break;
            }
            case ORDERListItemButtonTypeBuyAgain:
            case ORDERListItemButtonTypeConfirmReceipt: {
                model.type = JDISVOrderListCardButtonTypeRedBorder;
                break;
            }
            default: {
                model.type = JDISVOrderListCardButtonTypeDefault;
                break;
            }
        }
        [buttonArr addObject:model];
    }
    vm.buttonArray = [buttonArr copy];
    return vm;
}

//- (JDISVOrderListCardViewModel *)cardViewModelFromOrderInfo:(JDISVOrderListOrderInfoDTO *)order config:(NSDictionary *)config{
//    JDISVOrderListOrderInfoDTO *dto3 = order;
//    JDISVOrderListCardViewModel *vm = [JDISVOrderListCardViewModel new];
//    vm.shopName = order.shopInfo.shopName;
//    vm.statusName = order.orderBaseInfo.orderStatusName;
//    vm.statusType = order.orderBaseInfo.orderStatusType;
//    if (vm.statusType == JDISVOrderListOrderStatusTypeCancelled) {
//        vm.statusColor = JDISVOrderListCardStatusColorDefault;
//    } else if (vm.statusType == JDISVOrderListOrderStatusTypeCompleted) {
//        vm.statusColor = JDISVOrderListCardStatusColorDefault;
//    } else {
//        vm.statusColor = JDISVOrderListCardStatusColorRed;
//    }
//    //待支付, 接口返回的是剩余秒数,转成时间戳
//    NSString *currentTimeintervalStr = [JDISVCommonTools getNowTimeTimestamp];
//    NSString *remainingPaymentTimeStr = [NSString stringWithFormat:@"%0.f",(order.orderBaseInfo.remainingPaymentTime.doubleValue*1000+currentTimeintervalStr.doubleValue)];
//    vm.remainingPaymentTime = remainingPaymentTimeStr;
//
//    vm.totalPrice = order.priceInfo.price;
//    
//    //product
//    NSInteger totalCount = 0;
//    NSMutableArray *temp = [NSMutableArray array];
//    for (JDISVOrderListWareInfoItemDTO *item in order.wareInfoList) {
//        JDISVOrderListCardProductModel *product = [JDISVOrderListCardProductModel new];
//        product.title = item.wareName;
//        product.imageUrlString = item.imageUrl;
//        totalCount = totalCount + item.buyCount;
//        [temp addObject:product];
//    }
//    vm.totalCount = totalCount;
//    vm.productArray = [temp copy];
//
//    //拼团 预售
//    if (order.orderBaseInfo.sendPay.length > 44 && [order.orderBaseInfo.sendPay characterAtIndex:43] == '1') {
//        //预售
//        vm.presaleFlag = YES;
//        
//        //预售订单状态处理
//        NSInteger presaleState = order.presaleInfoDTO.state;
//        if (presaleState == 0 || presaleState == 1) {//待付定金
//            vm.presaleStatus = @"0";
//        } else if(presaleState == 2 || presaleState == 3 || presaleState == 4){//已付定金，待付尾款
//            vm.presaleStatus = @"1";
//        }
//        //预售尾款时间相关处理
//        NSString *timeInterVal = [JDISVCommonTools getNowTimeTimestamp];
//        vm.payEndTime = [self getTimeStrWithString:dto3.presaleInfoDTO.endPayTime];
//        
//        vm.paytime = [self getTimeStrWithString:dto3.presaleInfoDTO.startPayTime];
//        
//        NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[timeInterVal integerValue]/1000];
//        NSDate *payDate = [NSDate dateWithTimeIntervalSince1970:[vm.paytime integerValue]/1000];
//        NSComparisonResult result = [currentDate compare:payDate];
//        if (result == NSOrderedDescending) {
//            vm.isCanPay = YES;
//        }else{
//            vm.isCanPay = NO;
//        }
//        NSDate* date =  [NSDate date];
//        vm.payEndDate = [date dateByAddingTimeInterval:dto3.presaleInfoDTO.leftTime];
//    }else if (order.orderBaseInfo.sendPay.length > 433 && [order.orderBaseInfo.sendPay characterAtIndex:432] == '1') {
//        vm.groupBuyFlag = YES;
//        vm.groupBuyStatus = order.shareGroupInfoDTO.status;
//        //接口返回的是剩余秒数,转成时间戳
//        NSString *currentTimeintervalStr = [JDISVCommonTools getNowTimeTimestamp];
//        NSString *remainingPaymentTimeStr = [NSString stringWithFormat:@"%0.f",(order.shareGroupInfoDTO.remainingTime.doubleValue*1000+currentTimeintervalStr.doubleValue)];
//        vm.payEndTime = remainingPaymentTimeStr;
//        vm.groupRemainingPeopleNum = order.shareGroupInfoDTO.groupRemainingPeopleNum;
//    }
//    
//    //button
//    NSMutableArray *buttonArr = @[].mutableCopy;
//    for (JDISVOrderListButtonInfoDTO *btn in order.buttons) {
//        if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeEvaluate) {
//            //是否隐藏评价
//            NSDictionary *features = config[@"features"][@"floors"];
//            NSNumber *evaluateButtonFeature = features[@"KaOrderListActionFloor"][@"evaluateButtonFeature"];
//            if ([evaluateButtonFeature isEqualToNumber:@(0)]){
//                break;
//            }
//        } else if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeDelayReceive){
//            //是否隐藏延迟收货
//            NSDictionary *features = config[@"features"][@"floors"];
//            NSNumber *delayReceiveButtonFeature = features[@"KaOrderListActionFloor"][@"delayReceiveButtonFeature"];
//            if ([delayReceiveButtonFeature isEqualToNumber:@(0)]){
//                break;
//            }
//        } else if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeCheckInvoice){
//            //是否隐藏查看发票
//            NSDictionary *features = config[@"features"][@"floors"];
//            NSNumber *delayReceiveButtonFeature = features[@"KaOrderListActionFloor"][@"checkInvoiceButtonFeature"];
//            if ([delayReceiveButtonFeature isEqualToNumber:@(0)]){
//                break;
//            }
//        } else if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeApplyInvoice){
//            //是否隐藏申请开票
//            NSDictionary *features = config[@"features"][@"floors"];
//            NSNumber *delayReceiveButtonFeature = features[@"KaOrderListActionFloor"][@"applyInvoiceButtonFeature"];
//            if ([delayReceiveButtonFeature isEqualToNumber:@(0)]){
//                break;
//            }
//        }
//        
//        JDISVOrderListCardButtonModel *model = [JDISVOrderListCardButtonModel new];
//        model.actionTitle = btn.showLabel;
//        model.buttonInfoDTO = btn;
//        if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypePay && order.presaleInfoDTO && order.presaleInfoDTO.state == 0) {//预售支付定金
//            model.type = JDISVOrderListCardButtonTypeRedFill;
//            model.buttonInfoDTO.showLabelType = JDISVOrderListOrderActionFloorModelButtonTypePayFirstMoney;
//            
//        } else if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypePay && order.presaleInfoDTO && order.presaleInfoDTO.state == 3) {//预售支付尾款
//            if (vm.isCanPay) {
//                model.type = JDISVOrderListCardButtonTypeRedFill;
//            }else{
//                model.type = JDISVOrderListCardButtonTypeDisable;
//            }
//            model.buttonInfoDTO.showLabelType = JDISVOrderListOrderActionFloorModelButtonTypePayLastMoney;
//        } else if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypePay && order.presaleInfoDTO && order.presaleInfoDTO.state == 2) {//预售支付尾款(未到支付时间)
//            model.type = JDISVOrderListCardButtonTypeDisable;
//        } else if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeBuyAgain ||
//                   btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeConfirmReceive ||
//                   btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeDelayReceive ||
//                   btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypeShareFirend) {
//            model.type = JDISVOrderListCardButtonTypeRedBorder;
//        } else {
//            if (btn.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypePay){
//                model.type = JDISVOrderListCardButtonTypeRedFill;
//            }else{
//                model.type = JDISVOrderListCardButtonTypeDefault;
//            }
//        }
//        
//        [buttonArr addObject:model];
//    }
//    vm.buttonArray = [buttonArr copy];
//    
//    return vm;
//}

- (void)requestOrderListPageDataFailureProcessWithOrderModel:(NSError *)error subscriber:(id<RACSubscriber>)subscriber {
    [subscriber sendError:error];
}

- (void)removeOrderInfoWithOrderId:(NSString *)orderId block:(void (^)(BOOL))block {
    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
        if (block) {
            block(NO);
        }
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"delHistoryOrder" function:@"" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            if (block) {
                block(NO);
            }
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            if ([code isEqual:@0]) {
                NSDictionary *dict = [responseObject objectForKey:@"value"];
                NSNumber *rt = [dict objectForKey:@"result"];
                if (block) {
                    block([rt boolValue]);
                }
            } else {
                if (block) {
                    block(NO);
                }
            }
        }
    }];
}

/*
- (void)cancelOrderInfoWithOrderId:(NSString *)orderId block:(void (^)(BOOL))block {
    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
        if (block) {
            block(NO);
        }
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"cancelOrder" function:@"" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            if (block) {
                block(NO);
            }
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            if ([code isEqual:@0]) {
                NSNumber *rt = [responseObject objectForKey:@"success"];
                if (block) {
                    block([rt boolValue]);
                }
            } else {
                if (block) {
                    block(NO);
                }
            }
        }
    }];
}
*/

- (void)comfirmOrderInfoWithOrderId:(NSString *)orderId block:(void (^)(BOOL))block {
    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
        if (block) {
            block(NO);
        }
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    
//    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"confirmReceipt" function:@"" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//        if (error) {
//            if (block) {
//                block(NO);
//            }
//        } else {
//            NSNumber *code = [responseObject objectForKey:@"code"];
//            if ([code isEqual:@0]) {
//                NSDictionary *dict = [responseObject objectForKey:@"value"];
//                NSNumber *rt = [dict objectForKey:@"result"];
//                if (block) {
//                    block([rt boolValue]);
//                }
//            } else {
//                if (block) {
//                    block(NO);
//                }
//            }
//        }
//    }];
    [[OOPNetworkManager sharedManager] POST:@"order/confirm?apiCode=b2c.cbff.order.confirm" parameters:param headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            JDT_BLOCK_IF_EXISTS(block, NO);
        } else {
            BOOL success = [responseObject[@"data"] boolValue];
            JDT_BLOCK_IF_EXISTS(block, success);
        }
    }];
}

- (void)refreshAllDataWithBlock:(void (^)(void))block {
    [[self requestOrderListDataSizeOf:self.originDataArray.count] subscribeError:^(NSError * _Nullable error) {
        //
    } completed:^{
        if (block) {
            block();
        }
    }];
}

- (RACSignal * _Nonnull)requestOrderListDataSizeOf:(NSInteger)pageSize {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
//        NSURLSessionDataTask *task = [self requestOrderListDataWithOrderType:self.orderListType page:@1 pageSize:@(pageSize) success:^(__kindof JDISVOrderListBaseModel * _Nullable orderListModel) {
//            @strongify(self)
//            [self resetData];
////            [self processOrderModel:orderListModel];
//            [subscriber sendCompleted];
//        } failure:^(NSError * _Nonnull error) {
//            [subscriber sendError:error];
//        }];
//        return [RACDisposable disposableWithBlock:^{
//            [task cancel];
//        }];
        NSMutableDictionary *testParams = [NSMutableDictionary dictionaryWithDictionary:@{
            @"pageNum": @1,
            @"pageSize": @(pageSize),
            @"keywords": @""
        }];
        if (self.orderListType > 0) {
            testParams[@"orderState"] = @[@(self.orderListType)];
        }
        [[OOPNetworkManager sharedManager] POST:@"order/list?apiCode=b2c.cbff.order.list" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                ORDERListModel *model = [ORDERListModel yy_modelWithDictionary:responseObject[@"data"]];
//                [self requestOrderListPageDataSuccessProcessWithOrderListModel:model subscriber:subscriber];
                [self resetData];
                [self processOrderListModel:model];
                [subscriber sendCompleted];
            } else {
//                [self requestOrderListPageDataFailureProcessWithOrderModel:error subscriber:subscriber];
                [subscriber sendError:error];
            }
        }];
        return nil;
    }];
}

//字符串转时间戳 如：2017-4-10 17:15:102022-11-28 00:00:00
- (NSString *)getTimeStrWithString:(NSString *)str{
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];// 创建一个时间格式化对象
    [dateFormatter setDateFormat:@"YYYY-MM-dd HH:mm:ss"]; //设定时间的格式
    NSDate *tempDate = [dateFormatter dateFromString:str];//将字符串转换为时间对象
    NSString *timeStr = [NSString stringWithFormat:@"%ld", (long)[tempDate timeIntervalSince1970]*1000];//字符串转成时间戳,精确到毫秒*1000
    return timeStr;
}
@end
