//
//  ORDERTrackController.h
//  JDTOrderListModule
//
//  Created by lvchenzhu.1 on 2025/6/24.
//

#import <UIKit/UIKit.h>

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

@class ORDERListItemShipmentOrderModel;

typedef NS_ENUM(NSUInteger, ORDERTrackContentType) {
    ORDERTrackContentTypeNone,
    ORDERTrackContentTypeExpress,           // 快递进度显示
    ORDERTrackContentTypeCancelProgress,    // 取消进度显示
};

@interface ORDERTrackController : UIViewController
/// 物流轨迹数据
@property (nonatomic, copy) NSArray <ORDERListItemShipmentOrderModel *> *shipmentDataArr;
/// 取消进度数据
@property (nonatomic, copy) NSArray <ORDERDetailCancelProgressModel *> *progressDataArr;
/// 订单号（取消进度视图需要显示）
@property (nonatomic, copy) NSString *orderId;

- (instancetype)initWithType:(ORDERTrackContentType)type;

@end

NS_ASSUME_NONNULL_END
