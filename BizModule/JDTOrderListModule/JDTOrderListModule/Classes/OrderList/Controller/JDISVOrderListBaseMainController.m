//
//  JDISVOrderListBaseMainController.m
//  JDISVOrderListSDKModule
//
//  Created by 张令浩 on 2021/6/29.
//

#import "JDISVOrderListBaseMainController.h"

#import <JDISVPagerViewModule/JDISVPagerViewModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVOrderListLocSelectController.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import "JDISVOrderListBaseSubController.h"
#import "JDISVPagerViewTableHeaderView.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import "JDISVOrderListBaseMainViewModel.h"
#import "JDISVOrderListBaseListViewModel.h"
#import "UIImage+JDISVOrderListSDK.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>


@interface JDISVOrderListBaseMainController ()<JXCategoryViewDelegate>
@property(strong,nonatomic) UIView* locBtnBackView;
@property(strong,nonatomic) UILabel* labelLoc;
@property(strong,nonatomic) UIImageView* imgLoc;
@property(strong,nonatomic) UIImageView* lineView;
@property(strong,nonatomic) UIButton* locButton;
@end

@implementation JDISVOrderListBaseMainController

- (instancetype)initWith:(__kindof JDISVOrderListBaseMainViewModel * _Nonnull)viewModel {
    
    self = [super init];
    if (self) {
        self.viewModel = viewModel;
        
        if (self.viewModel.subControllerClassName && self.viewModel.subControllerClassName.length > 0) {
            NSMutableArray *tempSubControllers = [NSMutableArray array];
            for (JDISVOrderListBaseListViewModel *listViewModel in self.viewModel.subViewModels) {
                Class controllerClass = NSClassFromString(self.viewModel.subControllerClassName);
                UIViewController<JXPagerViewListViewDelegate, JDISVOrderListSubControlerProtocol> *vc = [[controllerClass alloc] init];
                [vc bindViewModelWith:listViewModel];
                vc.title = listViewModel.categoryTitle;
                [tempSubControllers addObject:vc];
            }
            self.subControllers = [NSArray arrayWithArray:tempSubControllers];
        }
    }
    
    return self;
}


- (void)viewDidLoad {
    [super viewDidLoad];
    [self initData];
}

// 用于处理从WebView和订单详情返回的刷新
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
}

#pragma mark - JDISVOrderListMainControllerProtocol
- (void)initData {
    __weak typeof(self) weakSelf = self;
    [self.subControllers enumerateObjectsUsingBlock:^(__kindof JDISVOrderListBaseSubController<JDISVOrderListMainControllerProtocol> *orderListController, NSUInteger idx, BOOL * _Nonnull stop) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (idx == strongSelf.viewModel.currentSelectedIndex) {
            orderListController.viewModel.currentSelectedOrderKindType = strongSelf.viewModel.currentSelectedOrderKindType;
            [orderListController initListData];
        }
    }];
}

- (void)reloadCurrentTabData {
    __weak typeof(self) weakSelf = self;
    [self.subControllers enumerateObjectsUsingBlock:^(__kindof JDISVOrderListBaseSubController<JDISVOrderListMainControllerProtocol> *orderListController, NSUInteger idx, BOOL * _Nonnull stop) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (idx == strongSelf.viewModel.currentSelectedIndex) {
            orderListController.viewModel.currentSelectedOrderKindType = strongSelf.viewModel.currentSelectedOrderKindType;
            [orderListController reloadData];
        }
    }];
}

-(void)tapLoc{
    NSLog(@"user tap");
    BOOL isRTL = [UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
    JDISVOrderListLocSelectController* presentedVC = [[JDISVOrderListLocSelectController alloc] init];
    
    __weak typeof(self) weakSelf = self;
    presentedVC.sureCallBack = ^(NSInteger type) {
        NSLog(@"user select:%@",@(type));
        __strong typeof(weakSelf) strongSelf = weakSelf;
        strongSelf.viewModel.currentSelectedOrderKindType = type;
        [strongSelf initData];
    };
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:presentedVC presentingViewController:self];
    presentationVC.type = KAFloatLayerTypeCustom;
    presentationVC.contentHeight = [UIScreen mainScreen].bounds.size.height;
    presentationVC.insets = UIEdgeInsetsZero;
    presentationVC.animationDirection = isRTL ? KAFloatLayerDirectionLeftToRight : KAFloatLayerDirectionRigthToLeft;
    presentationVC.contentWidth = self.view.bounds.size.width - 40;
    
    presentedVC.type = self.viewModel.currentSelectedOrderKindType;
    presentedVC.transitioningDelegate = presentationVC;
    [self presentViewController:presentedVC animated:YES completion:nil];
}

#pragma mark - 配置CategoryView
- (void)initializeCategoryView {
  
    self.categoryView = [[JXCategoryTitleView alloc] init];
    self.categoryView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C2");
    self.categoryView.delegate = self;
    self.categoryView.titles = _viewModel.categoryViewModel.titles;
    self.categoryView.titleSelectedColor = _viewModel.categoryViewModel.titleSelectedColor;
    self.categoryView.titleColor = _viewModel.categoryViewModel.titleColor;
    self.categoryView.titleFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.categoryView.titleSelectedFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
    self.categoryView.cellSpacing = 24;
    self.categoryView.titleColorGradientEnabled = _viewModel.categoryViewModel.titleColorGradientEnabled;
    self.categoryView.contentScrollViewClickTransitionAnimationEnabled = _viewModel.categoryViewModel.contentScrollViewClickTransitionAnimationEnabled;
    self.categoryView.averageCellSpacingEnabled = NO;
    // 设置默认选中
    [self.categoryView setDefaultSelectedIndex:self.viewModel.currentSelectedIndex];

    JXCategoryIndicatorLineView *lineView = [[JXCategoryIndicatorLineView alloc] init];
    lineView.indicatorColor = _viewModel.categoryViewModel.lineViewIndicatorColor;
    lineView.indicatorWidth = _viewModel.categoryViewModel.lineViewIndicatorWidth;
    lineView.verticalMargin = 10;
    self.categoryView.indicators = @[lineView];
    
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/KSAGlobalSearchView",@"KSAGlobalSearchModule"] arg:nil error:nil completion:^(UIView * view) {
        if(view){
            [self.view addSubview:view];
            [view mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.view);
                make.trailing.mas_equalTo(self.view);
                make.top.mas_equalTo([UIWindow ka_uikit_navigationHeight]);
                make.height.mas_equalTo(50.);
            }];
        }
    }];

}

#pragma mark - JXCategoryViewDelegate
- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
    if (index == self.viewModel.currentSelectedIndex) {
        return;
    }
    self.viewModel.currentSelectedIndex = index;
    [self initData];
}

#pragma mark - JDISVPagerScrollViewDataSource

//开始
- (NSUInteger)tableHeaderViewHeightInPagerView:(JXPagerView *)pagerView {
    return self.viewModel.topViewHeight;
}

- (NSUInteger)heightForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return self.viewModel.categoryViewModel.heightOfCategoryView + self.navigationBarOffset;
}

- (UIView *)viewForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    CGFloat width = CGRectGetWidth(pagerView.frame);
    CGFloat height = self.viewModel.categoryViewModel.heightOfCategoryView + self.navigationBarOffset;
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, width, height)];
    [view addSubview:self.categoryView];
    self.categoryView.frame = CGRectMake(0, self.navigationBarOffset, width, self.viewModel.categoryViewModel.heightOfCategoryView);
    return view;
}

-(void)createLocView:(CGFloat)x
                   y:(CGFloat)y
          titleWidth:(CGFloat)titleWidth
           iconWidth:(CGFloat)iconWidth
         buttonWidth:(CGFloat)buttonWidth
            height:(CGFloat)height{
    self.locBtnBackView = [[UIView alloc] initWithFrame:CGRectMake(x, y, buttonWidth, height)];
    self.locBtnBackView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C16");
    UIImage* lineImg = [UIImage jdisvOrderListImageNamed:@"locLine"];
//    lineImg = [lineImg imageWithRenderingMode: UIImageRenderingModeAlwaysTemplate];
    self.lineView = [[UIImageView alloc] initWithImage: lineImg];
    self.lineView.frame = CGRectMake(0, 8, 1, 36);
//    self.lineView.tintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    [self.locBtnBackView addSubview:self.lineView];
    
    self.labelLoc = [[UILabel alloc] initWithFrame:CGRectMake(CGRectGetMaxX(self.imgLoc.frame) + 6.f, 16, titleWidth, 20)];
    self.labelLoc.textAlignment = NSTextAlignmentCenter;
    self.labelLoc.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.labelLoc.textColor = _viewModel.categoryViewModel.titleColor;
    self.labelLoc.text = OrderListL(@"isv_order_screen");
    [self.locBtnBackView addSubview:self.labelLoc];
    
    UIImage* menu = [UIImage ka_iconWithName:JDIF_ICON_SYSTEM_MENU imageSize:CGSizeMake(12, 12)
                    color:_viewModel.categoryViewModel.titleColor];
//    menu = [menu imageWithRenderingMode: UIImageRenderingModeAlwaysTemplate];
    self.imgLoc = [[UIImageView alloc] initWithImage: menu];
    self.imgLoc.frame = CGRectMake(CGRectGetMaxX(self.labelLoc.frame) + 6.f, 20, iconWidth, iconWidth);
//    self.imgLoc.tintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    [self.locBtnBackView addSubview:self.imgLoc];
    
    self.locButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.locButton setTitle:@"" forState:UIControlStateNormal];
    [self.locButton addTarget:self action:@selector(tapLoc) forControlEvents:UIControlEventTouchUpInside];
    self.locButton.frame = CGRectMake(0, 0, buttonWidth,height);
    [self.locBtnBackView addSubview:self.locButton];
    
    [self.lineView jdcd_frameRTL];
    [self.imgLoc jdcd_frameRTL];
    [self.labelLoc jdcd_frameRTL];
    [self.locButton jdcd_frameRTL];
}
- (NSInteger)numberOfListsInPagerView:(JXPagerView *)pagerView {
    return self.viewModel.categoryViewModel.titles.count;
}

//结束
- (id<JXPagerViewListViewDelegate>)pagerView:(JXPagerView *)pagerView initListAtIndex:(NSInteger)index {
    if (index < self.subControllers.count) {
        UIViewController<JXPagerViewListViewDelegate> *vc = [self.subControllers objectAtIndex:index];
        return vc;
    }
    JDISVOrderListBaseSubController *listVC = [[JDISVOrderListBaseSubController alloc] init];
    listVC.title = @"JDISVOrderListBaseSubController";
    return listVC;
}

@end
