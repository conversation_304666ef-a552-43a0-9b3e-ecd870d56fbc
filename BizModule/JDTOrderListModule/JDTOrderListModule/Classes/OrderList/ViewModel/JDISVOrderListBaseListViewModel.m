//
//  JDISVOrderListBaseListViewModel.m
//  JDISVOrderListSDKModule
//
//  Created by 张令浩 on 2021/6/30.
//

#import "JDISVOrderListBaseListViewModel.h"
#import "JDISVOrderListBaseModel.h"
#import "JDISVOrderListResModel.h"
#import "JDISVOrderListMacro.h"
#import "JDISVCommonTools.h"
#import <JDISVYYModelModule/YYModel.h>
@import JDTCommonToolModule;

@import JDTInfrastructureModule;

@implementation JDISVOrderListTypeModel

+ (instancetype)orderListTypeOf:(NSInteger)type categoryTitle:(NSString *)title {
    JDISVOrderListTypeModel *model = [[JDISVOrderListTypeModel alloc] init];
    model.title = title;
    model.orderListType = type;
    
    return model;
}

@end

@interface JDISVOrderListBaseListViewModel ()
@property (nonatomic, strong) NSMutableArray *requestArray; /**< 当前请求队列 */
@end

@implementation JDISVOrderListBaseListViewModel

- (instancetype)initWithOrderListType:(NSInteger)orderListType
{
    self = [super init];
    if (self) {
        _pageSize = 10;
        _cellViewModels = [NSMutableArray array];
        _requestArray = [NSMutableArray array];
        _orderListType = orderListType;
        _pageCount = 1;
        _noMoreData = NO;
    }
    return self;
}

- (void)resetData {
    [self.cellViewModels removeAllObjects];
    self.pageCount = 1;
    self.noMoreData = NO;
    [self cancelAllRequest];
}

// 取消页面当前正在请求队列中的NSURLSessionDataTask
- (void)cancelAllRequest {
    if (self.requestArray == nil || self.requestArray.count <= 0) return;
    for (NSURLSessionDataTask *task in self.requestArray) {
        if (task) [task cancel];
        
    }
    [self.requestArray removeAllObjects];
}


/// 初始化首页数据
- (RACSignal *)initializeOrderListPageData {
    [self resetData];
    return [self requestOrderListPageData];
}

/// 加载下一页数据
- (RACSignal *)loadMoreData {
    return [self requestOrderListPageDataWithPageCount:self.pageCount + 1 updateCount:YES];
}

/// 基础方法
- (RACSignal *)requestOrderListPageData {
    return [self requestOrderListPageDataWithPageCount:self.pageCount updateCount:NO];
}

- (RACSignal *)requestOrderListPageDataWithPageCount:(NSUInteger)pageCount updateCount:(BOOL)updateCount {
    __weak typeof(self) weakSelf = self;
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
//        NSURLSessionDataTask *task = [self requestOrderListDataWithOrderType:self.orderListType page:@(pageCount) pageSize:@(self.pageSize) success:^(__kindof JDISVOrderListBaseModel *orderListModel) {
//            if (updateCount) {
//                strongSelf.pageCount = pageCount;
//            }
//            [strongSelf requestOrderListPageDataSuccessProcessWithOrderModel:orderListModel subscriber:subscriber];
//        } failure:^(NSError *error) {
//            [strongSelf requestOrderListPageDataFailureProcessWithOrderModel:error subscriber:subscriber];
//        }];
//        
//        if (task) {
//            [self.requestArray addObject:task];
//        }
        
        NSDictionary *testParams = @{
            @"orderState": @[@(strongSelf.orderListType)],
            @"pageNum": @(pageCount),
            @"pageSize": @(20),
            @"keywords": @""
        };
        [[OOPNetworkManager sharedManager] POST:@"order/list?apiCode=b2c.cbff.order.list" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                if (updateCount) {
                    strongSelf.pageCount = pageCount;
                }
                ORDERListModel *model = [ORDERListModel yy_modelWithDictionary:responseObject[@"data"]];
                [strongSelf requestOrderListPageDataSuccessProcessWithOrderListModel:model subscriber:subscriber];
            } else {
                [strongSelf requestOrderListPageDataFailureProcessWithOrderModel:error subscriber:subscriber];
            }
        }];
        
        return nil;
    }];
}

//- (void)requestOrderListPageDataSuccessProcessWithOrderModel:(__kindof JDISVOrderListBaseModel *)model subscriber:(id<RACSubscriber>  _Nonnull) subscriber {
//    
//}

- (void)requestOrderListPageDataSuccessProcessWithOrderListModel:(__kindof ORDERListModel * )model subscriber:(id<RACSubscriber>  )subscriber {
    
}

- (void)requestOrderListPageDataFailureProcessWithOrderModel:(NSError *)error subscriber:(id<RACSubscriber>  _Nonnull)subscriber {
    
}

@end
