//
//  JDISVOrderListCardViewModel.m
//  JDISVOrderListSDKModule
//
//  Created by gongyang2 on 2021/9/22.
//

#import "JDISVOrderListCardViewModel.h"

@implementation JDISVOrderListCardProductModel

@end

@implementation JDISVOrderListCardButtonModel

@end

@implementation JDISVOrderListCardViewModel

-(void)setStatusName:(NSString *)statusName{
    _statusName = statusName;
}
- (instancetype)init
{
    self = [super init];
    if (self) {
        _statusColor = JDISVOrderListCardStatusColorDefault;
    }
    return self;
}

- (CGFloat)height {
    
    CGFloat moreSpace = 0;
    //拼团中倒计时 || 支付尾款倒计时
      if ([self.groupBuyStatus isEqualToString:@"101"] || [self.presaleStatus isEqualToString:@"1"]) {
          moreSpace = 33 + 9 ;
      }
    
    if (self.buttonArray.count == 0) {
        return 18+20+18+80+18+moreSpace+30;
    }
    
    return 18+20+18+80+12+30+18+moreSpace+30;
}

@end
