//
//  JDISVOrderListCardViewModel.h
//  JDISVOrderListSDKModule
//
//  Created by gongyang2 on 2021/9/22.
//

#import <Foundation/Foundation.h>
#import "JDISVOrderListResModel.h"
@import JDTCommonToolModule.ORDERListModel;
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVOrderListCardStatusColor) {
    JDISVOrderListCardStatusColorDefault, // 黑色
    JDISVOrderListCardStatusColorRed, // 红色
};

typedef NS_ENUM(NSUInteger, JDISVOrderListCardButtonType) {
    JDISVOrderListCardButtonTypeDefault, /**< 灰色边框 */
    JDISVOrderListCardButtonTypeRedBorder, /**< 红色边框 */
    JDISVOrderListCardButtonTypeRedFill, /**< 红色填充 */
    JDISVOrderListCardButtonTypeDisable, /**<禁用 */
};

@class JDISVOrderListResModel;

@interface JDISVOrderListCardProductModel : NSObject

@property (nonatomic, copy) NSString *title; /**< 商品名称 */
@property (nonatomic, copy) NSString *imageUrlString; /**< 商品图片URL */
@property (nonatomic, copy) NSNumber *originalPrice; /**< 商品总价 eg 688.88 */

@end


@class JDISVOrderListButtonInfoDTO;

@interface JDISVOrderListCardButtonModel : NSObject

@property (nonatomic, copy) NSString *actionTitle; /**< 按钮名称 */
//@property (nonatomic, assign) JDISVOrderListButtonInfoDTO *buttonInfoDTO; /**< 按钮数据DTO */
@property (nonatomic, strong) ORDERListItemButtonModel *buttonModel;
@property (nonatomic, assign) JDISVOrderListCardButtonType type; /**< 按钮类型 */

@end

@interface JDISVOrderListCardViewModel : NSObject
@property (nonatomic,assign) bool isCanPay;
@property (nonatomic, copy) NSString *shopName; /**< 店铺名称 */
@property (nonatomic, copy) NSString *statusName; /**< 订单状态名称 */
@property (nonatomic, assign) ORDERListItemStateType stateType;
//@property (nonatomic, assign) JDISVOrderListOrderStatusType statusType; /**< 订单状态Id */
@property (nonatomic, assign) BOOL presaleFlag; /**< 预售标识*/
@property (nonatomic, assign) BOOL groupBuyFlag; /**<拼团标识 */
@property (nonatomic, copy) NSString *presaleStatus; /**< 预售状态：0待付定金，1待付尾款*/
@property (nonatomic, copy) NSString *groupBuyStatus; /**< 拼团状态status  101-拼团中      102-拼团成功   103-拼团失败*/
@property (nonatomic, copy) NSString *groupRemainingPeopleNum; /**<拼团剩余人数 */
@property (nonatomic,copy) NSString *payEndTime;
@property (nonatomic,copy) NSString *paytime;
@property (nonatomic,copy) NSString *serverTime;
@property (nonatomic,strong) NSDate* prepayEndTime;
@property (nonatomic,assign) NSInteger leftTime;
@property (nonatomic,strong) NSDate *payEndDate;

@property (nonatomic,copy) NSString *remainingPaymentTime;//立即支付倒计时
@property (nonatomic, assign) JDISVOrderListCardStatusColor statusColor; /**< 订单状态前景色 默认JDISVOrderListCardStatusColorDefault */
@property (nonatomic, assign) BOOL showRemoveButton; /**< 是否展示移除按钮 默认NO */
@property (nonatomic, copy) NSArray<JDISVOrderListCardProductModel *> *productArray; /**< 商品列表, 多个商品可以不传商品名称, 单个商品需要传 */
@property (nonatomic, copy) NSString *totalPrice; /**< 商品总价 eg 688.88 */
@property (nonatomic, assign) NSUInteger totalCount; /**< 商品总数 */
@property (nonatomic, copy) NSArray<JDISVOrderListCardButtonModel *> *buttonArray; /**< 事件按钮列表, 逆序排列 */

/// 视图高度
- (CGFloat)height;

@end

NS_ASSUME_NONNULL_END
