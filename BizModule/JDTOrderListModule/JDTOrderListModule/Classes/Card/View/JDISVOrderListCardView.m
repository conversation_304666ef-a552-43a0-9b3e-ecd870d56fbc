//
//  JDISVOrderListCardView.m
//  JDISVOrderListSDKModule
//
//  Created by gongyang2 on 2021/9/22.
//

#import "JDISVOrderListCardView.h"

#import "NSBundle+JDISVOrderListSDKBundle.h"
#import "UIImage+JDISVOrderListSDK.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVImageModule/JDISVImageModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import "JDISVFloorRenderModule/UIView+JDCDISVFloorRenderCorner.h"
#import "JDISVOrderListCardImageCell.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVOrderListMacro.h"
#import "JDISVCommonTools.h"

@interface JDISVOrderListCardView () <UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) JDISVOrderListCardViewModel *viewModel;
@property (nonatomic, strong) UIView *verticalGrain;
@property (nonatomic, strong) UIButton *removeButton;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UIImageView *shopIcon;
@property (nonatomic, strong) UILabel *shopNameLabel;
@property (nonatomic, strong) UIImageView *arrow;
@property (nonatomic, strong) KAPriceLabel *priceLabel;
@property (nonatomic, strong) UILabel *countLabel;

@property (nonatomic, strong) UIView *productContainer;
@property (nonatomic, strong) UIImageView *productImageView;
@property (nonatomic, strong) UILabel *productName;

@property (nonatomic, strong) KAPriceLabel *orderPriceLabel;
@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UIStackView *stackView;


@property (nonatomic,strong) UILabel *timeLabel;//拼团、预售倒计时
@property (nonatomic,strong) UIView *timeView;

@property (nonatomic,strong) JDISVTimerView *threeTimeInfoView;
@property (nonatomic,strong) JDISVTimerView *twoTimeInfoView;
@property (nonatomic,strong) UILabel *dayLabel;

@property (nonatomic,strong) UIButton *paymentBtn;

@property (nonatomic,strong) UILabel *lijiPayTimeLabelBG;//立即支付倒计时
@property (nonatomic,strong) UILabel *lijiPayTimeLabel;//立即支付倒计时
@end
//我在走
@implementation JDISVOrderListCardView

-(void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        //header
        UIImageView *shopIcon = [[UIImageView alloc] initWithImage:[UIImage ka_iconWithName:JDIF_ICON_SHOP_LINE imageSize:(CGSizeMake(18, 18)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]]];
        [self addSubview:shopIcon];
        [shopIcon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(20);
            make.leading.mas_equalTo(18);
            make.top.mas_equalTo(18);
        }];
        self.shopIcon = shopIcon;
        
        UILabel *shopName = [UILabel new];
        shopName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
        shopName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _shopNameLabel = shopName;
        [self addSubview:shopName];
        //CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        [shopName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(shopIcon.mas_trailing).offset(2);
            make.centerY.equalTo(shopIcon);
//            make.width.lessThanOrEqualTo(@([UIScreen mainScreen].bounds.size.width-18-20-2-2-12-18-w3*2-100));
        }];
        
        UIImage *arrorImg = [UIImage jdisvOrderListImageNamed:@"isv_orderlist_arrow_right"];
        if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
            arrorImg = [[UIImage jdisvOrderListImageNamed:@"isv_orderlist_arrow_right"] JDCDRTL];
        }
        UIImageView *arrow = [[UIImageView alloc] initWithImage:arrorImg];
        [self addSubview:arrow];
        [arrow mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(12);
            make.centerY.equalTo(shopIcon);
            make.leading.equalTo(shopName.mas_trailing).offset(2);
            make.trailing.lessThanOrEqualTo(_statusLabel.mas_leading).offset(-12);
        }];
        self.arrow = arrow;
        
        UIButton *lucidButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
        [lucidButton setTitle:@"" forState:(UIControlStateNormal)];
        [lucidButton addTarget:self action:@selector(shopButtonOnClicked:) forControlEvents:(UIControlEventTouchUpInside)];
        [self addSubview:lucidButton];
        [lucidButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.centerY.equalTo(shopName);
            make.trailing.equalTo(arrow);
            make.height.mas_equalTo(20);
        }];
        
        UIButton *removeButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
        _removeButton = removeButton;
        [removeButton setImage:[UIImage jdisvOrderListImageNamed:@"isv_orderlist_remove"] forState:(UIControlStateNormal)];
        [removeButton addTarget:self action:@selector(removeButtonOnClicked:) forControlEvents:(UIControlEventTouchUpInside)];
        [self addSubview:removeButton];
        [removeButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(20);
            make.trailing.mas_equalTo(-18);
            make.centerY.equalTo(shopIcon);
        }];
        
        UIView *verticalGrain = [UIView new];
        _verticalGrain = verticalGrain;
        verticalGrain.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C5");
        [self addSubview:verticalGrain];
        [verticalGrain mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(0.5);
            make.trailing.equalTo(removeButton.mas_leading).offset(-6);
            make.height.mas_equalTo(10);
            make.centerY.equalTo(removeButton);
        }];
        
        UILabel *statusLabel = [UILabel new];
        _statusLabel = statusLabel;
        statusLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        [self addSubview:statusLabel];
        [statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(-(18+20+6+1+7));
            make.centerY.equalTo(shopIcon);
        }];
        
        //middle
        KAPriceLabel *priceLabel = [KAPriceLabel labelWithPrice:0 type:(KAPriceTypeP3) colorType:@"#C7"];
        _priceLabel = priceLabel;
        _priceLabel.textAlignment = NSTextAlignmentRight;
        [priceLabel setContentCompressionResistancePriority:(UILayoutPriorityRequired) forAxis:(UILayoutConstraintAxisHorizontal)];
        [self addSubview:priceLabel];
        [priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(-18);
            make.top.mas_equalTo(77);
        }];
        
        UILabel *countLabel = [UILabel new];
        _countLabel = countLabel;
        _countLabel.numberOfLines = 1;
        _countLabel.textAlignment = NSTextAlignmentRight;
        countLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
        countLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
        
        [self addSubview:countLabel];
        [countLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(priceLabel.mas_bottom).offset(5);
            make.trailing.equalTo(priceLabel);
            make.leading.mas_equalTo(self.priceLabel);
        }];
        
        UIView *productContainer = [UIView new];
        _productContainer = productContainer;
        [self addSubview:productContainer];
        [productContainer mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(18);
            make.top.equalTo(shopIcon.mas_bottom).offset(19);
            make.height.mas_equalTo(80);
            make.trailing.equalTo(priceLabel.mas_leading).offset(-12);
        }];
        
        UIImageView *productImageView = [UIImageView new];
        _productImageView = productImageView;
        productImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
        productImageView.layer.masksToBounds = YES;
        [productContainer addSubview:productImageView];
        [productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(80);
            make.top.leading.bottom.equalTo(productContainer);
        }];
        
        UIView *mask = [UIView new];
        mask.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"] colorWithAlphaComponent:0.02];
        mask.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
        [productImageView addSubview:mask];
        [mask mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(productImageView);
        }];
        
        UILabel *productName = [UILabel new];
        _productName = productName;
        productName.numberOfLines = 2;
        productName.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        productName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        [productContainer addSubview:productName];
        [productName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.trailing.equalTo(productContainer);
            make.leading.equalTo(productImageView.mas_trailing).offset(6);
        }];
        
        UICollectionViewFlowLayout *layout = [UICollectionViewFlowLayout new];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.itemSize = CGSizeMake(80, 80);
        layout.minimumLineSpacing = 6;
        layout.minimumInteritemSpacing = 6;
        UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
//        collectionView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
        collectionView.backgroundColor = [UIColor clearColor];
        _collectionView = collectionView;
        collectionView.showsVerticalScrollIndicator = NO;
        collectionView.hidden = YES;
        collectionView.delegate = self;
        collectionView.dataSource = self;
        [self addSubview:collectionView];
        [collectionView registerClass:[JDISVOrderListCardImageCell class] forCellWithReuseIdentifier:@"cell"];
        [collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(productContainer);
        }];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(collectionTapGesture:)];
        [collectionView addGestureRecognizer:tap];
        
        UIView * line = [UIView new];
        line.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C33"];
        //line.hidden = YES;
        [self addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0.5);
            make.leading.mas_equalTo(18);
            make.trailing.mas_equalTo(-18);
            make.top.mas_equalTo(productContainer.mas_bottom).offset(8);
            
        }];
        
        KAPriceLabel *orderPriceLabel = [KAPriceLabel labelWithPrice:0 type:(KAPriceTypeP3) colorType:@"#C7"];
        _orderPriceLabel = orderPriceLabel;
        _orderPriceLabel.textAlignment = NSTextAlignmentRight;
        [orderPriceLabel setContentCompressionResistancePriority:(UILayoutPriorityRequired) forAxis:(UILayoutConstraintAxisHorizontal)];
        [self addSubview:orderPriceLabel];
        [orderPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(-18);
            make.top.mas_equalTo(productContainer.mas_bottom).offset(10);
        }];
        
        UILabel *orderPriceName = [UILabel new];
        orderPriceName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
        orderPriceName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        orderPriceName.text = OrderListL(@"isv_order_order_price_lable");
        [self addSubview:orderPriceName];
        [orderPriceName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(orderPriceLabel.mas_leading).offset(-5);
            make.bottom.equalTo(orderPriceLabel);
        }];
        
        //footer
        UIStackView *stackView = [[UIStackView alloc] init];
        _stackView = stackView;
        stackView.spacing = 12;
        stackView.axis = UILayoutConstraintAxisHorizontal;
        stackView.distribution = UIStackViewDistributionFill;
        stackView.alignment = UIStackViewAlignmentBottom;
        [self addSubview:stackView];
        [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(18);
            make.bottom.equalTo(self.mas_bottom).offset(-18);
            make.trailing.mas_equalTo(-18);
            make.height.mas_equalTo(30);
        }];
        
        
        self.lijiPayTimeLabelBG = [[UIView alloc] init];
        self.lijiPayTimeLabelBG.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"] colorWithAlphaComponent:0.07];
        [self addSubview:self.lijiPayTimeLabelBG];
        
        //立即支付倒计时label
        UILabel *lijitimeLabel = [[UILabel alloc]init];
        self.lijiPayTimeLabel = lijitimeLabel;
        lijitimeLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        lijitimeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        lijitimeLabel.numberOfLines = 2;
//        lijitimeLabel.backgroundColor = [UIColor blackColor];
//        lijitimeLabel.layer.cornerRadius = 30/2;
//        lijitimeLabel.layer.masksToBounds = YES;
//        lijitimeLabel.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"] colorWithAlphaComponent:0.07];
        [self.lijiPayTimeLabelBG addSubview:self.lijiPayTimeLabel];
        
        [self.lijiPayTimeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.mas_equalTo(self.lijiPayTimeLabelBG);
            make.leading.mas_equalTo(7.5);
            make.trailing.mas_equalTo(-15);
        }];
        
        //预售、拼团倒计时label
        UILabel *timeLabel = [[UILabel alloc]init];
        timeLabel.numberOfLines = 2;
        self.timeLabel = timeLabel;
        [self addSubview:timeLabel];
        [timeLabel setHidden:YES];
        timeLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        
        UIView *timeView = [[UIView alloc]init];
        [self addSubview:timeView];
        self.timeView = timeView;
        timeView.layer.cornerRadius = 4;
        [timeView.layer masksToBounds];
        timeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        timeView.alpha = 0.07;
        [timeView setHidden:YES];
        
        JDISVTimerView *threeTimeInfoView = [[JDISVTimerView alloc]initWithFrame:CGRectMake(0, 0, 0, 0) itemCount:3];
        [threeTimeInfoView setHidden:YES];
        self.threeTimeInfoView = threeTimeInfoView;
        threeTimeInfoView.borderColor = [UIColor clearColor];
        [self addSubview:threeTimeInfoView];
        
        JDISVTimerView *twoTimeInfoView = [[JDISVTimerView alloc]initWithFrame:CGRectMake(0, 0, 0, 0) itemCount:2];
        [twoTimeInfoView setHidden:YES];
        self.twoTimeInfoView = twoTimeInfoView;
        twoTimeInfoView.borderColor = [UIColor clearColor];
        [self addSubview:twoTimeInfoView];
        [twoTimeInfoView updateHour:@"12" min:@"33"];
        
        UILabel *hTextLabel = [self.threeTimeInfoView valueForKey:@"hourLabel"];
        UILabel *mTextLabel = [self.threeTimeInfoView valueForKey:@"minLabel"];
        UILabel *sTextLabel = [self.threeTimeInfoView valueForKey:@"secLabel"];
        hTextLabel.font =  [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        mTextLabel.font = hTextLabel.font;
        sTextLabel.font =  hTextLabel.font;
        
        hTextLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        mTextLabel.textColor = hTextLabel.textColor;
        sTextLabel.textColor = hTextLabel.textColor;
        
        UILabel *hTextLabel2 = [self.twoTimeInfoView valueForKey:@"hourLabel"];
        UILabel *mTextLabel2 = [self.twoTimeInfoView valueForKey:@"minLabel"];
        UILabel *sTextLabel2 = [self.twoTimeInfoView valueForKey:@"secLabel"];
        hTextLabel2.font =  [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        mTextLabel2.font = hTextLabel.font;
        sTextLabel2.font =  hTextLabel.font;
        
        hTextLabel2.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        mTextLabel2.textColor = hTextLabel.textColor;
        sTextLabel2.textColor = hTextLabel.textColor;
        
        self.dayLabel = [[UILabel alloc]init];
        self.dayLabel.textAlignment = NSTextAlignmentCenter;
        self.dayLabel.textColor = sTextLabel.textColor;
        self.dayLabel.font = sTextLabel.font;
        self.dayLabel.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        self.dayLabel.layer.cornerRadius = 3;
        self.dayLabel.layer.masksToBounds = YES;
        [self addSubview:self.dayLabel];
        
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateTimer:) name:ScheduleTimer object:nil];
    }
    return self;
}

- (void)updateTimer:(NSNotification *)fications{

    if ([self.viewModel.presaleStatus isEqualToString:@"1"]) {//待付尾款
        self.lijiPayTimeLabelBG.hidden = YES;
        
        NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[[JDISVCommonTools getNowTimeTimestamp] integerValue]/1000];
        NSDate *paytime = [NSDate dateWithTimeIntervalSince1970:[self.viewModel.paytime integerValue]/1000];
        
        NSComparisonResult result = [currentDate compare:paytime];
        
        if (result == NSOrderedDescending) {
            [self canPayStatus:[JDISVCommonTools getNowTimeTimestamp]];//倒计时
        }else{
            [self canNotPayStatus];//尾款应付时间
        }
    } else if (self.viewModel.stateType == ORDERListItemStateTypeWaitPay || [self.viewModel.presaleStatus isEqualToString:@"0"]) {
        NSString *paymentTime = @"";
        paymentTime = self.viewModel.remainingPaymentTime;
        NSString *currentTimeintervalStr = [JDISVCommonTools getNowTimeTimestamp];//fication.object[@"currentDate"];

        NSTimeInterval endInterVel = [paymentTime integerValue] /1000;
        NSDate *endPaymentDate = [NSDate dateWithTimeIntervalSince1970:endInterVel];
        
        NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[currentTimeintervalStr integerValue]/1000];
        NSComparisonResult result = [currentDate compare:endPaymentDate];
        if(result == NSOrderedDescending){
            self.lijiPayTimeLabelBG.hidden = YES;
        }else{
            NSString *day_h_m_s = [JDISVCommonTools getDay_H_M_S_WithEndTime:paymentTime];
            NSString *text = [NSString stringWithFormat:OrderListL(@"isv_order_auto_close"),day_h_m_s];
            if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
//                text = [text stringByReplacingOccurrencesOfString:@" " withString:@""];
                text = [NSString stringWithFormat:@"%@  ",text];
            }
            self.lijiPayTimeLabel.text = text;
            self.lijiPayTimeLabelBG.hidden = NO;
            ///MARK: 6.20 P0UI修改，固定倒计时展示宽度
//            CGFloat timeW = [self.lijiPayTimeLabel.text boundingRectWithSize:CGSizeMake(300, 15) options:(NSStringDrawingUsesLineFragmentOrigin) attributes:@{ NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] } context:nil].size.width;
//            timeW = timeW + 35;
//            timeW = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2) - 36.f;
//
//            [self.lijiPayTimeLabelBG mas_updateConstraints:^(MASConstraintMaker *make) {
//                make.width.mas_equalTo(timeW);
//            }];
            [self layoutIfNeeded];
            if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                [self.lijiPayTimeLabelBG isv_floorRender_addRoundedCorners:UIRectCornerTopRight | UIRectCornerBottomRight withRadii:CGSizeMake(15, 15) viewRect:self.lijiPayTimeLabelBG.bounds];
            }else{
                [self.lijiPayTimeLabelBG isv_floorRender_addRoundedCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft withRadii:CGSizeMake(15, 15) viewRect:self.lijiPayTimeLabelBG.bounds];
            }
        }
    } else if ([self.viewModel.groupBuyStatus isEqualToString:@"101"]) {
        [self showGroupBuyStatus:[JDISVCommonTools getNowTimeTimestamp]];
    }
}

- (void)showGroupBuyStatus:(NSString *)currentTime{
    NSTimeInterval endInterVel = [self.viewModel.payEndTime integerValue] /1000;
    NSDate *endPaymentDate = [NSDate dateWithTimeIntervalSince1970:endInterVel];
    NSString *currentDateStr = [JDISVCommonTools getNowTimeTimestamp];
    NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[currentDateStr integerValue]/1000];
    NSComparisonResult result = [currentDate compare:endPaymentDate];
    if(result == NSOrderedDescending){
        [self.threeTimeInfoView setHidden:YES];
        [self.twoTimeInfoView setHidden:YES];
        [self.dayLabel setHidden:YES];
        self.timeView.backgroundColor = UIColor.clearColor;
        self.timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.timeLabel.text = OrderListL(@"isv_order_joinGroupBuy_outTime");
        [self.timeLabel setHidden:YES];
        [self.timeView setHidden:YES];
        return;
    }
    
    NSString *currentTimeintervalStr = currentTime;//fication.object[@"currentDate"];
    NSDateFormatter *dateFromatter = [[NSDateFormatter alloc]init];
    dateFromatter.dateFormat = @"yyyy-MM-dd HH:mm:ss";
    
    NSString *diffDate = [JDISVCommonTools diffDateWithEndTime:self.viewModel.payEndTime serverTime:currentTimeintervalStr];
    
    NSArray *timeClipArray = [diffDate componentsSeparatedByString:@":"];
    
    NSString *day = timeClipArray[0];
    NSString *h = timeClipArray[1];
    NSString *m = timeClipArray[2];
    NSString *s = timeClipArray[3];
    if(h.length == 1){
        h = [@"0" stringByAppendingString:h];
    }
    if(m.length == 1){
        m = [@"0" stringByAppendingString:m];
    }
    if(s.length == 1){
        s = [@"0" stringByAppendingString:s];
    }
    
    NSString *days = [[@"" jdcd_appendStr:day] jdcd_appendStr:OrderListL(@"isv_order_data_format6")];
    if ([day intValue] == 0) {
        days = @"";
    }
  
    self.timeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    self.timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C45"];
    self.timeLabel.text = [NSString stringWithFormat:OrderListL(@"isv_order_list_un_team_tip"),self.viewModel.groupRemainingPeopleNum?:@""];
    
    [self.dayLabel setHidden:NO];
    self.dayLabel.text = days;
    [self.dayLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.timeLabel.mas_trailing);
        make.centerY.equalTo(self.timeLabel);
        make.height.equalTo(@20);
        CGSize textSize = [self.dayLabel.text sizeWithAttributes:@{NSFontAttributeName:self.dayLabel.font}];
        self.dayLabel.mj_size = textSize;
        if (self.dayLabel.text.length>0) {
            make.width.equalTo(@(textSize.width+6));
        }else{
            make.width.equalTo(@(0));
        }
    }];
    
    [self.threeTimeInfoView updateHour:h min:m sec:s];
    
    [self.threeTimeInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.dayLabel.mas_trailing).offset(6);
        make.centerY.equalTo(self.timeLabel);
        make.width.equalTo(@80);
        make.height.equalTo(@20);
    }];
}


-(NSArray*)lefSecToTime:(NSInteger)time{
    NSInteger day =  time/60/60/24;
    time  -= (day*24*60*60);
    
    NSInteger hour =  time/60/60;
    time -= hour*60*60;
    
    NSInteger min =  time/60;
    time -= min*60;
    
    NSInteger sec =  time;
    NSArray* result;
    
    result =  @[
        @(day).stringValue,
        @(hour).stringValue,
        @(min).stringValue,
        @(sec).stringValue];
    
    return result;
}


- (void)canPayStatus:(NSString *)serverTime{
    
    self.viewModel.isCanPay = YES;
    
    self.timeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    self.timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];
    self.timeLabel.text = OrderListL(@"isv_order_list_pre_progress_end_time_tip2");
    [self.threeTimeInfoView setHidden:NO];
    [self.twoTimeInfoView setHidden:YES];
    
    NSString *currentDateStr = serverTime;
//    
//    NSString *subTime = [JDISVCommonTools getSubStringWithPayTime:self.viewModel.payEndTime serverTime:currentDateStr] ;
//    
    NSDate* date = [NSDate date];
    NSTimeInterval sec = [self.viewModel.payEndDate timeIntervalSinceDate:date];
//    NSArray *timeClipArray = [subTime componentsSeparatedByString:@":"];
    NSArray *timeClipArray = [self lefSecToTime:sec];
    
    NSString *day = timeClipArray[0];
    NSString *h = timeClipArray[1];
    NSString *m = timeClipArray[2];
    NSString *s = timeClipArray[3];
    
    if([day intValue] == 0 && [h intValue] == 0 && [m intValue] == 0 && [m intValue] == 0 && [s intValue] == 0){
        [[NSNotificationCenter defaultCenter] postNotificationName:@"" object:nil];
    }
    //尾款已超时
    
    if(h.length == 1){
        h = [@"0" stringByAppendingString:h];
    }
    if(m.length == 1){
        m = [@"0" stringByAppendingString:m];
    }
    if(s.length == 1){
        s = [@"0" stringByAppendingString:s];
    }
    NSString *newTimeStr = [[@"" stringByAppendingString:day] stringByAppendingString:OrderListL(@"isv_order_data_format6")];
    if ([day intValue] == 0) {
        newTimeStr = @"";
    }
    
    [self.dayLabel setHidden:NO];
    [self.dayLabel setValue:newTimeStr forKey:@"text"];
    [self.dayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.timeLabel.mas_trailing);
        make.centerY.equalTo(self.timeLabel);
        make.height.equalTo(@20);
        CGSize textSize = [self.dayLabel.text sizeWithAttributes:@{NSFontAttributeName:self.dayLabel.font}];
        self.dayLabel.mj_size = textSize;
        if (self.dayLabel.text.length>0) {
            make.width.equalTo(@(textSize.width+6));
        }else{
            make.width.equalTo(@(0));
        }
    }];
    
    [self.threeTimeInfoView updateHour:h min:m sec:s];
    
    [self.threeTimeInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.dayLabel.mas_trailing).offset(6);
        make.centerY.equalTo(self.timeLabel);
        make.width.equalTo(@80);
        make.height.equalTo(@20);
    }];
    
//    NSTimeInterval endInterVel = [self.viewModel.payEndTime integerValue] /1000;
//    
//    NSDate *endPaymentDate = [NSDate dateWithTimeIntervalSince1970:endInterVel];
//    
//    NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[currentDateStr integerValue]/1000];
//    NSComparisonResult result = [currentDate compare:endPaymentDate];
//    if(result == NSOrderedDescending){
    if( sec <= 0 ){
        [self.threeTimeInfoView setHidden:YES];
        [self.twoTimeInfoView setHidden:YES];
        [self.dayLabel setHidden:YES];
        self.timeView.backgroundColor = UIColor.clearColor;
        self.timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.timeLabel.text = OrderListL(@"isv_order_pre_end_pay_cancel_tip");
        self.viewModel.isCanPay = NO;
        
    }
    
    if (self.viewModel.isCanPay) {
        
        
        [self.paymentBtn renderB3];
        self.paymentBtn.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
    }else{
        [self.paymentBtn renderB3];
        self.paymentBtn.userInteractionEnabled = NO;
        [self.paymentBtn jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
        [self.paymentBtn jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKey(@"#C4") picker2:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateNormal];
        self.paymentBtn.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
    }
}



//判断是否可以支付尾款

//- (void)reloadcell:(NSNotification *)fication{
//
//    NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[fication.object integerValue]/1000];
//    NSDate *paytime = [NSDate dateWithTimeIntervalSince1970:[self.viewModel.paytime integerValue]/1000];
//
//    NSComparisonResult result = [currentDate compare:paytime];
//
//    if (result == NSOrderedDescending) {
//        [self canPayStatus:fication.object];//倒计时
//    }else{
//        [self canNotPayStatus];//尾款应付时间
//    }
//
//}

-(void)canNotPayStatus{
    //不可以付包括：未到付尾款时间 和 付尾款已超时
    
    [self.paymentBtn renderB3];
    self.paymentBtn.userInteractionEnabled = NO;
    [self.paymentBtn jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
    [self.paymentBtn jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKey(@"#C4") picker2:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateNormal];
    self.paymentBtn.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
    
    self.viewModel.isCanPay = NO;
    
    NSTimeInterval intervel = [self.viewModel.paytime doubleValue]/1000;
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:intervel];
    
    NSDateFormatter *dateFromatter = [[NSDateFormatter alloc]init];
    dateFromatter.dateFormat = @"yyyy-MM-dd HH:mm:ss";
    NSString *dateStr = [dateFromatter stringFromDate:date];
    
    NSString *month = [dateStr substringWithRange:NSMakeRange(5, 2)];
    NSString *day = [dateStr substringWithRange:NSMakeRange(8, 2)];
    NSString *hou = [dateStr substringWithRange:NSMakeRange(11, 2)];
    NSString *minute = [dateStr substringWithRange:NSMakeRange(14, 2)];
    
    NSTimeInterval endInterVel = [self.viewModel.payEndTime integerValue] /1000;
    NSDate *endPaymentDate = [NSDate dateWithTimeIntervalSince1970:endInterVel];
    NSString *currentDateStr = [JDISVCommonTools getNowTimeTimestamp];
    NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[currentDateStr integerValue]/1000];
    NSComparisonResult result = [currentDate compare:endPaymentDate];
    if(result == NSOrderedDescending){
        [self.threeTimeInfoView setHidden:YES];
        [self.twoTimeInfoView setHidden:YES];
        [self.dayLabel setHidden:YES];
        self.timeView.backgroundColor = UIColor.clearColor;
        self.timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.timeLabel.text = OrderListL(@"isv_order_pre_end_pay_cancel_tip");
        self.viewModel.isCanPay = NO;
        return;
    }
    
    //时、分干掉
    NSString *time = [NSString stringWithFormat:OrderListL(@"isv_order_list_pre_progress_end_time_tip"),month,day,hou,minute];
    //时间 天
    self.timeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    self.timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];
    self.timeLabel.text = time;
    
//    [self.twoTimeInfoView setHidden:NO];
//    [self.twoTimeInfoView updateHour:hou min:minute];
//    [self.threeTimeInfoView setHidden:YES];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.01 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.twoTimeInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.timeLabel.mas_trailing).offset(4);
            
            make.height.equalTo(@20);
            make.centerY.equalTo(self.timeLabel);
        }];
    });
    
}

- (void)updateWithViewModel:(JDISVOrderListCardViewModel *)viewModel {
    [self.timeLabel setHidden:YES];
    [self.timeView setHidden:YES];
    [self.threeTimeInfoView setHidden:YES];
    [self.twoTimeInfoView setHidden:YES];
    [self.dayLabel setHidden:YES];
    self.lijiPayTimeLabelBG.hidden = YES;
    
    self.viewModel = viewModel;
    
    if (viewModel.shopName.length > 0) {
        self.shopIcon.hidden = NO;
        self.arrow.hidden = NO;
        self.shopNameLabel.hidden = NO;
        _shopNameLabel.text = viewModel.shopName;
    } else {
        self.shopIcon.hidden = YES;
        self.arrow.hidden = YES;
        self.shopNameLabel.hidden = YES;
    }
    
    _statusLabel.text = viewModel.statusName;
    //    _priceLabel.text = [NSString stringWithFormat:@"￥%@", viewModel.totalPrice];
    [_orderPriceLabel configTextWithPrice:viewModel.totalPrice.doubleValue type:(KAPriceTypeP3) colorType:@"#C7"];
    _countLabel.text = [NSString stringWithFormat:OrderListL(@"isv_order_order_list_product_count"), viewModel.totalCount];
    
    if (viewModel.statusColor == JDISVOrderListCardStatusColorRed) {
        _statusLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C9");
    } else {
        _statusLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    }
    
    if (viewModel.showRemoveButton) {
        _removeButton.hidden = NO;
        _verticalGrain.hidden = NO;
        [_statusLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(-(18+20+6+1+7));
        }];
    } else {
        _removeButton.hidden = YES;
        _verticalGrain.hidden = YES;
        CGFloat Width = ceil([_statusLabel.text jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(CGFLOAT_MAX, 20)].width);
        [_statusLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(-18);
            make.width.mas_equalTo(Width);
        }];
        [self.shopNameLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.lessThanOrEqualTo(@([UIScreen mainScreen].bounds.size.width-18-20-2-2-12-18-12*2-Width));
        }];
    }
    
    if (viewModel.productArray.count > 1) {
        _productContainer.hidden = YES;
        _collectionView.hidden = NO;
    } else {
        _productContainer.hidden = NO;
        _collectionView.hidden = YES;
        
        [_productImageView jdcd_setImage:viewModel.productArray.firstObject.imageUrlString placeHolder:[JDISV_RESOURCE_MANAGER imageWithImageType:(JDISVImageTypePlaceholderDefault)] contentMode:(UIViewContentModeScaleAspectFit) completion:nil];
        _productName.text = viewModel.productArray.firstObject.title;
        [_priceLabel configTextWithPrice:viewModel.productArray.firstObject.originalPrice.doubleValue type:(KAPriceTypeP3) colorType:@"#C7"];
    }
    [_collectionView reloadData];
    
    for (UIView *v in _stackView.arrangedSubviews) {
        [v removeFromSuperview];
    }
    // 拼团中 || 等待付尾款
    if ([self.viewModel.groupBuyStatus isEqualToString:@"101"]  || [self.viewModel.presaleStatus isEqualToString:@"1"]) {
        [self.timeLabel setHidden:NO];
        [self.timeView setHidden:NO];
        [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.timeView);
            make.leading.equalTo(self.timeView).offset(9);
//            make.trailing.equalTo(self.timeView).offset(-9);
            make.top.bottom.equalTo(self.timeView);
            make.width.lessThanOrEqualTo(@([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2) - 36.f - 18.f));
        }];
        if ([self.viewModel.groupBuyStatus isEqualToString:@"101"]) {
            
            [self.threeTimeInfoView setHidden:NO];
            [self.twoTimeInfoView setHidden:YES];
            [self.dayLabel setHidden:YES];
            
            [self showGroupBuyStatus:[JDISVCommonTools getNowTimeTimestamp]];
        }else if([self.viewModel.presaleStatus isEqualToString:@"1"]){
            if (viewModel.isCanPay == 1) {
                
                [self canPayStatus:[JDISVCommonTools getNowTimeTimestamp]];
            }else{
                
                //不可以付包括：未到付尾款时间 和 付尾款已超时
                [self canNotPayStatus];
            }
        }
        
        [self.timeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.mas_leading).offset(18);
            make.trailing.equalTo(self.mas_trailing).offset(-18);
            make.height.mas_greaterThanOrEqualTo(@33);
            //            make.top.equalTo(self.productContainer.mas_bottom).offset(9);
            make.top.equalTo(self.productContainer.mas_bottom).offset(10);
        }];
    }else{
        [self.timeLabel setHidden:YES];
        [self.timeView setHidden:YES];
        [self.threeTimeInfoView setHidden:YES];
        [self.twoTimeInfoView setHidden:YES];
    }
    
    if (viewModel.buttonArray.count) {
        [_stackView addArrangedSubview:[UIView new]];
        for (JDISVOrderListCardButtonModel *model in [viewModel.buttonArray reverseObjectEnumerator]) {
            UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//            if ([self.viewModel.presaleStatus isEqualToString:@"1"] && model.buttonInfoDTO.showLabelType == JDISVOrderListOrderActionFloorModelButtonTypePayLastMoney) {//支付尾款
//                self.paymentBtn = btn;
//            }
            [btn addTarget:self action:@selector(actionButtonOnClicked:) forControlEvents:(UIControlEventTouchUpInside)];
            btn.tag = [viewModel.buttonArray indexOfObject:model];
            [btn setTitle:model.actionTitle forState:UIControlStateNormal];
            CGFloat width = [model.actionTitle boundingRectWithSize:CGSizeMake(300, 15) options:(NSStringDrawingUsesLineFragmentOrigin) attributes:@{ NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] } context:nil].size.width;
            CGFloat rt = MAX(80, width + 20);
            [btn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(rt);
                make.height.mas_equalTo(30);
            }];
            
            if ([self.viewModel.presaleStatus isEqualToString:@"1"]) {
                //待付尾款，不展示立即支付倒计时
            } else if ((self.viewModel.stateType == ORDERListItemStateTypeWaitPay || [self.viewModel.presaleStatus isEqualToString:@"0"])) {
                //待支付或者待付定金
                //添加立即支付倒计时
//                NSString *payEndTime = @"1669579200000";
//                if (!self.viewModel.lijiPayEndTime) {
//                    self.viewModel.lijiPayEndTime = @([JDISVCommonTools getNowTimeTimestamp].doubleValue+10*1000).stringValue;
//                }
                  NSString *paymentTime = @"";
                  paymentTime = self.viewModel.remainingPaymentTime;
//                  if ([self.viewModel.presaleStatus isEqualToString:@"0"]){
//                      paymentTime = self.viewModel.paytime;
//                  }
                NSString *currentTimeintervalStr = [JDISVCommonTools getNowTimeTimestamp];
                NSTimeInterval endInterVel = [paymentTime integerValue] /1000;
                NSDate *endPaymentDate = [NSDate dateWithTimeIntervalSince1970:endInterVel];
                NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[currentTimeintervalStr integerValue]/1000];
                NSComparisonResult result = [currentDate compare:endPaymentDate];
                if(result == NSOrderedDescending){
                    //endTime < currentTime
                    self.lijiPayTimeLabelBG.hidden = YES;
                }else{
                    NSString *day_h_m_s = [JDISVCommonTools getDay_H_M_S_WithEndTime:paymentTime];
                    NSString *text = [NSString stringWithFormat:OrderListL(@"isv_order_auto_close"),day_h_m_s];
                    if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                        text = [text stringByReplacingOccurrencesOfString:@" " withString:@""];
                        text = [NSString stringWithFormat:@"%@  ",text];
                    }
                    self.lijiPayTimeLabel.text = text;
                    self.lijiPayTimeLabelBG.hidden = NO;
                    
                    CGFloat timeW = [text boundingRectWithSize:CGSizeMake(300, 15) options:(NSStringDrawingUsesLineFragmentOrigin) attributes:@{ NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] } context:nil].size.width;
                    timeW = timeW + 35;
                    ///MARK: 6.20 P0UI修改，固定倒计时展示宽度
                    timeW = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2) - 36.f - rt + 15;
                    [self.lijiPayTimeLabelBG mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.width.mas_equalTo(timeW);
                        make.height.mas_equalTo(30);
                        make.trailing.equalTo(self.mas_trailing).offset(-(rt+18)+30/2);
                        make.bottom.mas_equalTo(_stackView.mas_bottom);
                    }];
                    [self layoutIfNeeded];
                    CGRectGetMaxX(self.lijiPayTimeLabelBG.frame);
                    
                    if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                        [self.lijiPayTimeLabelBG isv_floorRender_addRoundedCorners:UIRectCornerTopRight | UIRectCornerBottomRight withRadii:CGSizeMake(15, 15) viewRect:self.lijiPayTimeLabelBG.bounds];
                    }else{
                        [self.lijiPayTimeLabelBG isv_floorRender_addRoundedCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft withRadii:CGSizeMake(15, 15) viewRect:self.lijiPayTimeLabelBG.bounds];
                    }
                }
            }
            switch (model.type) {
                case JDISVOrderListCardButtonTypeDefault:
                    [btn renderB4];
                    btn.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightRegular);
                    break;
                case JDISVOrderListCardButtonTypeRedBorder:
                    [btn renderB5];
                    btn.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightRegular);
                    break;
                case JDISVOrderListCardButtonTypeRedFill:
                    [btn renderB3];
                    btn.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
                    break;
                case JDISVOrderListCardButtonTypeDisable:
                    [btn renderB3];
                    btn.userInteractionEnabled = NO;
                    [btn jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
                    [btn jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKey(@"#C4") picker2:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateNormal];
                    btn.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
                    break;;
                    
            }
            if (self.viewModel.stateType == ORDERListItemStateTypeWaitPay && model.buttonModel.showLabelId == ORDERListItemButtonTypeCancelOrder) {
                // 待支付状态下不显示取消订单按钮
            } else {
                [_stackView addArrangedSubview:btn];
            }
        }
    }
}

#pragma mark - action

- (void)shopButtonOnClicked:(id)sender {
    !self.shopButtonClickedBlock ?: self.shopButtonClickedBlock();
}

- (void)removeButtonOnClicked:(id)sender {
    !self.removeButtonClickedBlock ?: self.removeButtonClickedBlock();
}

- (void)actionButtonOnClicked:(UIButton *)sender {
    NSUInteger idx = sender.tag;
    JDISVOrderListCardButtonModel *model = [self.viewModel.buttonArray objectAtIndex:idx];
    !self.actionButtonClickedBlock ?: self.actionButtonClickedBlock(idx, model);
}

- (void)collectionTapGesture:(UITapGestureRecognizer *)sender {
    if (self.cardViewClickedBlock) {
        self.cardViewClickedBlock();
    }
}

#pragma mark - delegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return _viewModel.productArray.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVOrderListCardImageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"cell" forIndexPath:indexPath];
    if (indexPath.item <= _viewModel.productArray.count - 1) {
        JDISVOrderListCardProductModel *model = [_viewModel.productArray objectAtIndex:indexPath.item];
        [cell configureProductModel:model];
    }
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    
}



@end
