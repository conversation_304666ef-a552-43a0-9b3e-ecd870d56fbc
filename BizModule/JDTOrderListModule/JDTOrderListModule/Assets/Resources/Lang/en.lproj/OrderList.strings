/* 
  OrderList.strings
  JDISVOrderListSDKModule

  Created by ext.chenhongyu12 on 2023/4/20.
  Copyright © 2023 张令浩. All rights reserved.
*/
"isv_order_pre_pay_last" = "Remainings";
"isv_address_detail_exit_dialog_save" = "Save";
"isv_order_order_id" = "Order No.: ";
"isv_order_detail_title_order_actual_pay_money" = "Actual payment:";
"isv_order_edit_address_tip" = "Modifying the address may affect logistics efficiency, and the actual distribution time shall prevail.";
"isv_order_qrcode_timeout_tip" = "Automatic refund at due date";
"isv_order_pre_end_pay_cancel_tip" = "Presale product. Failed to pay final payment on time. The order has been cancelled.";
"isv_address_detail_specific_label" = "Detailed address";
"isv_order_tab_title_all_order" = "All";
"isv_order_list_pre_progress_end_time_tip2" = "Payment countdown ";
"isv_order_service_chat" = "Contact customer service";
"isv_order_pre_progress_end_time_tip" = "Time to pay balance";
"isv_order_tab_title_wait_to_pay" = "Unpaid";
"isv_address_cannot_exit_tip" = "Address cannot be modified";
"isv_order_auto_receive" = "Automatically confirm the remaining %@";
"isv_order_pre_progress_end" = "Unpaid balance";
"isv_order_pre_progress_end_price" = "%@ balance has been reduced by (%@)";
"isv_order_pre_pay_state_pending" = "Unpaid";
"isv_address_detail_location_label" = "Region";
"isv_order_pre_start_price_text" = "Deposit: ";
"isv_order_edit_tip" = "Please note that the order can only be modified once";
"isv_order_edit_receive" = "Modify Order";
"isv_address_detail_region_hint" = "Province, city, district, county, township, town, etc.";
"isv_order_tab_title_finished" = "Completed";
"isv_order_detail_title_order_need_end" = "Balance Payable: ";
"isv_order_pre_progress_send" = "Ship";
"isv_order_un_team_tip_part3" = "more people are required for successful sharebuy. The remaining time is ";
"isv_order_un_team_tip_part1" = "more are required";
"isv_order_un_team_tip_part2" = "%d people";
"isv_order_auto_close" = "Close automatically after %@";
"isv_order_pre_pay_state_end" = "Paid";
"isv_order_detail_title_order_pay_way" = "Payment Method";
"isv_order_pre_end_price_text" = "Balance: ";
"isv_order_edit_confirm" = "Are you sure to modify";
"isv_order_receive_title" = "Receive information";
"isv_order_tab_title_wait_receive" = "Out for Delivery";
"isv_order_team_detail" = "Sharebuy details";
"isv_order_order_list_product_count" = "%ld item(s)";
"isv_order_team_ing" = "Share-buying";
"isv_order_pre_progress_start" = "Deposit paid";
"isv_order_pickup_period_time_title" = "Validity period: %@-%@";
"isv_order_pickup_code_not_settle" = "Awaiting Sending Code";
"isv_address_detail_phone_hint" = "Please fill in the mobile phone number of receiver";
"isv_order_pre_progress_end2" = "Balance paid";
"isv_order_detail_title_order_need_pay_money" = "Account payable:";
"isv_address_detail_phone_label" = "Mobile phone number";
"isv_order_detail_title_order_id" = "Order No.";
"isv_order_list_un_team_tip" = "%@ more people required, end in ";
"isv_address_detail_phone_illegal" = "Only %@-digit numbers can be entered";
"isv_order_pre_progress_end_no_time" = "Balance timed out";
"isv_address_detail_complete_hint" = "Complete the detailed address";
"isv_order_detail_title_buyer_message" = "Message from buyers";
"isv_address_detail_exit_dialog_discard" = "Do not save";
"isv_order_detail_title_order_discount" = "Total Discount Amount";
"isv_order_detail_title_order_need_bargin" = "Deposit Payable:";
"isv_order_data_format10" = "%@ day %@ hour %@ minute %@ second";
"isv_order_detail_title_order_product_total_price" = "Total Amount of Products";
"isv_address_detail_specific_hint" = "Street, building number, etc.";
"isv_order_order_logistics_title" = "Order Tracking";
"isv_order_data_format11" = "%@ hour %@ minute %@ second";
"isv_order_count_prefix" = "Qty: %d";
"isv_order_team_fail" = "Sharebuy Failed";
"isv_order_tab_title_cancelled" = "Cancelled";
"isv_order_team_text" = "Share-Buy";
"isv_address_detail_name_illegal" = "Please enter a maximum of %@ Chinese or English characters or numbers under the receiver";
"isv_order_data_format7" = "%02d : %02d : %02d";
//"isv_order_data_format6" = "%d day %02d: %02d : %02d";
"isv_order_data_format6" = "D";
"isv_order_data_format5" = "%02d:%02d:%02d";
"isv_order_data_format4" = "%d day %02d:%02d:%02d";
"isv_order_detail_title_order_time" = "Order time";
"isv_order_data_format3" = "%@ day %@ hour %@ minute %@ second";
"isv_order_data_format2" = "HH:mm  MM/dd";
"isv_order_data_format1" = "HH:mm:ss  MM/dd";
"isv_order_copy_success" = "Copy succeeded";
"isv_order_title_order_number" = "Order No.:";
"isv_address_detail_exit_dialog_title" = "Exit editing or not";
"isv_order_data_format9" = "%@ hour %@ minute %@ second";
"isv_order_data_format8" = "%d day %@ hour %@ minute";
"isv_address_detail_specific_illegal" = "Only 6~%@ characters can be entered for the detailed address";
"isv_order_team_suc" = "Share-Buy Succeeded";
"isv_order_pre_progress_end_price_text" = "Balance payable";
"isv_address_cannot_edit_level" = "This multilevel associative address cannot be modified";
"isv_address_detail_name_hint" = "Please enter the name of receiver";
"isv_order_text_copy" = "Copy";
"isv_address_detail_name_label" = "Receiver";
"isv_order_title_logistics_name" = "Carrier:";
"isv_order_detail_title_order_freight" = "Freight";
"isv_address_detail_phone_empty" = "Please enter the phone number";
"isv_order_detail_title_order_delivery_way" = "Mode of distribution ";
"isv_order_pickup_store_info" = "Business hours: %@-  Telephone No.: %@-";
"isv_order_used_store" = "Available physical stores";
"isv_order_edit_user_info" = "Modify receiver information";
"isv_order_detail_title" = "Order details";
"isv_address_detail_region_illegal" = "Please select the region";
"isv_order_detail_btn_more_operation" = "More";
"isv_order_title_logistics_number" = "Tracking number: ";
"ka_order_button_delete_request_failed" = "Order deletion failed, please try again later";
"ka_order_customer_pickup_code_title" = "Pickup code:";
"ka_order_dialog_confirm_receipt_title" = "Have you received the goods?";
"ka_order_tab_title_finished" = "Completed";
"ka_order_no_map_installed" = "You may not have installed the map application on your device. Please check";
"ka_order_submit_error" = "Request failed, please try again later";
"ka_order_cancel_order_cancelling" = "The order is being cancelled for you, please wait";
"ka_order_pickup_business_time_title" = "Business hours: %@";
"ka_order_title_order_type" = "Order Type";
"ka_order_dialog_btn_cancel" = "Cancel";
"ka_order_tab_title_cancelled" = "Canceled";
"ka_order_pickup_code_not_settle" = "Physical store stocking";
"ka_order_submit_edit_success" = "Modified successfully. The order address will be updated later";
"ka_order_list_title" = "Orders";
"ka_order_dialog_btn_confirm" = "OK";
"ka_order_list_empty_tip" = "You have no relevant orders yet";
"ka_order_submit_success" = "Submitted successfully";
"ka_order_dialog_delay_no_num" = "Remaining number of deferred acceptance operations: 0";
"ka_order_button_ensure_receive_request_failed" = "Receipt confirmation failed, please try again later";
"ka_order_customer_pick_up_place" = "Pickup site: ";
"ka_order_dialog_delay_title" = "Do you want to defer acceptance?";
"ka_order_button_cancel_request_success" = "The order is being cancelled for you, please wait";
"ka_order_submit_edit_error" = "Order modification failed";
"ka_order_dialog_cancel_order_title" = "Do you want to cancel the order?";
"ka_order_dialog_delete_order_content" = "Once deleted, the order cannot be recovered";
"ka_order_type_life" = "Local";
"ka_order_select_reset" = "Reset";
"ka_order_comfirm" = "OK";
"ka_order_tab_title_wait_to_pay" = "Unpaid";
"ka_order_pickup_code_period_title" = "Deadline: %@";
"ka_order_dialog_delay_tips" = "Remaining number of deferred acceptance operations: %@ times. The order completion time will be extended by %@ days after deferred acceptance operation";
"ka_order_button_pay_request_failed" = "Payment failed, please try again later";
"ka_order_title_aftersale" = "After-sales order";
"ka_order_button_cancel_request_failed" = "Cancellation failed, please try again later";
"ka_order_button_delete_request_success" = "Order deleted";
"ka_order_dialog_delete_order_title" = "Are you sure you want to delete this order?";
"ka_order_button_buy_again_failed" = "Failed to add the product to shopping cart!";
"ka_order_select_submit" = "Submit";
"ka_order_tab_title_all_order" = "All";
"ka_order_check_invoice" = "View Invoice";
"ka_order_title_order" = "Order";
"isv_order_pay_now" = "Pay Now";
"isv_order_screen" = "Filter";
"isv_order_load_failed" = "Page loading failed";
"isv_order_reply" = "Retry ";
"isv_order_empty" = "No order information available";
"isv_order_invited" = "Invite Friends";
"isv_order_confirm_receipt" = "Confirm Receipt";
"isv_order_comfirm_receipt_des" = "Have you received the products?";
"isv_order_comfirm_receipt_end" = "Confirm receipt successfully";
"isv_order_buy_again" = "Buy Again";
"isv_order_comment_title" = "Review";
"isv_order_delete_order" = "Delete Order";
"isv_order_delete_comfirm" = "Delete";
"isv_order_delayed_receipt" = "Deferred Acceptance";
"isv_order_comfirm_btn_text" = "Confirm";
"isv_order_pay_deposit" = "Pay Deposit";
"isv_order_waiting_for_self_pickup" = "Awaiting Pickup";
"isv_order_to_be_shipped" = "Unshipped";
"isv_order_processing" = "Processing";
"isv_order_evaluate_sun_exposure" = "Review and Share";
"isv_order_apply_for_invoicing" = "Apply for Invoicing";
"isv_order_failed_interface_exception" = "Failed. API exception";
"isv_order_account_balance" = "Your account balance is insufficient. Do you want top it up?";
"isv_order_pay_done_refresh" = "Your order has been paid, please refresh the page";
"isv_order_total_amount_limit" = "Your order amount has exceeded the total limit for a single order. Please modify it and try again";
"isv_order_network_error" = "Network exception, please try again later";
"isv_order_joingroupBuy_info" = "Sharebuy details";
"isv_order_joinGroupBuy_outTime" = "Sharebuy timed out";
"isv_order_day_unit" = "Day";
"isv_order_written_off" = "Write-off completed";
"isv_order_expired" = "Expired";
"isv_order_locked" = "Locked";
"isv_order_pre_progress_price" = "Deposit: SAR%.2f";
"isv_order_scale_zero" = "Save 0.00";
"isv_order_pre_end_price_text_1" = "Balance: SAR%.2f%@";
"isv_order_pre_end_price_text_2" = "Balance: SAR%.2f";
"isv_order_send_after" = "Shipped before %@";
"isv_order_time_out_order_cancel" = "The order will be cancelled automatically if you failed to pick up product before the deadline. Please pick up your products at the pickup site immediately";
"isv_order_i_known" = "I know";
"isv_order_stop_doing_business" = "Closure";
"isv_order_address_title" = "URL";
"isv_order_phone_tip" = "Only 11-digit number can be entered for phone number";
"isv_order_address_placeholder" = "Please enter the detailed address";
"isv_order_comfirm_failed" = "Submission failed ";
"isv_order_track" = "Order tracking";
"isv_order_store_address" = "Physical store address: %@";
"isv_order_list_pre_progress_end_time_tip" = "Balance Payment Time: %@-%@ %@:%@";
"ka_order_dialog_delay_tips" = "Remaining number of deferred acceptance operations: %@ times. The order completion time will be extended by %@ days after deferred acceptance operation";
"isv_order_share_sharebuy_detail" = "Share-Buy Details";
"isv_order_order_price_lable" = "总金额:";
"ka_order_cancel_order_title" = "取消订单";
