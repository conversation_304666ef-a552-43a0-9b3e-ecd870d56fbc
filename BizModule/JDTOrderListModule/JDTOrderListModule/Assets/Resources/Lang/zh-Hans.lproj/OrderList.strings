/* 
  OrderList.strings
  JDISVOrderListSDKModule

  Created by ext.chenhongyu12 on 2023/4/20.
  Copyright © 2023 张令浩. All rights reserved.
*/


"isv_order_data_format6" = "天";
"isv_order_tab_title_all_order" = "全部";
"isv_order_tab_title_wait_to_pay" = "待支付";
"isv_order_tab_title_wait_receive" = "待收货";
"isv_order_tab_title_finished" = "已完成";
"isv_order_tab_title_cancelled" = "已取消";
"isv_order_auto_close" = "%@ 后自动关闭 ";
"isv_order_list_un_team_tip" = "还差%@人成团，剩余 ";
"isv_order_list_pre_progress_end_time_tip2" = "支付倒计时：";
"isv_order_pre_end_pay_cancel_tip" = "预售商品，未按时支付尾款，订单已取消";
"isv_order_joinGroupBuy_outTime" = "拼团超时";
"isv_order_list_pre_progress_end_time_tip" = "尾款支付时间: %@-%@ %@:%@";
"isv_order_order_list_product_count" = "共%ld件";
"isv_order_pay_now" = "立即支付";
"isv_order_screen" = "筛选";
"isv_order_load_failed" = "页面加载失败";
"isv_order_reply" = "重试";
"isv_order_empty" = "暂无订单信息";
"ka_order_title_order_type" = "订单类型";
"ka_order_type_life" = "本地生活";
"ka_order_select_reset" = "重置";
"ka_order_select_submit" = "提交";
"ka_order_list_title" = "我的订单";
"isv_order_invited" = "邀请好友";
"isv_order_confirm_receipt" = "确认收货";
"isv_order_comfirm_receipt_des" = "确认收到货了吗？";
"ka_order_dialog_btn_cancel" = "取消";
"isv_order_comfirm_receipt_end" = "已确认收货";
"ka_order_button_ensure_receive_request_failed" = "确认收货失败，请稍后再试";
"isv_order_buy_again" = "再次购买";
"ka_order_button_buy_again_failed" = "商品加入购物车失败！";
"isv_order_comment_title" = "评价";
"isv_order_delete_order" = "删除订单";
"ka_order_dialog_delete_order_title" = "确定要删除此订单？";
"ka_order_dialog_delete_order_content" = "订单删除后不可恢复";
"isv_order_delete_comfirm" = "删除";
"ka_order_button_delete_request_success" = "订单已删除";
"ka_order_button_delete_request_failed" = "订单删除失败，请稍后重试";
"ka_order_button_pay_request_failed" = "支付失败，请稍后再试";
"isv_order_delayed_receipt" = "延迟收货";
"ka_order_submit_error" = "请求失败，请稍后重试";
"ka_order_dialog_delay_title" = "确认延迟收货操作?";
"ka_order_dialog_delay_tips" = "当前可延迟收货操作次数剩余 %@ 次，操作延迟收货后订单完成时间将会延长 %@ 天";
"isv_order_comfirm_btn_text" = "确认";
"ka_order_dialog_delay_no_num" = "当前可延迟收货操作次数剩余 0 次";
"isv_order_pay_deposit" = "支付定金";
"isv_order_waiting_for_self_pickup" = "等待自提";
"isv_order_to_be_shipped" = "待发货";
"isv_order_processing" = "处理中";
"isv_order_evaluate_sun_exposure" = "评价晒单";
"ka_order_check_invoice" = "查看发票";
"isv_order_apply_for_invoicing" = "申请开票";
"isv_order_share_sharebuy_detail" = "拼团详情";
"isv_order_order_price_lable" = "总金额:";
"ka_order_button_cancel_request_failed" = "取消订单失败，请稍后再试";
"ka_order_cancel_order_cancelling" = "正在为您取消订单，请稍后";
"ka_order_cancel_order_title" = "取消订单";
