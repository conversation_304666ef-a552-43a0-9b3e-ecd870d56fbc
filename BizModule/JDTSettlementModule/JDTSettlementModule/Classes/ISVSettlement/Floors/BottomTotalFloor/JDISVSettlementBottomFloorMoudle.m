//
//  JDISVSettlementBottomFloorMoudle.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import "JDISVSettlementBottomFloorMoudle.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

#import "JDISVSettlementBottomFloor.h"

#import "JDISVSettlementBottomFloorModel.h"
#import "JDISVSettlementBottomFloorAmountItemModel.h"
#import "JDISVSettlementBottomFloorMainModel.h"
#import "JDISVSettlementOrderSkuModel.h"
#import "JDISVSettlementOrderModel.h"

#import "JDISVSettlementOrderStockOutSkuViewModel.h"

#import "NSAttributedString+JDISVSettlementBottom.h"
#import "NSBundle+JDISVSettlement.h"
#import "JDISVServingTrayModel.h"
#import "CHECKDetailModel.h"
@import JDTInfrastructureModule;

JDISVRegisterFloorModule(KaCheckTotalFloor, JDISVSettlementBottomFloorMoudle);

static NSString * const kJDISVSettmentBottomFloorPrefix = @"defaultProductAmountFloor";

@interface JDISVSettlementBottomFloorMoudle()

@property (nonatomic, strong) JDISVSettlementBottomFloorAmountItemModel *amountModel;

@property (nonatomic, strong) NSDictionary *allOriginData;

@property (nonatomic, copy) NSString *checkoutId;

@property (nonatomic, copy) NSString *addressId;

@end

@implementation JDISVSettlementBottomFloorMoudle

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.height = 0;
        NSString *title = SettlementLan(@"checkout_total_prefix") ;
        if (ISPresell) {
            title = SettlementLan(@"checkout_pre_sale_sum_label") ;
        }
        self.titleAttributedString = [[NSAttributedString alloc] ka_settlement_bottom_initWithString:title colorKey:@"#C7" fontKey:@"#T7" weight:UIFontWeightMedium];
    }
    return self;
}

- (UIView *)floorView {
    UIView *v = [[NSBundle jdisvSettlement_bundle] loadNibNamed:@"JDISVSettlementBottomFloor" owner:nil options:nil].firstObject;
    return v;
}

- (CGFloat)floorHeight {
    return self.height;
}
- (BOOL)isDeliverFloor {
    return NO;
}

- (BOOL)ignoreCorner {
    return YES;
}

- (JDISVFloorType)floorType{
    return JDISVFloorTypeBottomFixFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *allFloorData = data[@"data"] ? : @{};
    
    // TODO:Juice 区分入口，见：JDISVSettlementSourceType
    self.sourceType = [allFloorData[@"source"] integerValue];
    self.checkoutId = allFloorData[@"checkoutId"];
    self.addressId = allFloorData[@"userAddress"][@"addressId"];
    
    CHECKAmountSummaryModel *amount = [CHECKAmountSummaryModel yy_modelWithDictionary:allFloorData[@"amountSummary"]];
    NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
    [priceAttributedString KA_renderWithPriceStr:amount.totalAmount.stringValue ? : @"" type:KAPriceTypeP2 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    self.amountAttributedString = [priceAttributedString copy];
    CGFloat safeBottom = 0.f;
    if ([UIApplication sharedApplication].jdt_currentKeyWindow.safeAreaInsets.bottom > 0) {
        safeBottom = [UIApplication sharedApplication].jdt_currentKeyWindow.safeAreaInsets.bottom;
    }
    
    self.height = 50.f + safeBottom;
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    self.allOriginData = resultInfo;
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];

    self.sourceType = [resultInfo[@"source"] integerValue];
    self.commonModel = commonModel;
    
    // 转楼层模型
    NSMutableArray *floorsModels = [NSMutableArray array];
    for (NSDictionary *floorData in floorsArray) {
        JDISVSettlementBottomFloorModel *floorModel = [JDISVSettlementBottomFloorModel yy_modelWithDictionary:floorData];
        [floorsModels addObject:floorModel];
    }
    
    [self updateViewModelWith:floorsModels
                      commond:commonModel];
}

- (void)updateViewModelWith:(NSArray *)floorDatas 
                    commond:(JDISVFloorCommonModel *)commonModel{
    if (floorDatas == nil) {
        self.amountModel = nil;
    }
    NSString *amount = @"";
    for (JDISVSettlementBottomFloorModel *floorData in floorDatas) {
        if ([floorData.uuid containsString:kJDISVSettmentBottomFloorPrefix]) {
            JDISVSettlementBottomFloorAmountItemModel *amountItemModel = [JDISVSettlementBottomFloorAmountItemModel yy_modelWithDictionary:floorData.info];
            if ([amountItemModel.floorType isEqualToString:@"2"]) {
                // type:2 应付金额
                self.amountModel = amountItemModel;
                amount = [_amountModel.amount copy] ;
                break;
            }
        }
    }
    if (ISPresell && ISFirstMoenyFlag) {
        for (JDISVSettlementBottomFloorModel * floorData in floorDatas) {
            if ([floorData.uuid containsString:@"presaleTotalDepositFloor"]) {
                NSDictionary *depositFloorDic = floorData.info[@"C-M#presaleTotalDepositFloor&basic"];
                if (depositFloorDic && [depositFloorDic isKindOfClass:[NSDictionary class]]) {
                    //应付金额：定金
                    amount = depositFloorDic[@"depositAmount"];
                }
                 break;
            }
        }
     }
    
    if (_amountModel) {
        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
        [priceAttributedString KA_renderWithPriceStr:amount ? : @"" type:KAPriceTypeP2 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
        self.amountAttributedString = [priceAttributedString copy];
        CGFloat safeBottom = 0.f;
        if (@available(iOS 11.0, *)) {
            if ([UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom > 0) {
                safeBottom = [UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom;
            }
        }
        NSNumber* isCod = commonModel.commonData[@"isCOD"];
        if(isCod.boolValue){
            commonModel.commonData[@"widgetPrice"]= @(0);
        }else{
            commonModel.commonData[@"widgetPrice"]= @(amount.floatValue);
        }
        [commonModel commonDataChange];
        self.height = 50.f + safeBottom;
    } else {
        self.height = 0;
        self.amountAttributedString = nil;
    }
}

//检查是否有虚拟子品无货
- (BOOL)checkHaveVirtualProductStockOutBySkuIds:(NSArray *)productModels{
    NSArray *floorsArray = [self.allOriginData objectForKey:@"floors"];
    NSDictionary *hierarchy = [self.allOriginData objectForKey:@"hierarchy"];
    NSString *rootKey = [self.allOriginData objectForKey:@"rootKey"];
    
    //查找所有的虚拟组套商品
    NSMutableArray *virtualproductFloorDatas = [[NSMutableArray alloc]init];
    for (NSDictionary *floorDic in floorsArray) {
        NSString *floorKey = floorDic[@"uuid"];
        if ([floorKey containsString:@"virtualProductFloor"]) {
            NSArray *virtualFloorKeys = (NSArray *)[hierarchy objectForKey:floorKey];
            for (NSString *virtualFloorKey in virtualFloorKeys) {
                if ([virtualFloorKey containsString:@"retailProductFloor"]) {
                    for (NSDictionary *floorData in floorsArray) {
                        if ([floorData[@"uuid"] isEqualToString:virtualFloorKey]) {
                            [virtualproductFloorDatas addObject:floorData];
                        }
                    }
                }
            }
        }
    }
    //再遍历存有虚拟商品的数组
    for (JDISVSettlementOrderStockOutSkuViewModel *stockoutSkuViewModel in productModels) {
        for (NSDictionary *floorData in virtualproductFloorDatas) {
            NSDictionary *info = floorData[@"info"] ? : @{};
            NSString *skuId = info[@"C-M#retailProductFloor&basic"][@"skuId"];
            if ([stockoutSkuViewModel.skuId isEqualToString:skuId]) {
                return YES;//无货商品中含有虚拟组套子品
            }
        }
    }
    
    return NO;
}

- (RACSignal *)loadSubmitOrderSignal2 {
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        NSDictionary *testParams = @{
            @"immediatelyBuy": @(NO),
            @"skuList": @[],
            @"addressId": self.addressId,
            @"payType": @"1",
            @"selectedVoucherIds": @[],
            @"remarkList": @[],
            @"source": @0, // 0代表购物车进入，1 代表商详进入
            @"checkoutId": (self.checkoutId.length > 0) ? self.checkoutId : @"",
            @"payMethod": @4, // 4 代表小程序
        };
        [[OOPNetworkManager sharedManager] POST:@"checkout/c/submit?apiCode=b2c.cbff.checkout.c.submit" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:@"提交订单失败"];
            } else {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    [subscriber sendNext:responseObject];
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:@"提交订单成功"];
                } else {
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:@"提交订单失败"];
                }
            }
        }];
        
        return nil;
    }];
}

// 提交订单
- (RACSignal *)loadSubmitOrderSignal {
    if (((NSNumber *)[self getCommonDataRouterPamars][@"presaleEndPayment"]).boolValue){
        //阿波罗不支持预售付尾款结算页提单，调结算中台提单接口
        @weakify(self)
        return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
            @strongify(self)
            NSString *orderId = [self getCommonDataRouterPamars][@"orderId"];
            [self requestPresaleEndPaymentSubmitOrderWithWithParam:orderId sourceOf:self.sourceType complete:^(NSDictionary * _Nonnull resultInfo, NSString *resultCode, NSError * _Nullable error) {
                if (error) {
                    // 错误提示
                    [subscriber sendError:error];
                } else {
                    // 处理提单逻辑
                    NSMutableDictionary *orderInfo = [NSMutableDictionary dictionary];
                    NSNumber *zeroOrder = resultInfo[@"zeroOrder"];
                    if (zeroOrder.boolValue) {
                        // 0元单
                        [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeZeroOrder)}];
                        if ([resultInfo[@"orderId"] jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"orderId": resultInfo[@"orderId"]}];
                        }
                    } else {
                        // 支付
                        [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypePay)}];
                        if ([orderId jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"orderId": orderId}];
                        }
                        if ([resultInfo[@"payId"] jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"payId": resultInfo[@"payId"]}];
                        }
                        if ([resultInfo[@"returnUrl"] jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"payUrl": resultInfo[@"returnUrl"]}];
                        }
                    }
                    [subscriber sendNext:orderInfo];
                }
            }];
            return nil;
        }];
    }else{
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *orderStrParam = [NSMutableDictionary dictionary];
        [orderStrParam addEntriesFromDictionary:@{@"remarks": [self getCommonDataRemarkArrayParam]}];
        [orderStrParam addEntriesFromDictionary:@{@"callbackUrl": @"https://www.jd.com"}];
        [self requestSubmitOrderWithWithParam:[orderStrParam copy] sourceOf:self.sourceType complete:^(JDISVSettlementBottomFloorMainModel * _Nonnull model, NSString *resultCode,NSString *message, NSError * _Nullable error) {
            BOOL submitSuccessMTA = NO;//提单失败，用于埋点
            if (error) {
                // 错误提示
                [subscriber sendError:error];
            } else {
                // 处理提单逻辑
                NSMutableDictionary *orderInfo = [NSMutableDictionary dictionary];
                if ([resultCode isEqualToString:@"CSBB-DM@1013-CD@2.500102"]) {
                    // 部分无货:CSBB-DM@1013-CD@2.500102
                    NSArray *stockoutSkuModels = [self partStockOutSkuViewModelsWith:model];
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeStockOutPart)}];
                    if (stockoutSkuModels && stockoutSkuModels.count > 0) {
                        [orderInfo addEntriesFromDictionary:@{@"stockoutSkuViewModelArray": stockoutSkuModels}];
                    }
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                } else if ([resultCode isEqualToString:@"VCYBB2C-DM@1013-CD@2.500101"] || [resultCode isEqualToString:@"CSBB-DM@1013-CD@2.500101"]) {
                    // 全部无货:VCYBB2C-DM@1013-CD@2.500101 CSBB-DM@1013-CD@2.500101
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeStockOutAll)}];
                    
                    if ([self allStockOutSkuParam]) {
                        [orderInfo addEntriesFromDictionary:@{@"stockoutParam": [self allStockOutSkuParam]}];
                    }
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                } else if ([resultCode isEqualToString:@"300001"]//拼团结算页提单错误
                            || [resultCode isEqualToString:@"300005"]
                            || [resultCode isEqualToString:@"300007"]
                            || [resultCode isEqualToString:@"300008"]
                            || [resultCode isEqualToString:@"300009"]
                            || [resultCode isEqualToString:@"300010"]
                            || [resultCode isEqualToString:@"3000013"]
                            || [resultCode isEqualToString:@"CSBB-DM@1035-CD@2.402005"]) {
                    
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeGroupBuyError)}];
                    [orderInfo addEntriesFromDictionary:@{@"message":message ? : SettlementLan(@"setllement_opation_error") }];
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                } else if ([resultCode isEqualToString:@"CSBB-DM@1035-CD@2.402003"]) {
                    // pin限购:CSBB-DM@1035-CD@2.402003
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypePinLimitBuy)}];
                    [orderInfo addEntriesFromDictionary:@{@"message":message ? : SettlementLan(@"setllement_opation_error")}];
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                } else if ([resultCode isEqualToString:@"CSBB-DM@1035-CD@2.405002"]) {
                    // 最小限购:CSBB-DM@1035-CD@2.405002
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeMinLimitBuy)}];
                    [orderInfo addEntriesFromDictionary:@{@"message":message ? : SettlementLan(@"setllement_opation_error")}];
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                } else if ([resultCode isEqualToString:@"CSBB-DM@1035-CD@2.405003"]) {
                    // 最大限购:CSBB-DM@1035-CD@2.405003
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeMaxLimitBuy)}];
                    [orderInfo addEntriesFromDictionary:@{@"message":message ? : SettlementLan(@"setllement_opation_error")}];
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                } else if ([resultCode isEqualToString:@"10002"]
                           || [resultCode isEqualToString:@"CSBB-DM@101710-CD@2.300011"]){//优惠码不可用
                    // 优惠码不可用coed码: COUPON_CODE_ERROR:['10002','CSBB-DM@101710-CD@2.300011']
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeCodeUnUse)}];
                    [orderInfo addEntriesFromDictionary:@{@"message":message ? : SettlementLan(@"setllement_opation_error")}];
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                }else if ([resultCode isEqualToString:@"HJM-DM@1029-CD@2.200004"]){//托盘商品限购
                    //托盘商品 限购
                    NSArray *partList = [NSArray array];
                    for (JDISVSettlementBottomFloorModel *floor in model.floors) {
                        if ([floor.uuid containsString:@"defaultOrderFloor"]){
                            partList = [floor.partList copy];
                            break;
                        }
                    }
                    if (partList.count > 0){
                        if (partList.count == [JDISVSettlementAmountFloorNetService sharedService].mainProductCount){
                            [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeAllLimit)}];
                        }else{
                            NSMutableArray *dataList = [NSMutableArray array];
                            for (JDISVSettlementBottomFloorPartListItemModel *info in partList) {
                                [dataList addObject:[JDISVServingTrayModel yy_modelWithDictionary:info.yy_modelToJSONObject]];
                            }
                            [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeSomeLimit)}];
                            [orderInfo setObject:dataList forKey:@"partList"];
                        }
                    }else{
                        [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeAllLimit)}];
                    }
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                }else if ([resultCode isEqualToString:@"CSBB-DM@101611-CD@2.200100"] ||
                          [resultCode isEqualToString:@"CSBB-DM@101611-CD@2.200018"]) {
                    // COD 提单报错
                    [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeCODPaymentError)}];
                    [orderInfo addEntriesFromDictionary:@{@"message":message ? : SettlementLan(@"setllement_opation_error")}];
                    [subscriber sendNext:[NSDictionary dictionaryWithDictionary:orderInfo]];
                }
                else {
                    // 成功: 支付 0元单
                    submitSuccessMTA = YES;
                    JDISVSettlementOrderModel *orderModel;
                    for (JDISVSettlementBottomFloorModel *floorData in model.floors) {
                        if ([floorData.uuid containsString:@"defaultOrderFloor"]) {
                            orderModel = [JDISVSettlementOrderModel yy_modelWithDictionary:floorData.info];
                            break;
                        }
                    }
                    
                    if (orderModel.zeroOrderFlag) {
                        // 0元单
                        [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypeZeroOrder)}];
                        if ([orderModel.orderId jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"orderId": orderModel.orderId}];
                        }
                        
                    } else {
                        // 支付
                        [orderInfo addEntriesFromDictionary:@{@"resultType": @(JDISVSettlementBottomFloorMoudleSubmitResultTypePay)}];
                        if ([orderModel.orderId jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"orderId": orderModel.orderId}];
                        }
                        if ([orderModel.payId jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"payId": orderModel.payId}];
                        }
                        if ([orderModel.payUrl jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"payUrl": orderModel.payUrl}];
                        }
                        if ([orderModel.idPaymentType jdcd_validateString]) {
                            [orderInfo addEntriesFromDictionary:@{@"idPaymentType": orderModel.idPaymentType}];
                        }
                        [orderInfo addEntriesFromDictionary:@{@"payUrl": orderModel.payUrl}];
                    }
                    [subscriber sendNext:orderInfo];
                }
            }
            
            if (!submitSuccessMTA){
                NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
                NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
                [JDRouter openURL:url arg:@{@"name":@"PlaceOrderError"} error:nil completion:nil];//提单失败埋点
            }
        }];
        return nil;
    }];
    }
}

- (RACSignal *)bindOrderAndSelectInvoiceSignalWithOrderId:(NSString *)orderId{
    if (![self getCommonDataInvoiceData])
        return nil;
    
    
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
//        NSMutableDictionary *orderStrParam = [NSMutableDictionary dictionary];
//        [orderStrParam addEntriesFromDictionary:@{@"remarks": [self getCommonDataRemarkArrayParam]}];
//        [orderStrParam addEntriesFromDictionary:@{@"callbackUrl": @"https://www.jd.com"}];
        [self bindOrderAndSelectInvoiceWithParam:orderId sourceOf:self.sourceType complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
                // 错误提示
//                [subscriber sendError:error];
            } else {
//                NSMutableDictionary *orderInfo = [NSMutableDictionary dictionary];
//                [subscriber sendNext:orderInfo];
            }
        }];
        return nil;
    }];
}

/// 预售付尾款结算，提交订单
/// @param orderStrParam 提单参数
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestPresaleEndPaymentSubmitOrderWithWithParam:(NSString *)orderId
                                                        sourceOf:(NSInteger)source
                                                       complete:(void(^)(NSDictionary *resultInfo, NSString *resultCode, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    NSString *couponIds = [self.commonModel.commonData[@"couponIds"] ? : @[] componentsJoinedByString:@","];//,为分隔符
    if ([couponIds jdcd_validateString]) {
        [param setObject:couponIds forKey:@"selectCouponIds"];
    }
    [param setObject:orderId ? : @"" forKey:@"orderId"];
    [param setObject:self.amountModel.amount ? : @"" forKey:@"payAmount"];
    [param setObject:@"3" forKey:@"sourceType"];//3（h5.ios.Android）
    
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"marketing_presale_balance_submit" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, @"-999", error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            NSNumber *success = (NSNumber *)[responseObject objectForKey:@"success"];

            if (success.boolValue) {
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"data"];
//                JDISVSettlementBottomFloorMainModel *model = [JDISVSettlementBottomFloorMainModel yy_modelWithDictionary:resultInfo];
                completeBlock(resultInfo, resultCode, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil, resultCode, resultError);
            }
        }
        
    }];
}

/// 提交订单 se_submitOrder
/// @param orderStrParam 提单参数
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSubmitOrderWithWithParam:(NSDictionary *)orderStrParam
                                                        sourceOf:(NSInteger)source
                                                       complete:(void(^)(JDISVSettlementBottomFloorMainModel *model, NSString *resultCode,NSString *message, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
        
    NSMutableDictionary *orderStrParams = [NSMutableDictionary dictionary];
    [orderStrParams addEntriesFromDictionary:orderStrParam];
    
    NSDictionary *groupBuyMap = [self getCommonDataGroupBuyPamars];
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
        [orderStrParams addEntriesFromDictionary:groupBuyMap];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
        NSDictionary *routerMap = [self getCommonDataRouterPamars];
        if (routerMap[@"payTypeForPresale"]) {
            //订单支付类型 1：全款支付  2：定金支付
            [orderStrParams setObject:routerMap[@"payTypeForPresale"] forKey:@"payTypeForPresale"];
        }
        NSString *mobile = [self getCommonDataUserMobile];
        if ([mobile jdcd_validateString]) {
            [orderStrParams setObject:mobile forKey:@"userMobile"];
        }
    }
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSNumber *deliveryAddressId = [defaults objectForKey:@"deliveryAddressId"];
    NSNumber *billingAddrId = [defaults objectForKey:@"billingAdrressId"];
//    if (!billingAddrId) {
//        NSLog(@"billingAddrId = %@",billingAddrId);
//    }
//    
//    if (!deliveryAddressId) {
//        NSLog(@"deliveryAddressId = %@",deliveryAddressId);
//    }
    [orderStrParams setValue:billingAddrId forKey:@"billAddressId"];
    [orderStrParams setValue:deliveryAddressId forKey:@"deliveryAddressId"];
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrParams}];
    
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_submitOrder" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, @"-999", nil,error);
        } else {
            
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"deliveryAddressId"];
            [defaults removeObjectForKey:@"billingAdrressId"];
            [defaults synchronize];
            
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && ([resultCode isEqualToString:@"C-DM@60-CD@1.100000"]
                                                     || [resultCode isEqualToString:@"CSBB-DM@1013-CD@2.500102"]
                                                     || [resultCode isEqualToString:@"VCYBB2C-DM@1013-CD@2.500101"]
                                                     || [resultCode isEqualToString:@"CSBB-DM@1013-CD@2.500101"])) {
                // 提单成功 C-DM@60-CD@1.100000(支付 zero) CSBB-DM@1013-CD@2.500102(部分无货) VCYBB2C-DM@1013-CD@2.500101(全部无货)
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                JDISVSettlementBottomFloorMainModel *model = [JDISVSettlementBottomFloorMainModel yy_modelWithDictionary:resultInfo];
                completeBlock(model, resultCode,nil, nil);
            } else if ([resultCode jdcd_validateString] && ([resultCode isEqualToString:@"300001"]
                                                            || [resultCode isEqualToString:@"300005"]
                                                            || [resultCode isEqualToString:@"300007"]
                                                            || [resultCode isEqualToString:@"300008"]
                                                            || [resultCode isEqualToString:@"300009"]
                                                            || [resultCode isEqualToString:@"300010"]
                                                            || [resultCode isEqualToString:@"3000013"]
                                                            || [resultCode isEqualToString:@"CSBB-DM@1035-CD@2.402005"]
                                                            || [resultCode isEqualToString:@"CSBB-DM@1035-CD@2.405002"]//限购(最大最小限购)
                                                            || [resultCode isEqualToString:@"CSBB-DM@1035-CD@2.405003"]//限购(最大最小限购)
                                                            || [resultCode isEqualToString:@"CSBB-DM@1035-CD@2.402003"]//限购(pin限购等)
                                                            || [resultCode isEqualToString:@"10002"]// 优惠码不可用coed码
                                                            || [resultCode isEqualToString:@"CSBB-DM@101710-CD@2.300011"]// 优惠码不可用coed码
                                                            || [resultCode isEqualToString:@"CSBB-DM@101611-CD@2.200100"]//COD 提单报错
                                                            || [resultCode isEqualToString:@"CSBB-DM@101611-CD@2.200018"])){//COD 提单报错
//                300001：该商品拼团活动不存在，请重新按照原价购买
//                300005：该商品拼团活动不存在，请重新按照原价购买
//                300007：该拼团商品最少购买N件，最多购买M件
//                300008：该拼团任务已满员，请新建拼团任务
//                300009：你已参加过此团，不能再次下单
//                300010：该拼团任务已过期，请新建拼团任务
//                3000013：该拼团商品无活动库存
                completeBlock(nil, resultCode, message,nil);
            }else if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"HJM-DM@1029-CD@2.200004"]){
                ////托盘商商品限购
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                JDISVSettlementBottomFloorMainModel *model = [JDISVSettlementBottomFloorMainModel yy_modelWithDictionary:resultInfo];
                completeBlock(model, resultCode,nil, nil);
            }
            else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
//                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
//                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    
//                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_opation_error") ;
//                    }
                    if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"CSBB-DM@101612-CD@2.200021"]){
                        message = SettlementLan(@"checkout_shipment_please_select_store");
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil, resultCode, nil,resultError);
            }
        }
        
    }];
}

/// 绑定发票和订单
- (NSURLSessionDataTask * _Nonnull)bindOrderAndSelectInvoiceWithParam:(NSString *)orderId
                                                        sourceOf:(NSInteger)source
                                                       complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSDictionary *invoiceMap = [self getCommonDataInvoiceData];
    NSNumber *invoiceId = invoiceMap[@"id"];
    [param setObject:invoiceId ? : @"" forKey:@"invoiceId"];
    [param setObject:orderId ? : @"" forKey:@"orderId"];
    
    //拼团场景下入参
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }
        
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"orderInvoiceRelation_save_color" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            NSString *success = (NSString *)[responseObject objectForKey:@"success"];
            
            if (success.boolValue) {
                // 正常
//                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"data"];
//                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
//                completeBlock(nil,resultError);
            }
        }
    }];
}

// 获取部分无货商品
- (NSArray *)partStockOutSkuViewModelsWith:(JDISVSettlementBottomFloorMainModel *)model {
    NSMutableArray *tempArr = [NSMutableArray array];
    for (JDISVSettlementBottomFloorModel *floorData in model.floors) {
        if ([floorData.uuid containsString:@"defaultOrderFloor"]) {
            
            for (JDISVSettlementBottomFloorPartListItemModel *itemModel in floorData.partList ) {
                if ([itemModel.type.nameSpace isEqualToString:@"core.trade-FLR#balance.order-P#skuInfo"]) {
                    JDISVSettlementOrderSkuModel *skuModel = [JDISVSettlementOrderSkuModel yy_modelWithDictionary:itemModel.info];
                    JDISVSettlementOrderStockOutSkuViewModel *itemViewModel = [[JDISVSettlementOrderStockOutSkuViewModel alloc] init];
                    itemViewModel.imageDomain = [self imageDomain];
                    [itemViewModel updateWithData:skuModel forType:0];
                    [tempArr addObject:itemViewModel];
                }
            }
        }
    }
    
    return [NSArray arrayWithArray:tempArr];
}

// 获取全部无货Router参数
- (NSArray *)allStockOutSkuParam {
    NSArray *allStockOutSkuParamArray = [self.commonModel.commonData objectForKey:@"allStockOutSkuParamArray"] ? : [NSArray array];
    return allStockOutSkuParamArray;
}

- (NSArray *)getCommonDataRemarkArrayParam {
    NSArray *remarkArray = [self.commonModel.commonData objectForKey:@"remarkArray"] ? : [NSArray array];
    
    return remarkArray;
}

- (NSDictionary *)getCommonDataGroupBuyPamars {
    NSDictionary *remarkArray = [self.commonModel.commonData objectForKey:@"groupBuyParams"];
    
    return remarkArray;
}

- (NSDictionary *)getCommonDataRouterPamars {
    NSDictionary *remarkArray = [self.commonModel.commonData objectForKey:@"routerParams"];
    
    return remarkArray;
}

- (NSString *)getCommonDataUserMobile {
    NSString *mobile = [self.commonModel.commonData objectForKey:@"presaleUserMobile"];
    
    return mobile;
}

- (NSDictionary *)getCommonDataInvoiceData {
    NSDictionary *dic = [self.commonModel.commonData objectForKey:@"selectedInvoiceData"];
    
    return dic;
}

- (NSString *)imageDomain {
    NSString *imageDomain = [self.commonModel.commonData objectForKey:@"imageDomain"] ? : @"";
    return imageDomain;
}

/// 检查支付金额是否为0元，并且选择了COD
- (BOOL)payAmountAndCODCheck{
    if (self.amountModel.amount.floatValue > 0){
        return NO;
    }else{
        if ([JDISVSettlementAmountFloorNetService sharedService].ISCODPayType){
            return YES;
        }else{
            return NO;
        }
    }
}
@end
