//
//  CHECKPriceAndDiscountFloor.m
//  JDTSettlementModule
//
//  Created by lvchenzhu.1 on 2025/6/11.
//

#import "CHECKPriceAndDiscountFloor.h"
#import "JDISVSettlementAmountFloorModule.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "CHECKPADPriceTypeCell.h"
#import "CHECKPADDiscountTypeCell.h"
#import "CHECKPriceAndDiscountCellModel.h"
#import "CHECKCouponListController.h"

@import JDISVFloorRenderModule;

@interface CHECKPriceAndDiscountFloor () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) JDISVSettlementAmountFloorModule *viewModel;

@end

@implementation CHECKPriceAndDiscountFloor

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    [self.contentView addSubview:self.tableView];
    self.tableView.scrollEnabled = NO;
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.tableView registerClass:[CHECKPADPriceTypeCell class] forCellReuseIdentifier:@"CHECKPADPriceTypeCell"];
    [self.tableView registerClass:[CHECKPADDiscountTypeCell class] forCellReuseIdentifier:@"CHECKPADDiscountTypeCell"];
    
    [self.tableView reloadData];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.cellDataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    CHECKPriceAndDiscountCellModel *cellModel = self.viewModel.cellDataArr[indexPath.row];
    __kindof UITableViewCell *cell;
    switch (cellModel.cellType) {
        case CHECKPriceAndDiscountCellTypeTotalAmount: {
            CHECKPADPriceTypeCell *totalPriceCell = [tableView dequeueReusableCellWithIdentifier:@"CHECKPADPriceTypeCell" forIndexPath:indexPath];
            totalPriceCell.type = cellModel.cellType;
            totalPriceCell.amountModel = cellModel.data;
            cell = totalPriceCell;
            break;
        }
        case CHECKPriceAndDiscountCellTypeShippingFee: {
            CHECKPADPriceTypeCell *shippingFeeCell = [tableView dequeueReusableCellWithIdentifier:@"CHECKPADPriceTypeCell" forIndexPath:indexPath];
            shippingFeeCell.type = cellModel.cellType;
            shippingFeeCell.amountModel = cellModel.data;
            cell = shippingFeeCell;
            break;
        }
        case CHECKPriceAndDiscountCellTypeDiscountCoupon: {
            CHECKPADDiscountTypeCell *discountCell = [tableView dequeueReusableCellWithIdentifier:@"CHECKPADDiscountTypeCell" forIndexPath:indexPath];
            discountCell.type = cellModel.cellType;
            discountCell.counponModel = cellModel.data;
            cell = discountCell;
            break;
        }
        default:
            break;
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

#pragma mark - UITableViewDelegate
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    CHECKPriceAndDiscountCellModel *cellModel = self.viewModel.cellDataArr[indexPath.row];
    return cellModel.cellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    CHECKPriceAndDiscountCellModel *cellModel = self.viewModel.cellDataArr[indexPath.row];
    if (cellModel.cellType == CHECKPriceAndDiscountCellTypeDiscountCoupon) {
        if ([cellModel.data isKindOfClass:[CHECKCouponModel class]]) {
            JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVCHECKShowCouponList"];
            action.value = (CHECKCouponModel *)cellModel.data;
            [self isv_sendAction:action];
        }
    }
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"JDISVCHECKShowCouponList"]) {
        CHECKCouponListController *couponListVC = [[CHECKCouponListController alloc] init];
        couponListVC.coupon = (CHECKCouponModel *)action.value;
        KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:couponListVC presentingViewController:controller];
        presentationVC.type = KAFloatLayerTypeCustom;
        couponListVC.transitioningDelegate = presentationVC;
        [controller presentViewController:couponListVC animated:YES completion:nil];
    }
    return YES;
}

#pragma mark - Getter and Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    return _tableView;
}

@end
