//
//  JDISVKAAfterSaleApplyListCell.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleApplyListCell.h"

#import "JDISVKAAfterSaleUtils.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVKAAfterSaleAction.h"

#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
@interface JDISVKAAfterSaleApplyListCell()

@property (weak, nonatomic) IBOutlet UIView *mainView;

@property (weak, nonatomic) IBOutlet UILabel *applyIDLabel;
@property (weak, nonatomic) IBOutlet UILabel *applyIDValue;
@property (weak, nonatomic) IBOutlet UIImageView *commodityImageView;
@property (weak, nonatomic) IBOutlet UILabel *commodityName;
@property (weak, nonatomic) IBOutlet UILabel *commodityCountLabel;
@property (weak, nonatomic) IBOutlet UILabel *commodityCountValue;
@property (weak, nonatomic) IBOutlet UIView *applyReasonView;
@property (weak, nonatomic) IBOutlet UILabel *applyReasonLabel;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImageView;
@property (weak, nonatomic) IBOutlet UIButton *replaceNewButton;
@property (weak, nonatomic) IBOutlet UIButton *returnButton;
@property (weak, nonatomic) IBOutlet UIButton *returnDamageButton;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *replaceNewButtonWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *returnButtonWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *returnDamageButtonWidth;

@property (nonatomic, strong) ASApplyListItemModel *model;

@property (nonatomic, strong) ASApplyListItemSkuItemModel *skuItem;

#pragma mark 约束

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewLeftConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewBottomConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewTopConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewRightConstraint;


@end

@implementation JDISVKAAfterSaleApplyListCell

- (void)awakeFromNib{
    [super awakeFromNib];
    [self setCell];
}


- (void)setCell{
    [self configCellColorAndFont];
}

- (void)config:(ASApplyListItemModel *)model {
    self.model = model;
    self.skuItem = model.skuList.firstObject;;
}

- (void)render:(NSInteger)index{
//    if([self.model.orderId isEqualToString:@"339967"]){
//        int n = 2;
//    }
    [self.rightArrowImageView setHidden:YES];
    self.applyIDValue.text = self.model.orderId;
    self.commodityName.text = self.skuItem.skuName;
    self.commodityCountValue.text = [NSString stringWithFormat:@"%ld", self.skuItem.skuCount];
    [self.commodityImageView jdcd_setImage:[PlatformService getCompleteImageUrl:self.skuItem.skuImg] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    
    [self.returnButton setHidden:YES];
    [self.replaceNewButton setHidden:YES];
    [self.returnDamageButton setHidden:YES];
    // 可以申请售后 / Can apply for after-sales service
//    self.model.canApply = YES;
    if (self.skuItem.canApply) {
        [self.applyReasonView setHidden:true];
        
        for (ASApplyListItemSkuItemServiceTypeModel *serviceType in self.skuItem.serviceTypeList) {
            if (serviceType.serviceType == 10) {//退货
                [self.returnButton setHidden:NO];
                [self.returnButton setTitle:Lang(@"after_sale_return") forState:UIControlStateNormal];
                self.returnButtonWidth.constant = [self.returnButton jdcd_getSize].width+25;

                if (serviceType.valid == 0) {
                    [self.returnButton jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateNormal];

                }else{
                    [self.returnButton jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
                }
                [self.returnButton setEnabled:YES];

            } else if(serviceType.serviceType == 20) {//换货
                [self.replaceNewButton setHidden:NO];
                [self.replaceNewButton setTitle:Lang(@"after_sale_bt_record_exchange") forState:UIControlStateNormal];
                self.replaceNewButtonWidth.constant = [self.replaceNewButton jdcd_getSize].width+25;

                if (serviceType.valid == 0) {
                    [self.replaceNewButton jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateNormal];

                }else{
                    [self.replaceNewButton jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];

                }
                [self.replaceNewButton setEnabled:YES];

            } else if(30 == serviceType.serviceType) { // 坏品售后 Defective product after-sales service
                [self.returnDamageButton setHidden:NO];
                [self.returnDamageButton setTitle:Lang(@"after_sale_bt_damage_product_service") forState:UIControlStateNormal];
                [self.returnDamageButton mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.trailing.mas_equalTo(self.returnButton.mas_trailing);
                }];
                self.returnDamageButtonWidth.constant = [self.returnDamageButton jdcd_getSize].width+25;

                if (serviceType.valid == 0) {
                    [self.returnDamageButton jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateNormal];
                } else {
                    [self.returnDamageButton jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
                }
                [self.returnDamageButton setEnabled:YES];
            }
        }
    }else{
        [self.returnButton setHidden:YES];
        [self.replaceNewButton setHidden:YES];
        [self.returnDamageButton setHidden:YES];
        if (self.skuItem.reasonText && self.skuItem.reasonText.length > 0) {
            self.applyReasonLabel.text = self.skuItem.reasonText;
            [self.applyReasonView setHidden:NO];
            if (self.skuItem.afsServiceId.integerValue > 0) {
                [self.rightArrowImageView setHidden:NO];
            }
        }else{
            [self.applyReasonView setHidden:true];
        }
    }
    
//    self.returnButtonWidth.constant = [self.returnButton jdcd_getSize].width+25;
//    self.replaceNewButtonWidth.constant = [self.replaceNewButton jdcd_getSize].width+25;
//    self.returnDamageButtonWidth.constant = [self.returnDamageButton jdcd_getSize].width+25;
}

- (void)configCellColorAndFont{
    [self.applyReasonView jd_addTapAction:@selector(clickApplyReasonView) withTarget:self];
    self.backgroundColor = [UIColor clearColor];
    self.applyIDLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightSemibold);
    self.applyIDLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.applyIDLabel.text = Lang(@"after_sale_apply_id");
    self.applyIDValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyIDValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.commodityName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.commodityCountLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityCountLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.commodityCountValue.jdisv_fontPicker = JDISVJDFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityCountValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.commodityCountLabel.text = Lang(@"after_sale_count");
    self.applyReasonLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyReasonLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.applyReasonView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    self.rightArrowImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
//    if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
//        self.rightArrowImageView.image = [[UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]] JDCDRTL];
//    }
    
    [self.returnButton renderB4];
    [self.replaceNewButton renderB4];
    [self.returnDamageButton renderB4];
    
    [self.returnButton addTarget:self action:@selector(clickReturnButton) forControlEvents:UIControlEventTouchUpInside];
    [self.replaceNewButton addTarget:self action:@selector(clickReplaceNewButton) forControlEvents:UIControlEventTouchUpInside];
    [self.returnDamageButton addTarget:self action:@selector(clickRetrunDamageButton) forControlEvents:UIControlEventTouchUpInside];
    [self.returnButton setEnabled:NO];
    [self.replaceNewButton setEnabled:NO];
    [self.returnDamageButton setEnabled:NO];
    
    self.mainViewLeftConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.mainViewTopConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    self.mainViewBottomConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.mainViewRightConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    
    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.applyReasonView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    
    self.commodityImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.commodityImageView.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"] colorWithAlphaComponent:0.02];
}

- (void)clickReturnButton {
    ASApplyListItemSkuItemModel *skuItem = self.model.skuList.firstObject;
    NSInteger currentServiceType = 0;
    for (ASApplyListItemSkuItemServiceTypeModel *serviceType in skuItem.serviceTypeList) {
        if (serviceType.serviceType == 10) {//退货
            currentServiceType = serviceType.serviceType;
            if (serviceType.valid == 0) {
//                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:Lang(@"after_sale_list_no_return")];
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:serviceType.tips];
                return;
            }
        }
    }
    
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithReturnCommodity];
    NSMutableDictionary *requestParams = [NSMutableDictionary dictionary];
    [requestParams setValue:self.model.orderId forKey:@"orderId"];
    [requestParams setValue:skuItem.skuId forKey:@"skuId"];
    [requestParams setValue:skuItem.skuUUid forKey:@"skuUUid"];
    [requestParams setValue:@(currentServiceType) forKey:@"serviceType"];
    action.sender = [requestParams copy];
    [self.delegate sendNext:action];
}

- (void)clickReplaceNewButton {
    
    NSInteger currentServiceType = 0;
    for (ASApplyListItemSkuItemServiceTypeModel *serviceType in self.skuItem.serviceTypeList) {
       if(serviceType.serviceType == 20) { // 换货
           currentServiceType = serviceType.serviceType;
           currentServiceType = serviceType.serviceType;
            if (serviceType.valid == 0) {
//                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:Lang(@"after_sale_list_no_exchange")];
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:serviceType.tips];

                return;
            }
        }
    }
    
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithReplaceNew];
    NSMutableDictionary *requestParams = [NSMutableDictionary dictionary];
    [requestParams setValue:self.model.orderId forKey:@"orderId"];
    [requestParams setValue:self.skuItem.skuId forKey:@"skuId"];
    [requestParams setValue:self.skuItem.skuUUid forKey:@"skuUUid"];
    [requestParams setValue:@(currentServiceType) forKey:@"serviceType"];
    action.sender = [requestParams copy];
    [self.delegate sendNext:action];
}

- (void)clickRetrunDamageButton {
    NSInteger currentServiceType = 0;
    for (ASApplyListItemSkuItemServiceTypeModel *serviceType in self.skuItem.serviceTypeList) {
       if(serviceType.serviceType == 30) {//换货
           currentServiceType = serviceType.serviceType;
            if (serviceType.valid == 0) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:serviceType.tips];
                return;
            }
        }
    }
    
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithReturnDamaged];
    NSMutableDictionary *requestParams = [NSMutableDictionary dictionary];
    [requestParams setValue:self.model.orderId forKey:@"orderId"];
    [requestParams setValue:self.skuItem.skuId forKey:@"skuId"];
    [requestParams setValue:self.skuItem.skuUUid forKey:@"skuUUid"];
    [requestParams setValue:@(currentServiceType) forKey:@"serviceType"];
    action.sender = [requestParams copy];
    [self.delegate sendNext:action];
}

- (void)clickApplyReasonView{
    if (self.skuItem.afsServiceId.integerValue > 0) {
        JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithClickRecordApplyDetailView];
        action.sender = self.skuItem.afsServiceId;
        [self.delegate sendNext:action];
    }
}

- (RACSubject *)delegate{
    if (!_delegate) {
        _delegate = [[RACSubject alloc] init];
    }
    return _delegate;
}

@end
