//
//  ASApplyListCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/8.
//

#import "ASApplyListCell.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVKAAfterSaleAction.h"

@import JDISVKAIconFontModule;

@interface ASApplyListCell ()

// 主容器
@property (nonatomic, strong) UIView *containerView;

// 店铺信息
@property (nonatomic, strong) UIImageView *shopImgView;
@property (nonatomic, strong) UILabel *shopNameLabel;
@property (nonatomic, strong) UIImageView *shopArrowImgView;

// 商品信息
@property (nonatomic, strong) UIImageView *productImageView;
@property (nonatomic, strong) UILabel *productNameLabel;
@property (nonatomic, strong) UILabel *quantityLabel;

// 售后状态提示
@property (nonatomic, strong) UIView *statusTipView;
@property (nonatomic, strong) UIImageView *statusTipImgView;
@property (nonatomic, strong) UILabel *statusTipLabel;

// 申请按钮
@property (nonatomic, strong) UIButton *applyButton;

// 进度信息视图（申请中状态使用）
@property (nonatomic, strong) UIView *progressView;
@property (nonatomic, strong) UILabel *progressLabel;
@property (nonatomic, strong) UILabel *progressDescLabel;
@property (nonatomic, strong) UIImageView *progressArrowImgView;

// 数据模型
@property (nonatomic, strong) ASApplyListItemModel *applyModel;
@property (nonatomic, strong) ASRecordListItemModel *recordModel;
@property (nonatomic, strong) ASApplyListItemSkuItemModel *skuItem;

@property (nonatomic, assign) ASListCellStyle curStyle;

@end

@implementation ASApplyListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
        [self setupConstraints];
    }
    return self;
}

- (void)configUI {
    self.backgroundColor = [UIColor clearColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    // 主容器
    self.containerView = [[UIView alloc] init];
    self.containerView.backgroundColor = [UIColor whiteColor];
    self.containerView.layer.cornerRadius = 4;
    self.containerView.layer.masksToBounds = YES;
    [self.contentView addSubview:self.containerView];

    // 店铺名称
    self.shopImgView = [[UIImageView alloc] init];
    self.shopImgView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C7", JDIF_ICON_SHOP_LINE, CGSizeMake(20, 20));
    [self.containerView addSubview:self.shopImgView];
    
    self.shopNameLabel = [[UILabel alloc] init];
    self.shopNameLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
    self.shopNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    [self.containerView addSubview:self.shopNameLabel];
    
    self.shopArrowImgView = [[UIImageView alloc] init];
    self.shopArrowImgView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C7", JDIF_ICON_ARROW_RIGHT_SMALL, CGSizeMake(12, 12));
    [self.containerView addSubview:self.shopArrowImgView];

    // 商品图片
    self.productImageView = [[UIImageView alloc] init];
    self.productImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.productImageView.layer.cornerRadius = 6;
    self.productImageView.layer.masksToBounds = YES;
    self.productImageView.backgroundColor = [UIColor whiteColor];
    [self.containerView addSubview:self.productImageView];

    // 商品名称
    self.productNameLabel = [[UILabel alloc] init];
    self.productNameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.productNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.productNameLabel.numberOfLines = 0;
    [self.containerView addSubview:self.productNameLabel];

    // 数量标签
    self.quantityLabel = [[UILabel alloc] init];
    self.quantityLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    [self.containerView addSubview:self.quantityLabel];

    // 状态提示
    self.statusTipView = [[UIView alloc] init];
    self.statusTipView.backgroundColor = [UIColor whiteColor];
    [self.containerView addSubview:self.statusTipView];
    
    self.statusTipImgView = [[UIImageView alloc] init];
    self.statusTipImgView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C12", JDIF_ICON_TIPS, CGSizeMake(18, 18));
    [self.statusTipView addSubview:self.statusTipImgView];
    
    self.statusTipLabel = [[UILabel alloc] init];
    self.statusTipLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.statusTipLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
    self.statusTipLabel.textAlignment = NSTextAlignmentLeft;
    self.statusTipLabel.numberOfLines = 1;
    [self.statusTipView addSubview:self.statusTipLabel];

    // 申请按钮
    self.applyButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.applyButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    [self.applyButton setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateNormal];
    [self.applyButton setTitleColor:[UIColor whiteColor] forState:UIControlStateDisabled];
    [self.applyButton jdcd_setBackgroundColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.applyButton jdcd_setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"] forState:UIControlStateDisabled];
    self.applyButton.layer.cornerRadius = 4;
    self.applyButton.layer.masksToBounds = YES;
    [self.applyButton addTarget:self action:@selector(applyButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.applyButton];

    // 进度信息视图
    self.progressView = [[UIView alloc] init];
    self.progressView.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"#F3F2F7"];
    self.progressView.layer.cornerRadius = 4;
    self.progressView.layer.masksToBounds = YES;
    [self.containerView addSubview:self.progressView];
    [self.progressView jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
        JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:ASActionTypeApplyMainClickProgressView];
        if (self.skuItem.afsServiceId.integerValue > 0) {
            action.value = self.skuItem.afsServiceId;
        } else {
            action.value = self.recordModel.afsServiceId;
        }
        [self.delegate sendNext:action];
    }];

    self.progressLabel = [[UILabel alloc] init];
    self.progressLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.progressLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.progressLabel.textAlignment = NSTextAlignmentLeft;
    [self.progressView addSubview:self.progressLabel];
    
    self.progressDescLabel = [[UILabel alloc] init];
    self.progressDescLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.progressDescLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.progressDescLabel.textAlignment = NSTextAlignmentLeft;
    [self.progressView addSubview:self.progressDescLabel];
    
    self.progressArrowImgView = [[UIImageView alloc] init];
    self.progressArrowImgView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C5", JDIF_ICON_ARROW_RIGHT_SMALL, CGSizeMake(12, 12));
    [self.progressView addSubview:self.progressArrowImgView];
}

- (void)setupConstraints {
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(8, 8, 8, 8));
    }];

    [self.shopImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.containerView).offset(18);
        make.top.mas_equalTo(self.containerView).offset(18);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];
    
    [self.shopNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.shopImgView);
        make.leading.mas_equalTo(self.shopImgView.mas_trailing).offset(2);
        make.trailing.mas_equalTo(self.shopArrowImgView.mas_leading).offset(-2);
        make.height.mas_equalTo(20);
    }];
    
    [self.shopArrowImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.shopNameLabel.mas_trailing).offset(2);
        make.trailing.mas_lessThanOrEqualTo(self.containerView).offset(-18);
        make.centerY.mas_equalTo(self.shopNameLabel);
        make.size.mas_equalTo(CGSizeMake(12, 12));
    }];

    [self.productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.shopImgView);
        make.top.mas_equalTo(self.shopNameLabel.mas_bottom).offset(22);
        make.width.height.mas_equalTo(80);
    }];

    [self.productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productImageView);
        make.leading.mas_equalTo(self.productImageView.mas_trailing).offset(12);
        make.trailing.mas_equalTo(self.containerView).offset(-18);
        make.height.mas_lessThanOrEqualTo(42);
    }];

    [self.quantityLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.productImageView.mas_bottom).offset(-5);
        make.leading.mas_equalTo(self.productNameLabel);
        make.trailing.mas_equalTo(self.productNameLabel);
        make.height.mas_equalTo(20);
    }];
    
    [self.statusTipImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.statusTipView);
        make.top.bottom.mas_equalTo(self.statusTipView);
        make.width.mas_equalTo(18);
    }];
    
    [self.statusTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.statusTipImgView.mas_trailing).offset(2);
        make.top.bottom.trailing.mas_equalTo(self.statusTipView);
    }];
    
    [self.progressArrowImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(self.progressView).offset(-8);
        make.centerY.mas_equalTo(self.progressView);
        make.size.mas_equalTo(CGSizeMake(12, 12));
    }];
}

#pragma mark - Public Methods
- (void)configWithApplyModel:(ASApplyListItemModel *)model cellStyle:(ASListCellStyle)style {
    self.applyModel = model;
    self.recordModel = nil;
    self.skuItem = model.skuList.firstObject;
    [self render:style];
}
- (void)configWithRecordModel:(ASRecordListItemModel *)model cellStyle:(ASListCellStyle)style {
    self.recordModel = model;
    self.applyModel = nil;
    self.skuItem = model.skuList.firstObject;
    [self render:style];
}

+ (CGFloat)heightWithCellStyle:(ASListCellStyle)cellStyle {
    // 基础高度：上边距(18) + 店铺(20) + 边距(22) + 商品信息(80) + 下边距(18) + containerView的上下间距(16)= 174
    CGFloat baseHeight = 174;

    switch (cellStyle) {
        case ASListCellStyleApplyBtnOnly: {
            // 只有申请按钮：基础高度 + 按钮高度(30) + 间距(12) = 200
            return baseHeight + 30 + 12;
        }
        case ASListCellStyleApplyBtnAndProgress: {
            // 进度信息 + 申请按钮：基础高度 + 间距(12) + 进度信息高度(30) + 间距(12) + 按钮高度(30) = 242
            return baseHeight + 12 + 30 + 12 + 30;
        }
        case ASListCellStyleApplyBtnDisabledAndTips: {
            // 禁用按钮 + 状态提示文字
            // 基础高度 + 间距(12) + 按钮高度(30，和提示文字等高)
            return baseHeight + 12 + 30;
        }
        case ASListCellStyleApplyNoBtnAndTips: {
            // 无按钮，只有状态提示
            // 基础高度 + 间距(12) + 提示文字高度(20)
            return baseHeight + 12 + 20;
        }
        case ASListCellStyleApplyNoBtnAndProgress: {
            // 申请中状态，显示进度信息
            // 间距(12) + 进度信息高度(32)
            return baseHeight + 12 + 32;
        }
        case ASListCellStyleRecordProgressNormal: {
            // 申请记录，正常进度
            // 间距(12) + 进度信息高度(44)
            return baseHeight + 12 + 44;
        }
        case ASListCellStyleRecordProgressReject: {
            // 申请记录，被驳回
            // 间距(12) + 进度信息高度(70)
            return baseHeight + 12 + 70;
        }
        default:
            return baseHeight;
    }
}

- (void)render:(ASListCellStyle)style {
    self.curStyle = style;
    // 店铺名称
    if (self.applyModel) {
        self.shopNameLabel.text = self.applyModel.venderInfo.shopName ?: @"";
    } else {
        self.shopNameLabel.text = self.recordModel.venderInfo.shopName ?: @"";
    }

    // 商品信息
    [self.productImageView jdcd_setImage:[PlatformService getCompleteImageUrl:self.skuItem.skuImg moduleType:@""]
                             placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault]
                             contentMode:UIViewContentModeScaleAspectFit];

    self.productNameLabel.text = self.skuItem.skuName ?: @"";
    NSString *prefix = @"数量 ";
    NSString *count = [NSString stringWithFormat:@"%ld", (long)self.skuItem.skuCount];
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@%@", prefix, count]];
    
    [attStr addAttributes:@{NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]} range:NSMakeRange(0, prefix.length)];
    [attStr addAttributes:@{NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]} range:NSMakeRange(prefix.length, count.length)];
    self.quantityLabel.attributedText = attStr;

    // 根据样式类型配置界面
    [self configUIForStyleType:style];
}

- (void)configUIForStyleType:(ASListCellStyle)styleType {
    // 先隐藏所有可变组件
    self.statusTipView.hidden = self.statusTipImgView.hidden = self.statusTipLabel.hidden = YES;
    self.progressView.hidden = self.progressLabel.hidden = self.progressDescLabel.hidden = self.progressArrowImgView.hidden = YES;
    self.applyButton.hidden = YES;

    switch (styleType) {
        case ASListCellStyleApplyBtnOnly: {
            [self configApplyButtonWithTitle:@"申请售后" enabled:YES];
            break;
        }
        case ASListCellStyleApplyBtnAndProgress: {
            [self configApplyButtonWithTitle:@"再次申请" enabled:YES];
            [self configProgressViewWithText:self.skuItem.reasonText];
            break;
        }
        case ASListCellStyleApplyBtnDisabledAndTips: {
            [self configApplyButtonWithTitle:@"申请售后" enabled:NO];
            [self configStatusTipWithText:self.skuItem.reasonText];
            break;
        }
        case ASListCellStyleApplyNoBtnAndTips: {
            [self configStatusTipWithText:self.skuItem.reasonText];
            break;
        }
        case ASListCellStyleApplyNoBtnAndProgress: {
            [self configProgressViewWithText:self.skuItem.reasonText];
            break;
        }
        case ASListCellStyleRecordProgressNormal: {
            [self configProgressViewWithText:self.recordModel.afsServiceStatusText];
            break;
        }
        case ASListCellStyleRecordProgressReject: {
            [self configProgressViewWithText:@"您的售后申请审核未通过"];
            break;
        }
        default:
            break;
    }

    // 更新约束
    [self updateConstraintsForStyleType:styleType];
}

#pragma mark - Private Config Methods

- (void)configApplyButtonWithTitle:(NSString *)title enabled:(BOOL)enabled {
    self.applyButton.hidden = NO;
    [self.applyButton setTitle:title forState:UIControlStateNormal];
    self.applyButton.enabled = enabled;

    if (enabled) {
        self.applyButton.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"].CGColor;
        self.applyButton.layer.borderWidth = 1;
    } else {
        self.applyButton.layer.borderColor = nil;
        self.applyButton.layer.borderWidth = 0;
    }
}

- (void)configStatusTipWithText:(NSString *)text {
    if (text.length > 0) {
        self.statusTipView.hidden = self.statusTipImgView.hidden = self.statusTipLabel.hidden = NO;
        self.statusTipLabel.text = text;
    }
}

- (void)configProgressViewWithText:(NSString *)text {
    self.progressView.hidden = self.progressLabel.hidden = self.progressArrowImgView.hidden = NO;
    if (self.curStyle == ASListCellStyleRecordProgressReject) {
        self.progressLabel.text = @"审核未通过";
        self.progressLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9-1"];
        self.progressDescLabel.hidden = NO;
        self.progressDescLabel.text = @"您的售后申请审核未通过";
    } else {
        self.progressDescLabel.hidden = YES;
        self.progressLabel.text = text;
        self.progressLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.progressDescLabel.hidden = YES;
    }
}

- (void)updateConstraintsForStyleType:(ASListCellStyle)styleType {
    switch (styleType) {
        case ASListCellStyleApplyBtnOnly: {
            // 只有申请按钮
            [self.applyButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.productImageView.mas_bottom).offset(12);
                make.trailing.mas_equalTo(self.containerView).offset(-18);
                make.width.mas_equalTo(80);
                make.height.mas_equalTo(30);
            }];
            break;
        }
        case ASListCellStyleApplyBtnAndProgress: {
            // 申请按钮 + 进度提示
            [self.progressView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.productImageView.mas_bottom).offset(12);
                make.leading.mas_equalTo(self.productImageView);
                make.trailing.mas_equalTo(self.productNameLabel);
                make.height.mas_equalTo(30);
            }];
            [self.progressLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.progressView).offset(12);
                make.top.bottom.mas_equalTo(self.progressView);
                make.trailing.mas_equalTo(self.progressArrowImgView.mas_leading).offset(-6);
            }];
            
            [self.applyButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.trailing.mas_equalTo(self.containerView);
                make.bottom.mas_equalTo(self.containerView).offset(-12);
                make.width.mas_equalTo(80);
                make.height.mas_equalTo(30);
            }];
            break;
        }
        case ASListCellStyleApplyBtnDisabledAndTips: {
            // 申请按钮 + 状态提示
            [self.statusTipView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.productImageView);
                make.top.mas_equalTo(self.productImageView.mas_bottom).offset(12);
                make.height.mas_equalTo(30);
                make.trailing.equalTo(self.applyButton.mas_leading).offset(-12);
            }];

            [self.applyButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.trailing.mas_equalTo(self.containerView);
                make.top.bottom.mas_equalTo(self.statusTipLabel);
                make.width.mas_equalTo(80);
                make.height.mas_equalTo(30);
            }];
            break;
        }
        case ASListCellStyleApplyNoBtnAndTips: {
            // 只有状态提示
            [self.statusTipView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.productImageView);
                make.trailing.mas_equalTo(self.containerView).offset(-18);
                make.top.mas_equalTo(self.productImageView.mas_bottom).offset(12);
                make.height.mas_equalTo(20);
            }];
            break;
        }
        case ASListCellStyleApplyNoBtnAndProgress: {
            // 进度信息视图
            [self.progressView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.productImageView);
                make.trailing.mas_equalTo(self.productNameLabel);
                make.top.mas_equalTo(self.productImageView.mas_bottom).offset(12);
                make.height.mas_equalTo(32);
            }];
            
            [self.progressLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.progressView).offset(12);
                make.top.bottom.mas_equalTo(self.progressView);
                make.trailing.mas_equalTo(self.progressArrowImgView.mas_leading).offset(-6);
            }];
            
            break;
        }
        case ASListCellStyleRecordProgressNormal: {
            [self.progressView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.productImageView);
                make.trailing.mas_equalTo(self.productNameLabel);
                make.top.mas_equalTo(self.productImageView.mas_bottom).offset(12);
                make.height.mas_equalTo(44);
            }];
            
            [self.progressLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.progressView).offset(12);
                make.top.bottom.mas_equalTo(self.progressView);
                make.trailing.mas_equalTo(self.progressArrowImgView.mas_leading).offset(-6);
            }];
            break;
        }
        case ASListCellStyleRecordProgressReject: {
            [self.progressView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.productImageView);
                make.trailing.mas_equalTo(self.productNameLabel);
                make.top.mas_equalTo(self.productImageView.mas_bottom).offset(12);
                make.height.mas_equalTo(70);
            }];
            
            [self.progressLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.progressView).offset(12);
                make.top.mas_equalTo(self.progressView).offset(12);
                make.bottom.equalTo(self.progressView.mas_centerY);
                make.trailing.mas_equalTo(self.progressArrowImgView.mas_leading).offset(-6);
            }];
            
            [self.progressDescLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.progressView).offset(12);
                make.bottom.mas_equalTo(self.progressView).offset(-12);
                make.top.equalTo(self.progressView.mas_centerY);
                make.trailing.mas_equalTo(self.progressArrowImgView.mas_leading).offset(-6);
            }];
            break;
        }
        default:
            break;
    }
}

#pragma mark - Actions

- (void)applyButtonTapped {
    if (!self.skuItem.canApply) {
        return;
    }
    // 发送申请售后事件
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:ASActionTypeApplyMainClickApplyBtn];
//    action.sender = self.model;
    [self.delegate sendNext:action];
}

#pragma mark - Lazy Loading

- (RACSubject *)delegate {
    if (!_delegate) {
        _delegate = [RACSubject subject];
    }
    return _delegate;
}

@end
