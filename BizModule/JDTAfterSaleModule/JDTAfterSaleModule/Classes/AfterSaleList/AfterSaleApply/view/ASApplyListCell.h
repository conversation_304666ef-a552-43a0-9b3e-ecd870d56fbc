//
//  ASApplyListCell.h
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/8.
//

#import <UIKit/UIKit.h>
#import "ASApplyListModel.h"

@class RACSubject;

typedef NS_ENUM(NSUInteger, ASApplyListCellStyleType) {
    ASApplyListCellStyleTypeApplyBtnOnly,              // 仅显示申请按钮
    ASApplyListCellStyleTypeApplyBtnDisabledAndTips,   // 申请按钮显示但禁用以及提示信息(超过售后期)
    ASApplyListCellStyleTypeApplyBtnAndProgress,       // 申请按钮和进度信息（购买多件商品时，剩余商品可继续申请）
    ASApplyListCellStyleTypeNoApplyBtnAndTips,         // 无申请按钮，有提示信息（不支持申请售后）
    ASApplyListCellStyleTypeNoApplyBtnAndProgress      // 无申请按钮，有进度信息（申请中，显示一条进度信息）
};

NS_ASSUME_NONNULL_BEGIN

@interface ASApplyListCell : UITableViewCell

@property (nonatomic, strong) RACSubject *delegate;

/// 配置数据
/// @param model 数据模型
- (void)configWithModel:(ASApplyListItemModel *)model;

/// 渲染界面
/// @param index 索引
- (void)render:(NSInteger)index;

/// 获取Cell的样式类型
/// @param model 数据模型
+ (ASApplyListCellStyleType)getStyleTypeWithModel:(ASApplyListItemModel *)model;

/// 计算Cell高度
/// @param model 数据模型
/// @param styleType 样式类型
+ (CGFloat)getCellHeightWithModel:(ASApplyListItemModel *)model styleType:(ASApplyListCellStyleType)styleType;

@end

NS_ASSUME_NONNULL_END
