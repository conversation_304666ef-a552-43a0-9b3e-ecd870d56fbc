//
//  ASApplyListCell.h
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/8.
//

#import <UIKit/UIKit.h>
#import "ASApplyListModel.h"
#import "ASRecordListModel.h"

@class RACSubject;

typedef NS_ENUM(NSUInteger, ASListCellStyle) {
    ASListCellStyleApplyBtnOnly,              // 售后申请页：仅显示申请按钮
    ASListCellStyleApplyBtnDisabledAndTips,   // 售后申请页：申请按钮显示但禁用以及提示信息(超过售后期)
    ASListCellStyleApplyBtnAndProgress,       // 售后申请页：申请按钮和进度信息（购买多件商品时，剩余商品可继续申请）
    ASListCellStyleApplyNoBtnAndTips,         // 售后申请页：无申请按钮，有提示信息（不支持申请售后）
    ASListCellStyleApplyNoBtnAndProgress,     // 售后申请页：无申请按钮，有进度信息（申请中，显示一条进度信息）
    ASListCellStyleRecordProgressNormal,      // 申请记录页：正常进度（一行文字）
    ASListCellStyleRecordProgressReject       // 申请记录页：被驳回（两行文字）
};

NS_ASSUME_NONNULL_BEGIN

@interface ASApplyListCell : UITableViewCell

@property (nonatomic, strong) RACSubject *delegate;

/// 配置数据
/// @param model 数据模型
- (void)configWithApplyModel:(ASApplyListItemModel *)model cellStyle:(ASListCellStyle)style;
- (void)configWithRecordModel:(ASRecordListItemModel *)model cellStyle:(ASListCellStyle)style;

/// 渲染界面
- (void)render:(ASListCellStyle)style;

+ (CGFloat)heightWithCellStyle:(ASListCellStyle)cellStyle;

@end

NS_ASSUME_NONNULL_END
