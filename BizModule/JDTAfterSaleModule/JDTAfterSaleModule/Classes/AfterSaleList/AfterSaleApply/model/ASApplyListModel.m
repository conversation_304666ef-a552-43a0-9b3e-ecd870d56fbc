//
//  ASApplyListModel.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/7.
//

#import "ASApplyListModel.h"

@implementation ASApplyListItemAddressInfoModel

@end

@implementation ASApplyListItemExpressInfoModel

@end

@implementation ASApplyListItemGeneralContactInfoModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"expressInfos": [ASApplyListItemExpressInfoModel class]
    };
}

@end

@implementation ASApplyListItemSkuItemLocCodeModel

@end

@implementation ASApplyListItemSkuItemGeneralTypeModel

@end

@implementation ASApplyListItemSkuItemServiceTypeModel

@end

@implementation ASApplyListItemSkuItemModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"canApplyType": [NSNumber class],
        @"serviceTypeList": [ASApplyListItemSkuItemServiceTypeModel class],
        @"refundTypeList": [ASApplyListItemSkuItemGeneralTypeModel class],
        @"returnModelList": [ASApplyListItemSkuItemGeneralTypeModel class],
        @"locCodeList": [ASApplyListItemSkuItemLocCodeModel class]
    };
}

@end

@implementation ASApplyListItemVenderModel

@end

@implementation ASApplyListItemModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"skuList": [ASApplyListItemSkuItemModel class]
    };
}

@end

@implementation ASApplyListModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"data": [ASApplyListItemModel class]
    };
}

@end
