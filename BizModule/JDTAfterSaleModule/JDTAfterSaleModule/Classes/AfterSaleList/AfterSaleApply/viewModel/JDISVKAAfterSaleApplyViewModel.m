//
//  JDISVKAAfterSaleApplyViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleApplyViewModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKAAfterSaleCustomModel.h"
#import "ASApplyListModel.h"

@import JDTInfrastructureModule;

@interface JDISVKAAfterSaleApplyViewModel()

//@property (nonatomic,copy)NSArray *tempArray;

//是否隐藏到底提示
@property (nonatomic,assign)BOOL isHiddenFullTips;

@end

@implementation JDISVKAAfterSaleApplyViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.currentPage = 1;
        self.isHiddenFooter = true;
    }
    return self;
}


- (CGFloat)getCurrentCellHeight:(NSInteger)index {
    //cell固定高度
    CGFloat fixedHeight = 166 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    if (index >= 0 && index < self.dataArr.count) {
        ASApplyListItemSkuItemModel *skuItem = self.dataArr[index].skuList.firstObject;
        if (skuItem.canApply) {
            fixedHeight = fixedHeight + 30;
        } else {
            if (skuItem.reasonText) {
                CGSize reasonSize = [skuItem.reasonText jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width - 88 - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], MAXFLOAT))];
                fixedHeight = fixedHeight + 24 + reasonSize.height;
            }
        }
    }
    return fixedHeight;
}

- (void)configDataArray{
//    if (![self.tempArray yy_modelIsEqual:[NSNull null]]) {
//        for (NSDictionary *order in self.tempArray) {
//            afterSaleApplyListOrder *orderModel = [afterSaleApplyListOrder yy_modelWithDictionary:order];
//            for (NSDictionary *detailOrder in orderModel.skuList) {
//                NSMutableArray *mutableArray = [NSMutableArray arrayWithArray:self.dataArray];
//                afterSaleApplyListOrderDetail *tempOrder = [afterSaleApplyListOrderDetail yy_modelWithDictionary:detailOrder];
//                tempOrder.orderId = orderModel.orderId;
//                [mutableArray addObject:tempOrder];
//                self.dataArray = [mutableArray copy];
//            }
//        }
//    }
//    self.isHiddenFooter = self.dataArray.count >= 4 ? NO : YES;
    
}

- (RACSignal *)requestInfo {
    NSDictionary *parameters = @{
        @"pageSize": @20,
        @"pageNum": @(self.currentPage),
        @"nearDays": @0,
        @"keywords": @""
    };
    JDWeakSelf
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        JDStrongSelf
//        [[JDISVKAAfterSaleService shareService] requestAfterSaleApplyListWithParameters:parameters complete:^(NSDictionary * _Nonnull response) {
//            JDStrongSelf
//            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:response];
//            
//            if (customModel.success) {
//                NSDictionary *data = customModel.data;
//                if (!([data[@"result"] isEqual:[NSNull null]] || data[@"result"] == nil)) {
//                    self.tempArray = data[@"result"];
//                    self.isHiddenFooter = self.tempArray.count >= 4 ? NO : YES;
//                    
//                    if (!([data[@"pageCount"] isEqual:[NSNull null]] || data[@"pageCount"] == nil)) {
//                        self.pageCount = [data[@"pageCount"] intValue];
//                    }else{
//                        self.pageCount = 1;
//                    }
//                    [self configDataArray];
//                    [subscriber sendCompleted];
//                }else{
//                    self.errorTypeValue = 0;
//                    [subscriber sendError:nil];
//                }
//            }else{
//                self.errorTypeValue = 1;
//                [subscriber sendError:nil];
//            }
//            
//        }];
        [[OOPNetworkManager sharedManager] POST:@"afterSale/queryAfsOrderList?apiCode=b2c.cbff.afterSale.queryAfsOrderList" parameters:parameters headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                self.errorTypeValue = 1;
                [subscriber sendError:error];
            } else {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    ASApplyListModel *model = [ASApplyListModel yy_modelWithDictionary:responseObject[@"data"]];
                    if (model.data.count > 0) {
                        self.pageCount = model.total;
                        self.dataArr = model.data;
                        self.isHiddenFooter = self.dataArr.count >= 4 ? NO : YES;
                        [subscriber sendCompleted];
                    } else {
                        self.errorTypeValue = 0;
                        [subscriber sendError:nil];
                    }
                } else {
                    self.errorTypeValue = 1;
                    [subscriber sendError:nil];
                }
            }
        }];
        return nil;
    }];
    return signal;
}


@end
