//
//  JDISVKAAfterSaleApplyViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleApplyViewModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKAAfterSaleCustomModel.h"
#import "ASApplyListModel.h"

@import JDTInfrastructureModule;

@interface JDISVKAAfterSaleApplyViewModel()

//是否隐藏到底提示
@property (nonatomic,assign)BOOL isHiddenFullTips;

@property (nonatomic, assign) NSInteger pageSize;

@end

@implementation JDISVKAAfterSaleApplyViewModel

- (instancetype)init {
    self = [super init];
    if (self) {
        self.currentPage = 1;
        self.pageSize = 20;
        self.isHiddenFooter = true;
    }
    return self;
}

- (RACSignal *)requestInfo:(ASListType)type {
    switch (type) {
        case ASListTypeApply: {
            NSDictionary *parameters = @{
                @"pageSize": @(self.pageSize),
                @"pageNum": @(self.currentPage),
                @"nearDays": @0,
                @"keywords": @""
            };
            JDWeakSelf
            RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
                JDStrongSelf
                [[OOPNetworkManager sharedManager] POST:@"afterSale/queryAfsOrderList?apiCode=b2c.cbff.afterSale.queryAfsOrderList" parameters:parameters headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
                    if (error) {
                        self.errorTypeValue = 1;
                        [subscriber sendError:error];
                    } else {
                        if ([responseObject[@"code"] isEqualToString:@"0"]) {
                            ASApplyListModel *model = [ASApplyListModel yy_modelWithDictionary:responseObject[@"data"]];
                            if (model.data.count > 0) {
                                self.pageCount = ceil((double)model.total / self.pageSize);
                                if (self.currentPage == 1) {
                                    self.applyDataArr = model.data;
                                } else {
                                    // 创建可变数组，将现有数据和新数据合并
                                    NSMutableArray *combinedArray = [NSMutableArray arrayWithArray:self.applyDataArr];
                                    [combinedArray addObjectsFromArray:model.data];
                                    self.applyDataArr = [combinedArray copy];
                                }
                                // 判断是否已经到达最后一页
                                self.isHiddenFooter = (self.currentPage >= self.pageCount);
                                [subscriber sendCompleted];
                            } else {
                                self.errorTypeValue = 0;
                                [subscriber sendError:nil];
                            }
                        } else {
                            self.errorTypeValue = 1;
                            [subscriber sendError:nil];
                        }
                    }
                }];
                return nil;
            }];
            return signal;
        }
        case ASListTypeRecord: {
            NSDictionary *parameters = @{
                @"pageSize": @(self.pageSize),
                @"pageNum": @(self.currentPage),
                @"nearDays": @0,
                @"keywords": @""
            };
            JDWeakSelf
            RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
                JDStrongSelf
                [[OOPNetworkManager sharedManager] POST:@"afterSale/getAfterSaleRecordList?apiCode=b2c.cbff.afterSale.getAfterSaleRecordList" parameters:parameters headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
                    if (error) {
                        self.errorTypeValue = 1;
                        [subscriber sendError:error];
                    } else {
                        if ([responseObject[@"code"] isEqualToString:@"0"]) {
                            ASRecordListModel *model = [ASRecordListModel yy_modelWithDictionary:responseObject[@"data"]];
                            if (model.data.count > 0) {
                                self.pageCount = ceil((double)model.total / self.pageSize);
                                if (self.currentPage == 1) {
                                    self.recordDataArr = model.data;
                                } else {
                                    // 创建可变数组，将现有数据和新数据合并
                                    NSMutableArray *combinedArray = [NSMutableArray arrayWithArray:self.recordDataArr];
                                    [combinedArray addObjectsFromArray:model.data];
                                    self.recordDataArr = [combinedArray copy];
                                }
                                // 判断是否已经到达最后一页
                                self.isHiddenFooter = (self.currentPage >= self.pageCount);
                                [subscriber sendCompleted];
                            } else {
                                self.errorTypeValue = 0;
                                [subscriber sendError:nil];
                            }
                        } else {
                            self.errorTypeValue = 1;
                            [subscriber sendError:nil];
                        }
                    }
                }];
                return nil;
            }];
            return signal;
        }
        default:
            return nil;
    }
}

- (ASListCellStyle)getCellStyleWithApplyModel:(ASApplyListItemModel *)model {
    ASApplyListItemSkuItemModel *skuItem = model.skuList.firstObject;
    if (skuItem.canApply) {
        if (skuItem.reasonText.length > 0) {
            return ASListCellStyleApplyBtnAndProgress;
        } else {
            return ASListCellStyleApplyBtnOnly;
        }
    } else {
        switch (skuItem.reasonType) {
            case ASSkuNoApplyReasonTypeNotSupport: {
                return ASListCellStyleApplyNoBtnAndTips;
            }
            case ASSkuNoApplyReasonTypeNoCount: {
                return ASListCellStyleApplyNoBtnAndTips;
            }
            case ASSkuNoApplyReasonTypeExpired: {
                return ASListCellStyleApplyBtnDisabledAndTips;
            }
            case ASSkuNoApplyReasonTypeApplied: {
                return ASListCellStyleApplyNoBtnAndProgress;
            }
            default:
                break;
        }
    }
}

- (ASListCellStyle)getCellStyleWithRecordModel:(ASRecordListItemModel *)model {
    if (model.afsServiceStatus == ASServiceStatusReject) {
        return ASListCellStyleRecordProgressReject;
    } else {
        return ASListCellStyleRecordProgressNormal;
    }
}


@end
