//
//  JDISVKAAfterSaleApplyViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import <Foundation/Foundation.h>
#import "ASApplyListModel.h"
#import "ASRecordListModel.h"
#import "ASApplyListCell.h"

@class RACSubject;
@class RACSignal;

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfterSaleApplyViewModel : NSObject

@property (nonatomic, copy) NSArray <ASApplyListItemModel *> *applyDataArr;

@property (nonatomic, copy) NSArray <ASRecordListItemModel *> *recordDataArr;

@property (nonatomic,assign)NSInteger currentPage;

@property (nonatomic,assign)bool isHiddenFooter;

@property (nonatomic,assign)NSInteger errorTypeValue;  //0:空页面 1:加载失败

@property (nonatomic,assign)NSInteger pageCount;

- (RACSignal *)requestInfo:(ASListType)type;

/// 获取Cell的样式类型
/// @param model 数据模型
- (ASListCellStyle)getCellStyleWithApplyModel:(ASApplyListItemModel *)model;
- (ASListCellStyle)getCellStyleWithRecordModel:(ASRecordListItemModel *)model;

@end

NS_ASSUME_NONNULL_END
