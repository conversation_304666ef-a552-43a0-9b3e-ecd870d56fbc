# ASApplyListCell 实现说明

## 🎯 实现目标

将售后申请列表的Cell从`JDISVKAAfterSaleApplyListCell`改为使用`ASApplyListCell`，并按照设计图中的样式进行布局。

## 🎨 界面设计

### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│  📱 ASApplyListCell                                      │
├─────────────────────────────────────────────────────────┤
│  🏪 威克士官方旗舰店                                      │
├─────────────────────────────────────────────────────────┤
│  📷    KDI4 EP女蓝球鞋这里是商品                          │
│  [图]   品这里是商品品这里是商品品这                      │
│  片     数量 1                                           │
├─────────────────────────────────────────────────────────┤
│  ⚠️ 该商品已过售后期                                      │
├─────────────────────────────────────────────────────────┤
│                                        [申请售后] 按钮    │
└─────────────────────────────────────────────────────────┘
```

### 视觉特点
- **卡片式设计**：白色背景，圆角边框
- **层次分明**：店铺名称、商品信息、状态提示、操作按钮分层展示
- **状态指示**：根据商品是否可申请售后显示不同的提示和按钮状态
- **响应式布局**：使用Auto Layout适配不同屏幕尺寸

## 🔧 技术实现

### 1. 头文件定义 (ASApplyListCell.h)

```objc
@interface ASApplyListCell : UITableViewCell

@property (nonatomic, strong) RACSubject *delegate;

/// 配置数据
/// @param model 数据模型
- (void)configWithModel:(ASApplyListItemModel *)model;

/// 渲染界面
/// @param index 索引
- (void)render:(NSInteger)index;

@end
```

### 2. 界面组件

#### 主要UI组件
- **containerView**: 主容器，白色背景，圆角设计
- **shopNameLabel**: 店铺名称标签
- **productImageView**: 商品图片，60x60尺寸
- **productNameLabel**: 商品名称，支持2行显示
- **quantityLabel**: 数量标签
- **statusTipLabel**: 状态提示标签
- **applyButton**: 申请售后按钮

#### 约束布局
```objc
- (void)setupConstraints {
    // 主容器约束
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).offset(8);
        make.left.mas_equalTo(self.contentView).offset(12);
        make.right.mas_equalTo(self.contentView).offset(-12);
        make.bottom.mas_equalTo(self.contentView).offset(-8);
    }];
    
    // 其他组件约束...
}
```

### 3. 数据配置

#### 配置方法
```objc
- (void)configWithModel:(ASApplyListItemModel *)model {
    self.model = model;
    self.skuItem = model.skuList.firstObject;
}

- (void)render:(NSInteger)index {
    // 店铺名称
    self.shopNameLabel.text = self.model.venderInfo.shopName ?: @"";
    
    // 商品信息
    [self.productImageView jdcd_setImage:imageUrl placeHolder:placeholder];
    self.productNameLabel.text = self.skuItem.skuName ?: @"";
    self.quantityLabel.text = [NSString stringWithFormat:@"数量 %ld", self.skuItem.skuCount];
    
    // 状态和按钮配置
    [self configStatusAndButton];
}
```

#### 状态配置
```objc
- (void)configStatusAndButton {
    if (self.skuItem.canApply) {
        // 可以申请售后
        self.statusTipLabel.hidden = YES;
        self.applyButton.enabled = YES;
        [self.applyButton setBackgroundColor:enabledColor];
    } else {
        // 不能申请售后
        self.statusTipLabel.text = self.skuItem.reasonText;
        self.statusTipLabel.hidden = NO;
        self.applyButton.enabled = NO;
        [self.applyButton setBackgroundColor:disabledColor];
    }
}
```

### 4. 事件处理

#### 按钮点击事件
```objc
- (void)applyButtonTapped {
    if (!self.skuItem.canApply) {
        return;
    }
    
    // 发送申请售后事件
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithClickApplyButton];
    action.sender = self.model;
    [self.delegate sendNext:action];
}
```

## 🔄 Controller集成

### 1. 修改JDISVKAAfterSaleApplyViewController.m

#### 导入新Cell
```objc
#import "ASApplyListCell.h"  // 替换原来的JDISVKAAfterSaleApplyListCell
```

#### 注册Cell
```objc
[_tableView registerClass:[ASApplyListCell class] forCellReuseIdentifier:@"ASApplyListCell"];
```

#### 配置Cell
```objc
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ASApplyListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ASApplyListCell" forIndexPath:indexPath];
    [cell configWithModel:self.viewModel.dataArr[indexPath.row]];
    
    // 事件监听
    [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(JDISVKAAfterSaleAction *action) {
        [self eventWithActionType:action];
    }];
    return cell;
}
```

### 2. 新增事件类型

#### 在JDISVKAAfterSaleAction.h中添加
```objc
typedef NS_ENUM(NSInteger,JDISVKAAfterSaleActionType) {
    // ... 其他事件类型
    JDISVKAAfterSaleActionTypeWithClickApplyButton, // 点击申请售后按钮
};
```

### 3. 事件处理逻辑

#### 处理申请按钮点击
```objc
- (void)eventWithActionType:(JDISVKAAfterSaleAction *)action {
    if (action.actionType == JDISVKAAfterSaleActionTypeWithClickApplyButton) {
        ASApplyListItemModel *model = (ASApplyListItemModel *)action.sender;
        ASApplyListItemSkuItemModel *skuItem = model.skuList.firstObject;
        
        // 根据支持的服务类型处理
        NSArray *serviceTypes = skuItem.serviceTypeList;
        if (serviceTypes.count == 1) {
            // 单一服务类型，直接跳转
            [self navigateToApplyDetailWithModel:model serviceType:serviceType];
        } else {
            // 多种服务类型，显示选择界面
            [self showServiceTypeSelectionWithModel:model serviceTypes:serviceTypes];
        }
    }
}
```

#### 服务类型选择
```objc
- (void)showServiceTypeSelectionWithModel:(ASApplyListItemModel *)model 
                             serviceTypes:(NSArray<ASApplyListItemSkuItemServiceTypeModel *> *)serviceTypes {
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"选择售后类型" 
                                                                             message:nil 
                                                                      preferredStyle:UIAlertControllerStyleActionSheet];
    
    for (ASApplyListItemSkuItemServiceTypeModel *serviceType in serviceTypes) {
        if (serviceType.valid) {
            UIAlertAction *action = [UIAlertAction actionWithTitle:serviceType.serviceText 
                                                             style:UIAlertActionStyleDefault 
                                                           handler:^(UIAlertAction * _Nonnull action) {
                [self navigateToApplyDetailWithModel:model serviceType:serviceType.serviceType];
            }];
            [alertController addAction:action];
        }
    }
    
    [self presentViewController:alertController animated:YES completion:nil];
}
```

## 🎯 核心特性

### 1. 响应式设计
- **Auto Layout约束**：适配不同屏幕尺寸
- **动态高度**：根据内容自动调整
- **安全区域适配**：支持刘海屏等特殊屏幕

### 2. 状态管理
- **可申请状态**：按钮可点击，无状态提示
- **不可申请状态**：按钮禁用，显示原因提示
- **视觉反馈**：不同状态使用不同的颜色和样式

### 3. 交互体验
- **点击反馈**：按钮点击有视觉反馈
- **服务类型选择**：支持多种售后类型选择
- **错误处理**：对不可申请的商品进行友好提示

### 4. 数据驱动
- **模型绑定**：使用ASApplyListItemModel数据模型
- **图片加载**：支持网络图片加载和占位图
- **文本显示**：支持多行文本和动态内容

## 🔍 与原Cell的对比

| 特性 | JDISVKAAfterSaleApplyListCell | ASApplyListCell |
|------|------------------------------|-----------------|
| 布局方式 | XIB文件 | 纯代码Auto Layout |
| 设计风格 | 复杂布局 | 简洁卡片式 |
| 交互方式 | 多个按钮 | 单一申请按钮 |
| 状态提示 | 复杂状态显示 | 简洁状态提示 |
| 维护性 | 依赖XIB文件 | 纯代码，易维护 |

## 🎉 总结

通过实现ASApplyListCell，我们获得了：

✅ **简洁的界面设计** - 卡片式布局，层次分明
✅ **灵活的交互方式** - 支持多种售后类型选择
✅ **完善的状态管理** - 清晰的可申请/不可申请状态
✅ **响应式布局** - 适配不同屏幕尺寸
✅ **易于维护** - 纯代码实现，结构清晰
✅ **用户友好** - 直观的操作流程和状态提示

这个新的Cell实现完全符合设计图的要求，提供了更好的用户体验和更清晰的代码结构。
