# ASApplyListCell 五种样式类型实现说明

## 🎨 样式类型枚举

```objc
typedef NS_ENUM(NSUInteger, ASApplyListCellStyleType) {
    ASApplyListCellStyleTypeApplyBtnFirst,             // 第一次申请
    ASApplyListCellStyleTypeApplyBtnDisabled,          // 按钮显示但禁用(超过售后期时)
    ASApplyListCellStyleTypeApplyBtnAgain,             // 再次申请（购买多件商品，剩余商品继续申请）
    ASApplyListCellStyleTypeNoApplyBtnNotSupport,      // 无申请按钮-不支持申请
    ASApplyListCellStyleTypeNoApplyBtnApplying         // 无申请按钮-申请中（显示一条进度信息）
};
```

## 📱 五种样式展示

### 1. ASApplyListCellStyleTypeApplyBtnFirst - 第一次申请
```
┌─────────────────────────────────────────────────────────┐
│  🏪 威克士官方旗舰店                                      │
├─────────────────────────────────────────────────────────┤
│  📷    KDI4 EP女蓝球鞋这里是商品                          │
│  [图]   品这里是商品品这里是商品品这                      │
│  片     数量 1                                           │
├─────────────────────────────────────────────────────────┤
│                                        [申请售后] 按钮    │
└─────────────────────────────────────────────────────────┘
```
**特点**：
- 显示蓝色可点击的"申请售后"按钮
- 无状态提示文字
- 高度：基础高度(156) + 按钮区域(56) = 212px

### 2. ASApplyListCellStyleTypeApplyBtnAgain - 再次申请
```
┌─────────────────────────────────────────────────────────┐
│  🏪 威克士官方旗舰店                                      │
├─────────────────────────────────────────────────────────┤
│  📷    KDI4 EP女蓝球鞋这里是商品                          │
│  [图]   品这里是商品品这里是商品品这                      │
│  片     数量 1                                           │
├─────────────────────────────────────────────────────────┤
│                                        [再次申请] 按钮    │
└─────────────────────────────────────────────────────────┘
```
**特点**：
- 显示蓝色可点击的"再次申请"按钮
- 适用于已申请过部分数量，还有剩余可申请数量的情况
- 高度：基础高度(156) + 按钮区域(56) = 212px

### 3. ASApplyListCellStyleTypeApplyBtnDisabled - 按钮禁用
```
┌─────────────────────────────────────────────────────────┐
│  🏪 威克士官方旗舰店                                      │
├─────────────────────────────────────────────────────────┤
│  📷    KDI4 EP女蓝球鞋这里是商品                          │
│  [图]   品这里是商品品这里是商品品这                      │
│  片     数量 1                                           │
├─────────────────────────────────────────────────────────┤
│  ⚠️ 该商品已过售后期                                      │
├─────────────────────────────────────────────────────────┤
│                                        [申请售后] 按钮    │
│                                         (灰色禁用)        │
└─────────────────────────────────────────────────────────┘
```
**特点**：
- 显示灰色禁用的"申请售后"按钮
- 显示红色状态提示文字说明原因
- 适用于超过售后期等情况
- 高度：基础高度(156) + 状态文字高度 + 按钮区域(56)

### 4. ASApplyListCellStyleTypeNoApplyBtnNotSupport - 不支持申请
```
┌─────────────────────────────────────────────────────────┐
│  🏪 威克士官方旗舰店                                      │
├─────────────────────────────────────────────────────────┤
│  📷    KDI4 EP女蓝球鞋这里是商品                          │
│  [图]   品这里是商品品这里是商品品这                      │
│  片     数量 1                                           │
├─────────────────────────────────────────────────────────┤
│  ⚠️ 该商品不支持售后                                      │
└─────────────────────────────────────────────────────────┘
```
**特点**：
- 无申请按钮
- 显示红色状态提示文字
- 适用于商品本身不支持售后的情况
- 高度：基础高度(156) + 状态文字高度 + 间距(20)

### 5. ASApplyListCellStyleTypeNoApplyBtnApplying - 申请中
```
┌─────────────────────────────────────────────────────────┐
│  🏪 威克士官方旗舰店                                      │
├─────────────────────────────────────────────────────────┤
│  📷    KDI4 EP女蓝球鞋这里是商品                          │
│  [图]   品这里是商品品这里是商品品这                      │
│  片     数量 1                                           │
├─────────────────────────────────────────────────────────┤
│  📋 申请中，请耐心等待                                    │
└─────────────────────────────────────────────────────────┘
```
**特点**：
- 无申请按钮
- 显示浅灰色背景的进度信息条
- 适用于已提交申请正在处理中的情况
- 高度：基础高度(156) + 进度条高度(44)

## 🔧 技术实现

### 样式类型判断逻辑

```objc
+ (ASApplyListCellStyleType)getStyleTypeWithModel:(ASApplyListItemModel *)model {
    ASApplyListItemSkuItemModel *skuItem = model.skuList.firstObject;
    
    // 如果已经有申请的售后服务单，显示申请中状态
    if (skuItem.afsServiceId && skuItem.afsServiceId.integerValue > 0) {
        return ASApplyListCellStyleTypeNoApplyBtnApplying;
    }
    
    // 如果不能申请售后
    if (!skuItem.canApply) {
        switch (skuItem.reasonType) {
            case AFSkuNoApplyReasonTypeNotSupport:
                return ASApplyListCellStyleTypeNoApplyBtnNotSupport;
            case AFSkuNoApplyReasonTypeExpired:
                return ASApplyListCellStyleTypeApplyBtnDisabled;
            case AFSkuNoApplyReasonTypeNoCount:
                return ASApplyListCellStyleTypeNoApplyBtnNotSupport;
            case AFSkuNoApplyReasonTypeApplied:
                return ASApplyListCellStyleTypeNoApplyBtnApplying;
            default:
                return ASApplyListCellStyleTypeApplyBtnDisabled;
        }
    }
    
    // 可以申请售后的情况
    if (skuItem.appliedCount > 0 && skuItem.canApplyCount.integerValue > 0) {
        return ASApplyListCellStyleTypeApplyBtnAgain;
    } else {
        return ASApplyListCellStyleTypeApplyBtnFirst;
    }
}
```

### 高度计算逻辑

```objc
+ (CGFloat)getCellHeightWithModel:(ASApplyListItemModel *)model styleType:(ASApplyListCellStyleType)styleType {
    CGFloat baseHeight = 156; // 基础高度
    
    switch (styleType) {
        case ASApplyListCellStyleTypeApplyBtnFirst:
        case ASApplyListCellStyleTypeApplyBtnAgain:
            return baseHeight + 56; // 按钮区域
            
        case ASApplyListCellStyleTypeApplyBtnDisabled:
            // 状态文字 + 按钮
            return baseHeight + [self calculateTextHeight:model.skuList.firstObject.reasonText] + 56;
            
        case ASApplyListCellStyleTypeNoApplyBtnNotSupport:
            // 只有状态文字
            return baseHeight + [self calculateTextHeight:model.skuList.firstObject.reasonText] + 20;
            
        case ASApplyListCellStyleTypeNoApplyBtnApplying:
            // 进度条
            return baseHeight + 44;
    }
}
```

### UI配置逻辑

```objc
- (void)configUIForStyleType:(ASApplyListCellStyleType)styleType {
    // 先隐藏所有可变组件
    self.statusTipLabel.hidden = YES;
    self.applyButton.hidden = YES;
    self.progressView.hidden = YES;
    
    switch (styleType) {
        case ASApplyListCellStyleTypeApplyBtnFirst:
            [self configApplyButtonWithTitle:@"申请售后" enabled:YES];
            break;
            
        case ASApplyListCellStyleTypeApplyBtnAgain:
            [self configApplyButtonWithTitle:@"再次申请" enabled:YES];
            break;
            
        case ASApplyListCellStyleTypeApplyBtnDisabled:
            [self configApplyButtonWithTitle:@"申请售后" enabled:NO];
            [self configStatusTipWithText:self.skuItem.reasonText];
            break;
            
        case ASApplyListCellStyleTypeNoApplyBtnNotSupport:
            [self configStatusTipWithText:self.skuItem.reasonText];
            break;
            
        case ASApplyListCellStyleTypeNoApplyBtnApplying:
            [self configProgressViewWithText:@"申请中，请耐心等待"];
            break;
    }
    
    [self updateConstraintsForStyleType:styleType];
}
```

## 🎯 使用方式

### 在Controller中使用

```objc
// 计算Cell高度
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row < self.viewModel.dataArr.count) {
        ASApplyListItemModel *model = self.viewModel.dataArr[indexPath.row];
        ASApplyListCellStyleType styleType = [ASApplyListCell getStyleTypeWithModel:model];
        return [ASApplyListCell getCellHeightWithModel:model styleType:styleType];
    }
    return 156; // 默认高度
}

// 配置Cell
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ASApplyListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ASApplyListCell" forIndexPath:indexPath];
    [cell configWithModel:self.viewModel.dataArr[indexPath.row]];
    
    [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(JDISVKAAfterSaleAction *action) {
        [self eventWithActionType:action];
    }];
    return cell;
}
```

## 🎉 总结

通过实现五种不同的样式类型，ASApplyListCell现在可以：

✅ **智能判断状态** - 根据数据模型自动确定显示样式
✅ **动态高度计算** - 根据内容和样式类型精确计算Cell高度
✅ **灵活的UI配置** - 支持按钮、状态提示、进度信息等多种组合
✅ **响应式布局** - 使用Auto Layout适配不同内容和屏幕
✅ **用户体验优化** - 清晰的视觉层次和状态指示

这个实现完全满足了设计图中的五种状态展示需求，提供了完整的售后申请列表界面解决方案。
