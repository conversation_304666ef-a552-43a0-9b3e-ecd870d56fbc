//
//  ASRecordListModel.h
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/7.
//

#import <Foundation/Foundation.h>
#import "ASApplyListModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface ASRecordListItemModel : NSObject

@property (nonatomic, copy) NSString *afsServiceId;

@property (nonatomic, copy) NSString *orderId;

@property (nonatomic, assign) NSInteger applyCount;

@property (nonatomic, copy) NSString *applyDate;

@property (nonatomic, copy) NSString *currentDate;

@property (nonatomic, copy) NSString *updateTime;

@property (nonatomic, assign) NSInteger afsServiceStatus;

@property (nonatomic, copy) NSString *afsServiceStatusText;

@property (nonatomic, copy) NSString *afsServiceStateDesc;

@property (nonatomic, copy) NSArray <ASApplyListItemSkuItemModel *> *skuList;

@property (nonatomic, strong) ASApplyListItemVenderModel *venderInfo;

@property (nonatomic, assign) NSInteger serviceType;

@end

@interface ASRecordListModel : NSObject

@property (nonatomic, assign) NSInteger total;

@property (nonatomic, copy) NSArray <ASRecordListItemModel *> *data;

@property (nonatomic, assign) NSInteger currentPage;

@property (nonatomic, assign) NSInteger pageSize;
/// 滚动ID（用于分页查询）
@property (nonatomic, copy) NSString *scrollId;

@end

NS_ASSUME_NONNULL_END
