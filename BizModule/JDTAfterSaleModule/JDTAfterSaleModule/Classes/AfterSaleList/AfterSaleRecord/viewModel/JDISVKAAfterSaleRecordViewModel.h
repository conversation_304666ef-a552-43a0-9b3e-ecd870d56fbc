//
//  JDISVKAAfterSaleRecordViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import <Foundation/Foundation.h>
#import "ASRecordListModel.h"

@class RACSubject;
//@class AfsRecordSkuDetailVo;
@class RACSignal;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfterSaleRecordViewModel : NSObject

- (RACSignal *)requestInfo;

- (CGFloat)getCurrentCellHeight:(NSInteger)index;

@property (nonatomic,strong)NSArray<ASRecordListItemModel *> *dataArr;

@property (nonatomic,assign)NSInteger currentPage;

@property (nonatomic,assign)NSInteger pageCount;

@property (nonatomic,assign)bool isHiddenFooter;

@property (nonatomic,assign)NSInteger errorTypeValue;  //0:空页面 1:加载失败

@end

NS_ASSUME_NONNULL_END
