//
//  JDISVKAAfterSaleRecordViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleRecordViewModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKAAfterSaleCustomModel.h"

@import JDTInfrastructureModule;

@interface JDISVKAAfterSaleRecordViewModel()

//@property (nonatomic,copy)NSArray *tempArray;

@end

@implementation JDISVKAAfterSaleRecordViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.currentPage = 1;
        self.isHiddenFooter = true;
    }
    return self;
}

- (CGFloat)getCurrentCellHeight:(NSInteger)index{
//    AfsRecordSkuDetailVo *recordDetail = self.dataArray[index];
//    CGFloat fixedHeight = 166 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
//    if (recordDetail.afsServiceStateDesc) {
//        if (recordDetail.afsServiceStateDesc.length > 0) {
//            CGSize afsServiceStateDescSize = [recordDetail.afsServiceStateDesc jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width - 100 - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], MAXFLOAT))];
//            fixedHeight = fixedHeight + 48 + afsServiceStateDescSize.height;
//        }
//    }
//    return fixedHeight;
    
    ASRecordListItemModel *model = self.dataArr[index];
    CGFloat fixedHeight = 166 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    if (model.afsServiceStateDesc.length > 0) {
        CGSize afsServiceStateDescSize = [model.afsServiceStateDesc jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width - 100 - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], MAXFLOAT))];
        fixedHeight = fixedHeight + 48 + afsServiceStateDescSize.height;
    }
    return fixedHeight;
}

//- (void)configDataArray{
//    if (![self.tempArray yy_modelIsEqual:[NSNull null]]) {
//        for (NSDictionary *recordDic in self.tempArray) {
//            AfterSaleRecordModel *record = [AfterSaleRecordModel yy_modelWithDictionary:recordDic];
//            for (NSDictionary *detailRecord in record.skuList) {
//                NSMutableArray *mutableArray = [NSMutableArray arrayWithArray:self.dataArray];
//                AfsRecordSkuDetailVo *tempDetailRecord = [AfsRecordSkuDetailVo yy_modelWithDictionary:detailRecord];
//                tempDetailRecord.afsServiceId = record.afsServiceId;
//                tempDetailRecord.afsServiceStatusText = record.afsServiceStatusText;
//                tempDetailRecord.afsServiceStateDesc = record.afsServiceStateDesc;
//                tempDetailRecord.customerExpectStr = record.customerExpectStr;
//                tempDetailRecord.customerExpect = record.customerExpect;
//                tempDetailRecord.afsServiceStepName = record.afsServiceStepName;
//                [mutableArray addObject:tempDetailRecord];
//                self.dataArray = [mutableArray copy];
//            }
//        }
//    }
//    
//}

- (RACSignal *)requestInfo{
    NSDictionary *parameters = @{
        @"pageSize": @20,
        @"pageNum": @(self.currentPage),
        @"nearDays": @0,
        @"keywords": @""
    };
    JDWeakSelf
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        JDStrongSelf
        [[OOPNetworkManager sharedManager] POST:@"afterSale/getAfterSaleRecordList?apiCode=b2c.cbff.afterSale.getAfterSaleRecordList" parameters:parameters headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                self.errorTypeValue = 1;
                [subscriber sendError:error];
            } else {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    ASRecordListModel *model = [ASRecordListModel yy_modelWithDictionary:responseObject[@"data"]];
                    if (model.data.count > 0) {
                        self.pageCount = model.total;
                        self.dataArr = model.data;
                        self.isHiddenFooter = self.dataArr.count >= 4 ? NO : YES;
                        [subscriber sendCompleted];
                    } else {
                        self.errorTypeValue = 0;
                        [subscriber sendError:nil];
                    }
                } else {
                    self.errorTypeValue = 1;
                    [subscriber sendError:nil];
                }
            }
        }];
        return nil;
    }];
    return signal;
}

@end
