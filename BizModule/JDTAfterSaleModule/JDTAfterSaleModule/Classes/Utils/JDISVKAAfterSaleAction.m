//
//  JDISVKAAfterSaleAction.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleAction.h"

@interface JDISVKAAfterSaleAction()

@property (nonatomic, assign, readwrite) ASActionType actionType;

@end

@implementation JDISVKAAfterSaleAction

- (instancetype)initWithType:(ASActionType)actionType {
    if (self = [super init]) {
        _actionType = actionType;
    }
    return self;
}

+ (instancetype)actionWithType:(ASActionType)actionType {
    return [[JDISVKAAfterSaleAction alloc] initWithType:actionType];
}

@end
