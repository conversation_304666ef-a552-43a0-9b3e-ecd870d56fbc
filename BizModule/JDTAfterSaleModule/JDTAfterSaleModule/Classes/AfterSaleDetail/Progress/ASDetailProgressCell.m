//
//  ASDetailProgressCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailProgressCell.h"
#import "ASDetailProgressViewModel.h"
#import <JDISVMasonryModule/Masonry.h>

@import JDISVKAIconFontModule;
@import JDISVThemeModule;

@interface ASDetailProgressCell ()

@property (nonatomic, strong) UIView *bgView;

@property (nonatomic, strong) UIImageView *titleImgView;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIButton *moreBtn;

@property (nonatomic, strong) UIView *progressContainerView;

@property (nonatomic, strong) NSMutableArray<UIView *> *nodeViews;

@property (nonatomic, strong) NSMutableArray<UILabel *> *nodeLabels;

@property (nonatomic, strong) NSMutableArray<UIView *> *lineViews;

@property (nonatomic, strong) ASDetailProgressViewModel *viewModel;

@end

@implementation ASDetailProgressCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    [self.contentView addSubview:self.bgView];
    self.bgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9-1"];
    self.bgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self).offset(8);
        make.trailing.mas_equalTo(self).offset(-8);
        make.top.bottom.mas_equalTo(self);
    }];

    [self.bgView addSubview:self.moreBtn];
    [self.bgView addSubview:self.titleImgView];
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.progressContainerView];

    [self.moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.bgView).offset(18);
        make.trailing.mas_equalTo(self.bgView).offset(-18);
        make.size.mas_equalTo(CGSizeMake(72, 24));
    }];
    
    [self.titleImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.bgView).offset(18);
        make.top.mas_equalTo(self.bgView).offset(18);
        make.size.mas_equalTo(CGSizeMake(24, 24));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.titleImgView.mas_trailing).offset(4);
        make.trailing.equalTo(self.moreBtn.mas_leading).offset(-18);
        make.top.bottom.mas_equalTo(self.titleImgView);
    }];

    [self.progressContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(self.titleImgView);
        make.trailing.mas_equalTo(self.moreBtn);
        make.bottom.mas_equalTo(self.bgView).offset(-18);
    }];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    self.viewModel = viewModel;
}

- (void)render {
    [self configWithModel:self.viewModel.model];
}

- (void)configWithModel:(ASApplyDetailProcessModel *)model {
    if (!model || !model.nodeStatusList || model.nodeStatusList.count == 0) {
        return;
    }

    // 设置标题
    self.titleLabel.text = model.nodeCodeI18nKey ?: @"";

    // 清除之前的视图
    [self clearProgressViews];

    // 创建进度节点
    [self createProgressNodesWithModel:model];
}

- (void)clearProgressViews {
    for (UIView *view in self.nodeViews) {
        [view removeFromSuperview];
    }
    for (UILabel *label in self.nodeLabels) {
        [label removeFromSuperview];
    }
    for (UIView *line in self.lineViews) {
        [line removeFromSuperview];
    }

    [self.nodeViews removeAllObjects];
    [self.nodeLabels removeAllObjects];
    [self.lineViews removeAllObjects];
}

- (void)createProgressNodesWithModel:(ASApplyDetailProcessModel *)model {
    NSArray<ASApplyDetailProcessNodeModel *> *nodeList = model.nodeStatusList;
    NSInteger nodeCount = nodeList.count;

    if (nodeCount == 0) {
        return;
    }

    CGFloat nodeSize = 20.0;

    for (NSInteger i = 0; i < nodeCount; i++) {
        ASApplyDetailProcessNodeModel *nodeModel = nodeList[i];

        // 创建节点视图
        UIView *nodeView = [self createNodeViewWithModel:nodeModel currentNode:model.nodeCode];
        [self.progressContainerView addSubview:nodeView];
        [self.nodeViews addObject:nodeView];

        // 创建节点标签
        UILabel *nodeLabel = [self createNodeLabelWithModel:nodeModel currentNode:model.nodeCode];
        [self.progressContainerView addSubview:nodeLabel];
        [self.nodeLabels addObject:nodeLabel];

        // 设置节点约束
        [nodeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(nodeSize, nodeSize));
            make.centerY.mas_equalTo(self.progressContainerView).offset(-10);
        }];

        [nodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(nodeView.mas_bottom).offset(4);
            make.centerX.mas_equalTo(nodeView);
            make.width.mas_lessThanOrEqualTo(36);
        }];
    }

    // 设置节点的水平分布约束
    if (nodeCount > 1) {
        [self.nodeViews mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedItemLength:nodeSize leadSpacing:0 tailSpacing:0];
    } else if (nodeCount == 1) {
        [self.nodeViews.firstObject mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.progressContainerView);
        }];
    }

    // 创建连接线
    for (NSInteger i = 0; i < nodeCount - 1; i++) {
        UIView *currentNode = self.nodeViews[i];
        UIView *nextNode = self.nodeViews[i + 1];

        ASApplyDetailProcessNodeModel *nodeModel = nodeList[i];
        UIView *lineView = [self createLineViewWithNode:nodeModel.type currentNode:model.nodeCode];
        [self.progressContainerView addSubview:lineView];
        [self.lineViews addObject:lineView];

        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(currentNode.mas_trailing);
            make.trailing.mas_equalTo(nextNode.mas_leading);
            make.centerY.mas_equalTo(currentNode);
            make.height.mas_equalTo(2);
        }];
    }
}

- (UIView *)createNodeViewWithModel:(ASApplyDetailProcessNodeModel *)nodeModel currentNode:(NSInteger)curNode {
    UIView *nodeView = [[UIView alloc] init];
    nodeView.layer.cornerRadius = 10.0;
    nodeView.layer.masksToBounds = YES;
    
    UIImageView *imgView = [[UIImageView alloc] init];
    [nodeView addSubview:imgView];
    [imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(nodeView);
    }];
    
    // 根据节点状态设置样式
    if (nodeModel.type < curNode) {
        // 已完成状态
        imgView.image = [UIImage ka_iconWithName:JDIF_ICON_SUCCESS_FILL_SMALL imageSize:CGSizeMake(20, 20) color:[UIColor whiteColor]];
    } else if (nodeModel.type == curNode) {
        // 当前状态
        imgView.image = [UIImage ka_iconWithName:JDIF_ICON_PROCESS imageSize:CGSizeMake(20, 20) color:[UIColor whiteColor]];
    } else {
        // 未完成状态
        imgView.image = [UIImage ka_iconWithName:JDIF_ICON_UNDO imageSize:CGSizeMake(10, 10) color:[UIColor colorWithWhite:1 alpha:0.5]];
    }

    return nodeView;
}

- (UILabel *)createNodeLabelWithModel:(ASApplyDetailProcessNodeModel *)nodeModel currentNode:(NSInteger)curNode {
    UILabel *label = [[UILabel alloc] init];
    label.text = nodeModel.text ?: @"";
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;
    label.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];

    // 根据节点状态设置文字颜色
    if (nodeModel.type <= curNode) {
        label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    } else {
        label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1" alpha:0.6];
    }

    return label;
}

- (UIView *)createLineViewWithNode:(NSInteger)node currentNode:(NSInteger)curNode {
    UIView *lineView = [[UIView alloc] init];

    // 根据进度设置连接线颜色
    if (node < curNode) {
        lineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    } else {
        lineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1" alpha:0.5];
    }
    
    return lineView;
}

#pragma mark - Lazy Loading
- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
    }
    return _bgView;
}

- (UIImageView *)titleImgView {
    if (!_titleImgView) {
        _titleImgView = [[UIImageView alloc] init];
        _titleImgView.image = [UIImage ka_iconWithName:JDIF_ICON_CLOCK_LINE_BIG imageSize:CGSizeMake(24, 24) color:[UIColor whiteColor]];
    }
    return _titleImgView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightSemibold];
        _titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _titleLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _titleLabel;
}

- (UIButton *)moreBtn {
    if (!_moreBtn) {
        _moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_moreBtn setTitle:Lang(@"more") forState:UIControlStateNormal];
        [_moreBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateNormal];
        [_moreBtn setBackgroundColor:[UIColor whiteColor]];
        [_moreBtn.titleLabel setFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]];
        _moreBtn.layer.cornerRadius = 12;
        _moreBtn.layer.masksToBounds = YES;
    }
    return _moreBtn;
}

- (UIView *)progressContainerView {
    if (!_progressContainerView) {
        _progressContainerView = [[UIView alloc] init];
        _progressContainerView.backgroundColor = [UIColor clearColor];
    }
    return _progressContainerView;
}

- (NSMutableArray<UIView *> *)nodeViews {
    if (!_nodeViews) {
        _nodeViews = [[NSMutableArray alloc] init];
    }
    return _nodeViews;
}

- (NSMutableArray<UILabel *> *)nodeLabels {
    if (!_nodeLabels) {
        _nodeLabels = [[NSMutableArray alloc] init];
    }
    return _nodeLabels;
}

- (NSMutableArray<UIView *> *)lineViews {
    if (!_lineViews) {
        _lineViews = [[NSMutableArray alloc] init];
    }
    return _lineViews;
}


@end
