//
//  ASProgressListController.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/14.
//

#import "ASProgressListController.h"
#import "JDISVKADetailMainViewModel.h"
#import "ASProgressHistoryCell.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

@import JDISVKAUIKitModule;
@import JDTInfrastructureModule;

@interface ASProgressListController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, copy) NSArray <ASApplyDetailProcessNodeModel *> *modelArr;

@end

@implementation ASProgressListController

- (void)viewDidLoad {
    [super viewDidLoad];

    [self configNavBar];
    [self setupTableView];
    [self loadData];
}

- (void)configNavBar {
    KANavigationBar *navBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navBar];
    JDWeakSelf
    navBar.decorator.backgroundColor().title(@"服务单详情", NSTextAlignmentCenter).backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        JDStrongSelf
        [self.navigationController popViewControllerAnimated:YES];
    }).render();
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES];
}

- (void)setupTableView {
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];

    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset([UIWindow ka_uikit_navigationHeight]);
        make.left.right.bottom.equalTo(self.view);
    }];
}

- (void)loadData {
    [[OOPNetworkManager sharedManager] POST:@"afterSale/getAfsHistory?apiCode=b2c.cbff.afterSale.getAfsHistory" parameters:@{
        @"afsServiceId": self.afsServiceId
    } headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if ([responseObject[@"code"] isEqualToString:@"0"]) {
            self.modelArr = [NSArray yy_modelArrayWithClass:[ASApplyDetailProcessNodeModel class] json:responseObject[@"data"]];
            [self.tableView reloadData];
        } else {
            
        }
    }];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.modelArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ASProgressHistoryCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ASProgressHistoryCell" forIndexPath:indexPath];

    ASApplyDetailProcessNodeModel *model = self.modelArr[indexPath.row];
    BOOL isLast = (indexPath.row == self.modelArr.count - 1);
    BOOL isFirst = (indexPath.row == 0);

    [cell configWithModel:model isLast:isLast isFirst:isFirst];

    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

#pragma mark - Getter and Setter

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] init];
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.dataSource = self;
        _tableView.delegate = self;

        // 设置自动计算高度
        _tableView.estimatedRowHeight = 80;
        _tableView.rowHeight = UITableViewAutomaticDimension;

        // 注册 Cell
        [_tableView registerClass:[ASProgressHistoryCell class] forCellReuseIdentifier:@"ASProgressHistoryCell"];
    }
    return _tableView;
}

@end
