//
//  ASProgressListController.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/14.
//

#import "ASProgressListController.h"
#import "JDISVKADetailMainViewModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

@import JDISVKAUIKitModule;

@interface ASProgressListController ()

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) JDISVKADetailMainViewModel *viewModel;

@property (nonatomic, copy) NSArray <ASApplyDetailProcessNodeModel *> *modelArr;

@end

@implementation ASProgressListController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self configNavBar];
    [self loadData];
}

- (void)configNavBar {
    KANavigationBar *navBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navBar];
    JDWeakSelf
    navBar.decorator.backgroundColor().title(@"服务单详情", NSTextAlignmentCenter).backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        JDStrongSelf
        [self.navigationController popViewControllerAnimated:YES];
    }).render();
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES];
}

- (void)loadData {
    [[self.viewModel requestProgressHistory] subscribeNext:^(id  _Nullable x) {
        self.modelArr = x;
        [self.tableView reloadData];
    }];
}

#pragma mark - Getter and Setter
- (JDISVKADetailMainViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[JDISVKADetailMainViewModel alloc] init];
    }
    return _viewModel;
}

@end
