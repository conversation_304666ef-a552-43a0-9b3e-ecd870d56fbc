//
//  ASProgressHistoryCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/14.
//

#import "ASProgressHistoryCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>

@import JDISVKAIconFontModule;

@interface ASProgressHistoryCell ()

@property (nonatomic, strong) UIImageView *statusIconView;

@property (nonatomic, strong) UILabel *statusLabel;

@property (nonatomic, strong) UILabel *timeLabel;

@property (nonatomic, strong) UIView *lineView;

@end

@implementation ASProgressHistoryCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor clearColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 状态图标
    self.statusIconView = [[UIImageView alloc] init];
    self.statusIconView.layer.cornerRadius = 6;
    self.statusIconView.layer.masksToBounds = YES;
    [self.contentView addSubview:self.statusIconView];
    
    // 状态文本
    self.statusLabel = [[UILabel alloc] init];
    self.statusLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.statusLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.statusLabel.numberOfLines = 0;
    [self.contentView addSubview:self.statusLabel];
    
    // 时间标签
    self.timeLabel = [[UILabel alloc] init];
    self.timeLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.timeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    [self.contentView addSubview:self.timeLabel];
    
    // 连接线
    self.lineView = [[UIView alloc] init];
    self.lineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"];
    [self.contentView addSubview:self.lineView];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    // 状态图标约束
    [self.statusIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(20);
        make.top.equalTo(self.contentView).offset(16);
        make.size.mas_equalTo(CGSizeMake(12, 12));
    }];
    
    // 状态文本约束
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.statusIconView.mas_right).offset(16);
        make.right.equalTo(self.contentView).offset(-20);
        make.top.equalTo(self.contentView).offset(12);
    }];
    
    // 时间标签约束
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.statusLabel);
        make.right.equalTo(self.contentView).offset(-20);
        make.top.equalTo(self.statusLabel.mas_bottom).offset(4);
        make.bottom.equalTo(self.contentView).offset(-16);
    }];
    
    // 连接线约束
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.statusIconView);
        make.top.equalTo(self.statusIconView.mas_bottom).offset(4);
        make.bottom.equalTo(self.contentView);
        make.width.mas_equalTo(2);
    }];
}

- (void)configWithModel:(ASApplyDetailProcessNodeModel *)model isLast:(BOOL)isLast isFirst:(BOOL)isFirst {
    // 设置状态文本
    self.statusLabel.text = model.text ?: @"";
    
    // 设置时间
    self.timeLabel.text = model.finishTime ?: @"";
    
    // 根据是否是最后一个节点来控制连接线的显示
    self.lineView.hidden = isLast;
    
    if (isFirst) {
        self.statusIconView.image = [UIImage ka_iconWithName:JDIF_ICON_PROCESS imageSize:CGSizeMake(40, 40) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
        self.statusLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    } else {
        self.statusIconView.image = [UIImage ka_iconWithName:JDIF_ICON_SUCCESS_FILL_SMALL imageSize:CGSizeMake(40, 40) color:[UIColor jdcd_colorWithHexColorString:@"#E7E6EB"]];
        self.statusLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
}

@end
