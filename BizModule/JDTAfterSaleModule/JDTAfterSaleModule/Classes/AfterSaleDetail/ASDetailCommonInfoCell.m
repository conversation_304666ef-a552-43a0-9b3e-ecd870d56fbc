//
//  ASDetailCommonInfoCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/11.
//

#import "ASDetailCommonInfoCell.h"
#import "ASDetailRefundViewModel.h"
#import "ASDetailOrderViewModel.h"
#import "ASDetailVenderViewModel.h"
#import "ASDetailCustomerViewModel.h"

@interface ASDetailCommonInfoCell ()

@property (nonatomic, strong) ASDetailRefundViewModel *refundVM;

@property (nonatomic, strong) ASDetailOrderViewModel *orderVM;

@property (nonatomic, strong) ASDetailVenderViewModel *venderVM;

@property (nonatomic, strong) ASDetailCustomerViewModel *customerVM;

@end

@implementation ASDetailCommonInfoCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    if ([viewModel isKindOfClass:[ASDetailRefundViewModel class]]) {
        self.refundVM = viewModel;
    } else if ([viewModel isKindOfClass:[ASDetailOrderViewModel class]]) {
        self.orderVM = viewModel;
    } else if ([viewModel isKindOfClass:[ASDetailVenderViewModel class]]) {
        self.venderVM = viewModel;
    } else if ([viewModel isKindOfClass:[ASDetailCustomerViewModel class]]) {
        self.customerVM = viewModel;
    }
}

- (void)render {
    switch (self.type) {
        case ASDetailCommonInfoTypeRefund: {
            
            break;
        }
        case ASDetailCommonInfoTypeOrder: {
            
            break;
        }
        case ASDetailCommonInfoTypeVender: {
            
            break;
        }
        case ASDetailCommonInfoTypeCustomer: {
            
            break;
        }
        default:
            break;
    }
}

@end
