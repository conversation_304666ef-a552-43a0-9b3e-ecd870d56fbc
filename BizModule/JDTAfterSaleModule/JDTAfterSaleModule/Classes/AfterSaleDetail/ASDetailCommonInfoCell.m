//
//  ASDetailCommonInfoCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/11.
//

#import "ASDetailCommonInfoCell.h"
#import "ASDetailRefundViewModel.h"
#import "ASDetailOrderViewModel.h"
#import "ASDetailVenderViewModel.h"
#import "ASDetailCustomerViewModel.h"
#import "ASApplyDetailModel.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAUIKitModule/UIButton+KALabelRender.h>

@import JDISVKAIconFontModule;

@interface ASDetailCommonInfoCell ()

@property (nonatomic, strong) ASDetailRefundViewModel *refundVM;
@property (nonatomic, strong) ASDetailOrderViewModel *orderVM;
@property (nonatomic, strong) ASDetailVenderViewModel *venderVM;
@property (nonatomic, strong) ASDetailCustomerViewModel *customerVM;

// UI 组件
@property (nonatomic, strong) UIView *bgView;

@end

@implementation ASDetailCommonInfoCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    [self setupBgView];
}

- (void)setupBgView {
    self.bgView = [[UIView alloc] init];
    self.bgView.backgroundColor = [UIColor whiteColor];
    self.bgView.layer.cornerRadius = 4;
    self.bgView.layer.masksToBounds = YES;
    [self.contentView addSubview:self.bgView];

    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(0, 8, 0, 8));
    }];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    if ([viewModel isKindOfClass:[ASDetailRefundViewModel class]]) {
        self.refundVM = viewModel;
        self.type = ASDetailCommonInfoTypeRefund;
    } else if ([viewModel isKindOfClass:[ASDetailOrderViewModel class]]) {
        self.orderVM = viewModel;
        self.type = ASDetailCommonInfoTypeOrder;
    } else if ([viewModel isKindOfClass:[ASDetailVenderViewModel class]]) {
        self.venderVM = viewModel;
        self.type = ASDetailCommonInfoTypeVender;
    } else if ([viewModel isKindOfClass:[ASDetailCustomerViewModel class]]) {
        self.customerVM = viewModel;
        self.type = ASDetailCommonInfoTypeCustomer;
    }
}

- (void)render {
    // 清空之前的内容
    [self clearContentView];

    switch (self.type) {
        case ASDetailCommonInfoTypeRefund: {
            [self renderRefundInfo];
            break;
        }
        case ASDetailCommonInfoTypeOrder: {
            [self renderOrderInfo];
            break;
        }
        case ASDetailCommonInfoTypeVender: {
            [self renderVenderInfo];
            break;
        }
        case ASDetailCommonInfoTypeCustomer: {
            [self renderCustomerInfo];
            break;
        }
        default:
            break;
    }
}

- (void)clearContentView {
    for (UIView *subview in self.bgView.subviews) {
        [subview removeFromSuperview];
    }
}

#pragma mark - 渲染方法

- (void)renderRefundInfo {
    if (!self.refundVM) return;

    NSArray *infoItems = @[
        @{@"title": @"退款金额", @"value": [NSString stringWithFormat:@"¥%.2f", self.refundVM.refundInfo.estimatedAmount.floatValue]},
    ];

    [self createInfoListWithItems:infoItems];
}

- (void)renderOrderInfo {
    if (!self.orderVM) return;

    NSArray *infoItems = @[
        @{@"title": @"服务单号", @"value": self.orderVM.info.afsServiceId},
        @{@"title": @"申请时间", @"value": self.orderVM.info.applyDate},
        @{@"title": @"退货类型", @"value": self.orderVM.info.serviceTypeStr},
        @{@"title": @"退款方式", @"value": self.orderVM.info.refundInfoVO.refundTypeStr}
    ];

    [self createInfoListWithItems:infoItems];
}

- (void)renderVenderInfo {
    if (!self.venderVM) return;

    NSMutableArray *infoItems = [NSMutableArray array];

    ASApplyListItemGeneralContactInfoModel *returnVenderInfo = self.venderVM.info.returnVenderInfo;
    
    if (self.venderVM.info.returnModelStr.length > 0) {
        [infoItems addObject:@{@"title": @"返回方式", @"value": self.venderVM.info.returnModelStr}];
    }
    if (returnVenderInfo.contactName.length > 0) {
        [infoItems addObject:@{@"title": @"退货商家", @"value": self.venderVM.info.returnVenderInfo.contactName}];
    }
    if (returnVenderInfo.addressInfo.addressDetail.length > 0) {
        [infoItems addObject:@{@"title": @"退货地址", @"value": self.venderVM.info.returnVenderInfo.addressInfo.addressDetail}];
    }
    if (returnVenderInfo.contactTel.length) {
        [infoItems addObject:@{@"title": @"联系电话", @"value": self.venderVM.info.returnVenderInfo.contactTel}];
    }
    if (returnVenderInfo.expressNo.length > 0) {
        [infoItems addObject:@{@"title": @"物流信息", @"value": [NSString stringWithFormat:@"%@\n%@", returnVenderInfo.expressCompany, returnVenderInfo.expressNo]}];
    } else if (self.venderVM.info.allowExpressNoInput) {
        [infoItems addObject:@{@"title": @"物流信息", @"value": @"未添加", @"hasArrow": @YES}];
    } else {
        
    }

    [self createInfoListWithItems:infoItems];
}

- (void)renderCustomerInfo {
    if (!self.customerVM) return;

    NSMutableArray *infoItems = [NSMutableArray array];
    
    if (self.customerVM.receiveInfo.length > 0) {
        [infoItems addObject:@{@"title": @"收货信息", @"value": self.customerVM.receiveInfo}];
    }
    if (self.customerVM.customerInfo.expressNo.length > 0) {
        [infoItems addObject:@{@"title": @"发货单号", @"value": self.customerVM.customerInfo.expressNo}];
    }
    
    [self createInfoListWithItems:infoItems];
}

#pragma mark - 通用方法

- (void)createInfoListWithItems:(NSArray *)items {
    UIView *lastView = nil;

    for (NSInteger i = 0; i < items.count; i++) {
        NSDictionary *item = items[i];
        UIView *itemView = [self createInfoItemWithTitle:item[@"title"]
                                                   value:item[@"value"]
                                                hasArrow:[item[@"hasArrow"] boolValue]];

        [self.bgView addSubview:itemView];

        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.bgView).offset(18);
            make.trailing.equalTo(self.bgView).offset(-18);
            if (lastView) {
                make.top.equalTo(lastView.mas_bottom).offset(12);
            } else {
                make.top.equalTo(self.bgView).offset(18);
            }
            // 最后一个元素设置底部约束
            if (i == items.count - 1) {
                make.bottom.equalTo(self.bgView).offset(-18);
            }
        }];

        lastView = itemView;
    }
}

- (UIView *)createInfoItemWithTitle:(NSString *)title value:(NSString *)value hasArrow:(BOOL)hasArrow {
    UIView *itemView = [[UIView alloc] init];

    // 标题标签
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = title;
    titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    titleLabel.numberOfLines = 1;
    [itemView addSubview:titleLabel];

    // 值标签
    UILabel *valueLabel = [[UILabel alloc] init];
    valueLabel.text = value;
    valueLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    valueLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    valueLabel.numberOfLines = 0;
    valueLabel.textAlignment = NSTextAlignmentLeft;
    [itemView addSubview:valueLabel];

    // 箭头（如果需要）
    UIImageView *arrowView = nil;
    if (hasArrow) {
        arrowView = [[UIImageView alloc] init];
        arrowView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        arrowView.contentMode = UIViewContentModeScaleAspectFit;
        [itemView addSubview:arrowView];
        valueLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"];
    }

    // 布局
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.bottom.equalTo(itemView);
        make.width.lessThanOrEqualTo(@80);
    }];

    if (hasArrow && arrowView) {
        [arrowView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(itemView);
            make.centerY.equalTo(itemView);
            make.size.mas_equalTo(CGSizeMake(12, 12));
        }];

        [valueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(titleLabel.mas_right).offset(12);
            make.right.equalTo(arrowView.mas_left).offset(-8);
            make.top.bottom.equalTo(itemView);
            make.height.greaterThanOrEqualTo(@22);
        }];
    } else {
        [valueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(titleLabel.mas_right).offset(12);
            make.right.equalTo(itemView);
            make.top.bottom.equalTo(itemView);
            make.height.greaterThanOrEqualTo(@22);
        }];
    }

    return itemView;
}

@end
