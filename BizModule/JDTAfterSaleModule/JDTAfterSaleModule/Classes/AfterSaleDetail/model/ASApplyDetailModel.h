//
//  ASApplyDetailModel.h
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import <Foundation/Foundation.h>
#import "ASApplyListModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, ASReturnMode) {
    ASReturnModeUserShip,  // 用户寄回
    ASReturnModePickUp     // 上门取件
};

@interface ASApplyDetailProcessNodeModel : NSObject

@property (nonatomic, assign) NSInteger order;
/// 节点类型
@property (nonatomic, assign) NSInteger type;
/// 节点文案
@property (nonatomic, copy) NSString *text;
/// 节点完成时间
@property (nonatomic, copy) NSString *finishTime;

@end

@interface ASApplyDetailProcessModel : NSObject

@property (nonatomic, copy) NSString *afsServiceId;

@property (nonatomic, assign) NSInteger currentIndex;

@property (nonatomic, assign) NSInteger nodeCode;
/// 服务单状态节点名词(当前)
@property (nonatomic, copy) NSString *nodeCodeI18nKey;

@property (nonatomic, copy) NSString *nodeContentI18nKey;

@property (nonatomic, copy) NSArray <ASApplyDetailProcessNodeModel *> *nodeStatusList;

@end

@interface ASApplyDetailInfoRefundInfoModel : NSObject
/// 退款方式
@property (nonatomic, assign) NSInteger refundType;

@property (nonatomic, copy) NSString *refundTypeStr;
/// 退款预估金额
@property (nonatomic, strong) NSNumber *estimatedAmount;

@property (nonatomic, copy) NSString *bankName;

@property (nonatomic, copy) NSString *bankCardNo;

@property (nonatomic, copy) NSString *fullName;

@property (nonatomic, copy) NSString *firstName;

@property (nonatomic, copy) NSString *lastName;

@property (nonatomic, copy) NSString *phoneNum;

@end

@interface ASApplyDetailInfoModel : NSObject

@property (nonatomic, copy) NSString *orderId;
/// 售后服务单号
@property (nonatomic, copy) NSString *afsServiceId;

@property (nonatomic, copy) NSArray <ASApplyListItemSkuItemModel *> *skuList;

@property (nonatomic, assign) ASServiceType serviceType;

@property (nonatomic, copy) NSString *serviceTypeStr;
/// 退换货时，发给商家的地址
@property (nonatomic, strong) ASApplyListItemGeneralContactInfoModel *returnVenderInfo;
/// 退换货时，用户的收货地址
@property (nonatomic, strong) ASApplyListItemGeneralContactInfoModel *returnCustomerInfo;
/// 退换货时，上门取件的用户地址
@property (nonatomic, strong) ASApplyListItemGeneralContactInfoModel *pickCustomerInfo;
/// 申请时间
@property (nonatomic, copy) NSString *applyDate;
/// 用户返件方式
@property (nonatomic, assign) ASReturnMode returnModel;

@property (nonatomic, copy) NSString *returnModelStr;

@property (nonatomic, strong) ASApplyListItemVenderModel *venderInfo;
/// 是否展示客户返件物流单号输入框标记
@property (nonatomic, assign) BOOL allowExpressNoInput;

@property (nonatomic, assign) ProductType orderType;
/// 是否允许取消
@property (nonatomic, assign) BOOL allowCancel;
/// 退款信息
@property (nonatomic, strong) ASApplyDetailInfoRefundInfoModel *refundInfoVO;

@property (nonatomic, copy) NSString *remark;

@property (nonatomic, assign) ASServiceStatus status;

@end

@interface ASApplyDetailModel : NSObject
/// 详情
@property (nonatomic, strong) ASApplyDetailInfoModel *afsServiceDetailVo;
/// 进度
@property (nonatomic, strong) ASApplyDetailProcessModel *afsServiceProcessNodeVo;

@end

NS_ASSUME_NONNULL_END
