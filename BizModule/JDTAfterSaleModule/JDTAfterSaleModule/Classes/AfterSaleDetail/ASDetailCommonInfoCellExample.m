//
//  ASDetailCommonInfoCellExample.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/11.
//

#import <Foundation/Foundation.h>
#import "ASDetailCommonInfoCell.h"
#import "ASDetailRefundViewModel.h"
#import "ASDetailOrderViewModel.h"
#import "ASDetailVenderViewModel.h"
#import "ASDetailCustomerViewModel.h"

/*
 * ASDetailCommonInfoCell 使用示例
 * 
 * 这个 Cell 支持四种不同的信息展示类型：
 * 1. ASDetailCommonInfoTypeRefund   - 退款信息
 * 2. ASDetailCommonInfoTypeOrder    - 订单信息  
 * 3. ASDetailCommonInfoTypeVender   - 商家联系信息
 * 4. ASDetailCommonInfoTypeCustomer - 客户联系信息
 */

void exampleUsage() {
    // 创建模拟数据
    ASApplyDetailInfoRefundInfoModel *refundInfo = [[ASApplyDetailInfoRefundInfoModel alloc] init];
    refundInfo.estimatedAmount = @(3376.88);
    refundInfo.refundTypeStr = @"银行卡";
    refundInfo.bankName = @"中国工商银行";
    refundInfo.bankCardNo = @"*******************";
    refundInfo.fullName = @"陈多多";
    refundInfo.phoneNum = @"***********";
    
    // 创建退款信息 ViewModel
    ASDetailRefundViewModel *refundVM = [[ASDetailRefundViewModel alloc] init];
    refundVM.refundInfo = refundInfo;
    refundVM.serviceId = @"***********";
    refundVM.applyTime = @"2020.09.01 16:06:03";
    refundVM.serviceTypeStr = @"退货退款";
    
    // 在 TableView 中使用
    /*
    - (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
        ASDetailCommonInfoCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ASDetailCommonInfoCell" forIndexPath:indexPath];
        
        // 设置 Cell 类型
        cell.type = ASDetailCommonInfoTypeRefund;
        
        // 配置数据
        [cell config:refundVM];
        
        // 渲染内容
        [cell render];
        
        return cell;
    }
    */
}

/*
 * 显示效果说明：
 * 
 * 退款信息 (ASDetailCommonInfoTypeRefund)：
 * ┌─────────────────────────────────────┐
 * │ 退款金额                    ¥3376.88 │
 * │ 服务单号                ***********  │
 * │ 申请时间          2020.09.01 16:06:03 │
 * │ 退货类型                    退货退款  │
 * │ 退款方式                      银行卡  │
 * └─────────────────────────────────────┘
 * 
 * 订单信息 (ASDetailCommonInfoTypeOrder)：
 * ┌─────────────────────────────────────┐
 * │ 服务单号                ***********  │
 * │ 申请时间          2020.09.01 16:06:03 │
 * │ 退货类型                    退货退款  │
 * │ 退款方式                      银行卡  │
 * │ 银行名称                  中国工商银行  │
 * │ 银行卡号          ******************* │
 * │ 用户姓名                      陈多多  │
 * │ 手机号码                ***********  │
 * └─────────────────────────────────────┘
 * 
 * 商家信息 (ASDetailCommonInfoTypeVender)：
 * ┌─────────────────────────────────────┐
 * │ 返回方式                    客户寄回  │
 * │ 退货商家          北京畅晟宏达科技有限公司 │
 * │ 退货地址          上海市奉贤区金汇镇亭工业区 │
 * │                           光泰路1515号 │
 * │ 联系电话                028-37659999  │
 * │ 物流信息                      未添加 → │
 * │ 收货信息          曾娟  15171432606   │
 * │                 四川省成都市武侯区武青东四路8号 │
 * │                     京东成都西南总部   │
 * │ 发货单号                ***********  │
 * └─────────────────────────────────────┘
 * 
 * 客户信息 (ASDetailCommonInfoTypeCustomer)：
 * ┌─────────────────────────────────────┐
 * │ [包含退款信息 + 商家信息的所有内容]      │
 * │                                     │
 * │ ┌─────────────────────────────────┐ │
 * │ │            取消申请              │ │
 * │ └─────────────────────────────────┘ │
 * └─────────────────────────────────────┘
 */
