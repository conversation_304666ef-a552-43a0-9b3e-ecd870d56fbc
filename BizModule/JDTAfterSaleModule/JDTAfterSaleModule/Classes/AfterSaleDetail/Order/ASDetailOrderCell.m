//
//  ASDetailOrderCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailOrderCell.h"
#import "ASDetailOrderViewModel.h"

@interface ASDetailOrderCell ()

@property (nonatomic, strong) ASDetailOrderViewModel *viewModel;

@end

@implementation ASDetailOrderCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    self.viewModel = viewModel;
}

- (void)render {
    
}

@end
