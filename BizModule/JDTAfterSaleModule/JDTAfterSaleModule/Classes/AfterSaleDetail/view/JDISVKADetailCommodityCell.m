//
//  JDISVKADetailCommodityCell.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailCommodityCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>

#import "JDISVKADetailCommodityViewModel.h"
#import "JDISVKAAfterSaleAction.h"
#import <JDISVReactiveObjCModule/RACSubject.h>
#import "JDISVKADetailOrderInfoModel.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVCategoryModule/UIView+JDCDGesture.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
@interface JDISVKADetailCommodityCell()

@property (weak, nonatomic) IBOutlet UIView *mainView;

@property (weak, nonatomic) IBOutlet UIImageView *shopImageView;
@property (weak, nonatomic) IBOutlet UILabel *shopNameLabel;

@property (weak, nonatomic) IBOutlet UIImageView *commodityImageView;

@property (weak, nonatomic) IBOutlet UILabel *commodityNameLabel;

@property (weak, nonatomic) IBOutlet UILabel *priceLabel;
@property (weak, nonatomic) IBOutlet UILabel *priceValue;
@property (weak, nonatomic) IBOutlet UILabel *countLabel;
@property (weak, nonatomic) IBOutlet UILabel *countValue;
@property (weak, nonatomic) IBOutlet UIView *contactView;
@property (weak, nonatomic) IBOutlet UIView *divideLineView;
@property (weak, nonatomic) IBOutlet UIImageView *contactImageView;
@property (weak, nonatomic) IBOutlet UILabel *contactLabel;

#pragma mark 约束
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contactViewWidthConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewTopConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewLeftConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewRightConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewBottomConstraint;

@property (nonatomic,strong)JDISVKADetailCommodityViewModel *viewModel;

@end

@implementation JDISVKADetailCommodityCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self setupCell];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
}

- (void)render{
    self.shopNameLabel.text = self.viewModel.shopName;
    self.commodityNameLabel.text = self.viewModel.skuName;
    self.countValue.text = [NSString stringWithFormat:@"%ld",self.viewModel.appliedCount];
    
    NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
    formatter.numberStyle = NSNumberFormatterDecimalStyle;
    formatter.minimumFractionDigits = 2;
    formatter.maximumFractionDigits = 2;
    NSString *formattedPrice = [formatter stringFromNumber:@(self.viewModel.skuSingleShouldPrice.doubleValue)];
    self.priceValue.text = [NSString stringWithFormat:@"%@%@", PRICETAG, formattedPrice];
    
    [self.commodityImageView jdcd_setImage:[PlatformService getCompleteImageUrl:self.viewModel.skuImage] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    [self.contactView setHidden:!self.viewModel.isShowContactView];
}

- (void)setupCell{
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.shopNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightSemibold);
    self.shopNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.commodityNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.priceLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.priceLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.priceLabel.text = Lang(@"after_sale_price");
    self.priceValue.jdisv_fontPicker = JDISVJDFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.priceValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.countLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.countLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.countValue.jdisv_fontPicker = JDISVJDFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.countValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.countLabel.text = Lang(@"after_sale_count");
    
    
    self.divideLineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.contactLabel.jdisv_fontPicker = JDISVJDFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.contactLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.contactLabel.text = Lang(@"after_sale_detail_contact_service");
    
    self.shopImageView.image = [UIImage ka_iconWithName:JDIF_ICON_SHOP_LINE imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    
    self.contactImageView.image = [UIImage ka_iconWithName:JDIF_ICON_CUSTOMERSERVICE_LINE imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    
    CGSize contactViewSize =  [Lang(@"after_sale_detail_contact_service") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(500, 200)];
    
    self.contactViewWidthConstraint.constant = 20+4+contactViewSize.width;
    
    [self.contactView jd_addTapAction:@selector(clickContactView) withTarget:self];
    
    self.mainViewLeftConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.mainViewTopConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    self.mainViewBottomConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.mainViewRightConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];

    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.mainView.layer.masksToBounds = YES;
    self.commodityImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.commodityImageView.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"] colorWithAlphaComponent:0.02];
}

- (void)clickContactView{
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithDetailClickContactService];
//    NSMutableDictionary *arg = [NSMutableDictionary dictionary];
//    if(self.viewModel.shopId){
//        arg[@"shopId"] = @(self.viewModel.shopId);
//    }
//    if(self.viewModel.venderId){
//        arg[@"venderId"] = @(self.viewModel.venderId);
//    }
//    NSMutableDictionary *par = [NSMutableDictionary dictionary];
//    for(JDISVKAAfsSkuDetailVo* skuInfo in self.viewModel.skus){
//        if([skuInfo isKindOfClass:JDISVKAAfsSkuDetailVo.class]){
//            if(skuInfo.skuId){
//                par[@"pid"] = @(skuInfo.skuId);
//                break;
//            }
//        }else if([skuInfo isKindOfClass:NSDictionary.class]){
//            NSDictionary* skuDic = (NSDictionary*)skuInfo;
//            if(skuDic[@"skuId"]){
//                par[@"pid"] = skuDic[@"skuId"];
//                break;;
//            }
//        }
//    }
//    action.value = arg;
//    arg[@"param"] = [par copy];
//    [self.delegate sendNext:action];
}

@end
