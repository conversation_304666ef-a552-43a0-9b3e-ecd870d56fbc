//
//  JDISVKADetailMainViewModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import <Foundation/Foundation.h>
#import "JDISVKAApplyDetailBaseViewModel.h"
#import "ASApplyDetailModel.h"

@class RACSignal;
@class JDISVKADetailHeaderViewModel;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVKADetailMainViewModel : NSObject

@property (assign,nonatomic) NSInteger orderId;

@property (nonatomic, copy) NSArray <__kindof JDISVKAApplyDetailBaseViewModel *> *viewModelArray;

@property (nonatomic, strong) ASApplyDetailModel *detailModel;

//@property (nonatomic, strong) JDISVKADetailHeaderViewModel *headViewModel;

@property (nonatomic, strong) NSNumber *afsServiceId;

@property (nonatomic, copy) NSString *shopPhone;

@property (nonatomic, assign) BOOL requestIsSuccess;

@property (nonatomic, assign) NSInteger orderType;

@property (nonatomic, assign) NSInteger customerExpect;

@property (nonatomic, assign) BOOL allowExpressNoInput;  //是否展示客户返件物流单号输入框标记

@property (nonatomic, assign) BOOL allowCancel;         //是否允许取消售后服务单

@property (nonatomic, assign) NSInteger afsServiceStep;

@property (assign, nonatomic) NSInteger venderId;

@property (assign, nonatomic) NSInteger shopId;

@property (nonatomic, copy) NSString *expressNumber;
@property (nonatomic, copy) NSString *expressCompanyName;
@property (nonatomic, copy) NSString *expressCompanyId;

- (RACSignal *)requestData;

- (RACSignal *)cancelRequest;

- (RACSignal *)addExpressNumberRequest;

- (RACSignal *)requestProgressHistory;

@end

NS_ASSUME_NONNULL_END
