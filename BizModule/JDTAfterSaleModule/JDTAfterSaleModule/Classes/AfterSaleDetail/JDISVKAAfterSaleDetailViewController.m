//
//  JDISVKAAfterSaleDetailViewController.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKAAfterSaleDetailViewController.h"
#import "JDISVKADetailMainViewModel.h"
#import "JDISVKAApplyDetailBaseViewModel.h"
#import "JDISVKADetailCommodityCell.h"
#import "JDISVKADetailHeaderView.h"
#import "JDISVKADetailOrderInfoCell.h"
#import "JDISVKADetailReturnMoneyCell.h"
#import "JDISVKADetailShopInfoCell.h"
#import "JDISVKAAfterSaleUtils.h"
#import "ASDetailCommonInfoCell.h"
#import "ASDetailProgressCell.h"
#import "ASDetailProductCell.h"
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVCategoryModule/NSObject+JDCDExtend.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

#import "JDISVKADetailHeaderViewModel.h"
#import <JDISVMasonryModule/Masonry.h>

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDBRouterModule/JDRouter.h>
#import "JDISVKAAfterSaleAction.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVKAAfterSaleDeliverView.h"
#import "ASDetailProgressView.h"

@interface JDISVKAAfterSaleDetailViewController ()<UITableViewDelegate,UITableViewDataSource>

@property(nonatomic,strong)UITableView *tableView;

@property (nonatomic,strong)UIView *bottomView;

@property(nonatomic,strong)JDISVKADetailHeaderView *headerView;

@property (nonatomic, strong) ASDetailProgressView *progressView;

@property(nonatomic,strong)JDISVKADetailMainViewModel *viewModel;

@property(nonatomic,strong)KANavigationBarBackgrounItem *backgroundItem;

@property(nonatomic,strong)KANavigationBarButtonStandardItem *backItem;

@property (nonatomic,strong)KAEmptyView *errorView;

@property (nonatomic,strong) KANavigationBar* navBar;

@end

@implementation JDISVKAAfterSaleDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.navigationController.navigationBar setHidden:YES];
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    JDWeakSelf
    navigationBar
    .decorator
    .addBackground(^(KANavigationBarBackgrounItem * _Nonnull item) {
        item.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    })
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        JDStrongSelf
        [self.navigationController popViewControllerAnimated:YES];
    })
    .title(Lang(@"after_sale_detail_title"), NSTextAlignmentCenter) //标题
    .render();
    UILabel *titleLabel = (UILabel *)navigationBar.navigationItem.titleView;
    [titleLabel setTextColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];

    KANavigationBarButtonItem *backIcon = navigationBar.navigationItem.leftBarButtonItems.firstObject;
    KANavigationBarButtonStandardItem *backItem = [[KANavigationBarButtonStandardItem alloc] initWithFrame:backIcon.frame type:KANavigationBarButtonItemTypeBack];
    [backItem setBackgroundImage:[UIImage ka_iconWithName:JDIF_ICON_ARROW_LEFT_BIG imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]]];
    [backItem addTarget:self action:@selector(backAction)];
    navigationBar.navigationItem.leftBarButtonItems = @[backItem];
    
    navigationBar.navigationBarStyle = KANavigationBarStyleLightColor;
    [self setNavigationBarStyle:@1];
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.viewModel.afsServiceId = self.afsServiceId;
    
    [self configTableView];
    
    [self reloadData];
    
    self.navBar = navigationBar;
}

- (void)backAction{
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)reloadData{
    [PlatformService showLoadingInView:self.view];
    if (self.errorView) {
        [self.errorView removeFromSuperview];
    }
    JDWeakSelf
    [[self.viewModel requestData] subscribeError:^(NSError * _Nullable error) {
        [PlatformService dismissInView:self.view];
        self.errorView = [[KAEmptyView alloc] initWithFrame:CGRectMake(0, [UIWindow ka_uikit_navigationHeight], self.view.frame.size.width, self.view.frame.size.height) type:KAEmptyViewTypeNormal];
        self.errorView.decrible = Lang(@"after_sale_load_error");
        self.errorView.coverImage = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypeFailToLoad];
        JDWeakSelf
        [self.errorView addActionButtonWithTitle:Lang(@"after_sale_retry") render:^(UIButton * _Nonnull sender) {
            [sender renderB3];
        } action:^(UIButton * _Nonnull sender) {
            JDStrongSelf
           [self reloadData];
        }];
        [self.view addSubview:self.errorView];
    } completed:^{
        JDStrongSelf
        [PlatformService dismissInView:self.view];
        [self configBottomView];
//        [self.headerView render:self.viewModel.headViewModel];
        
        [self.progressView configWithModel:self.viewModel.headViewModel.progress];
        
        NSString* titleStr;

        if(self.viewModel.customerExpect == 10){
            titleStr = Lang(@"after_sale_detail_return_title");
        }else if(self.viewModel.customerExpect == 20){
            titleStr = Lang(@"after_sale_detail_exchange_title");
        } else if(self.viewModel.customerExpect == 30){
            titleStr = Lang(@"after_sale_detail_return_title");
        }
        else{
            titleStr = Lang(@"after_sale_detail_title");
        }
        [self.navBar reset];
        self.navBar
            .decorator
            .addBackground(^(KANavigationBarBackgrounItem * _Nonnull item) {
                item.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
            })
            .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
                JDStrongSelf
                [self.navigationController popViewControllerAnimated:YES];
            })
            .title(Lang(@"after_sale_detail_exchange_title"), NSTextAlignmentCenter) //标题
            .render();
        
        self.tableView.tableHeaderView.frame = CGRectMake(0, 0, self.view.frame.size.width, [self.viewModel.headViewModel cellHeight]);
        [self.tableView reloadData];
    }];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

- (void)configBottomView{
    if (self.bottomView) {
        [self.bottomView removeFromSuperview];
    }
    self.bottomView = [[UIView alloc] init];
    self.bottomView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    
    if (self.viewModel.allowCancel || self.viewModel.allowExpressNoInput) {
        [self.view addSubview:self.bottomView];
        [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.bottom.mas_equalTo(self.view);
            make.height.mas_equalTo(self.viewModel.safeBottom + 54);
        }];
        [self.tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.view).offset([UIWindow ka_uikit_navigationHeight]);
            make.leading.trailing.mas_equalTo(self.view);
            make.height.mas_equalTo(self.view.frame.size.height - [UIWindow ka_uikit_navigationHeight] - (self.viewModel.safeBottom + 54));
        }];
        if (self.viewModel.allowExpressNoInput && self.viewModel.orderType != 75) {
            UIButton *addDeliveryInfoButton = [[UIButton alloc] init];
            UIButton *cancelAfterSalesButton = [[UIButton alloc] init];
            [cancelAfterSalesButton setTitle:Lang(@"after_sale_detail_cancel_apply") forState:UIControlStateNormal];
            [addDeliveryInfoButton setTitle:Lang(@"after_sale_track_dialog_title") forState:UIControlStateNormal];
            
            [self.bottomView addSubview:addDeliveryInfoButton];
            [self.bottomView addSubview:cancelAfterSalesButton];
            
            CGSize addDeliverySize = [Lang(@"after_sale_track_dialog_title") jdcd_getStringSize:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium] constraintsSize:CGSizeMake(500, 100)];
            [addDeliveryInfoButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.trailing.mas_equalTo(self.bottomView).offset(-18);
                make.width.mas_equalTo(addDeliverySize.width+22);
                make.height.mas_equalTo(30);
                make.top.mas_equalTo(self.bottomView).offset(12);
            }];
            
            CGSize cancelAfterSize = [Lang(@"after_sale_detail_cancel_apply") jdcd_getStringSize:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium] constraintsSize:CGSizeMake(500, 100)];
            [cancelAfterSalesButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.trailing.mas_equalTo(addDeliveryInfoButton.mas_leading).offset(-6);
                make.width.mas_equalTo(cancelAfterSize.width+22);
                make.height.mas_equalTo(30);
                make.centerY.mas_equalTo(addDeliveryInfoButton);
            }];
            
            [cancelAfterSalesButton layoutIfNeeded];
            [addDeliveryInfoButton layoutIfNeeded];
            cancelAfterSalesButton.layer.cornerRadius = 15;
            addDeliveryInfoButton.layer.cornerRadius = 15;
            
            
            [addDeliveryInfoButton renderB4];
            [cancelAfterSalesButton renderB4];
            
            [cancelAfterSalesButton addTarget:self action:@selector(clickCancelAfsButton) forControlEvents:UIControlEventTouchUpInside];
            
            [addDeliveryInfoButton addTarget:self action:@selector(clickAddDeliveryInfoButton) forControlEvents:UIControlEventTouchUpInside];
            if (!self.viewModel.allowCancel) {
                [cancelAfterSalesButton removeFromSuperview];
            }
        }else{
            UIButton *cancelAfterSalesButton = [[UIButton alloc] init];
            [cancelAfterSalesButton setTitle:Lang(@"after_sale_detail_cancel_apply") forState:UIControlStateNormal];
            
            [self.bottomView addSubview:cancelAfterSalesButton];
            
            CGSize cancelAfterSize = [Lang(@"after_sale_detail_cancel_apply") jdcd_getStringSize:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium] constraintsSize:CGSizeMake(500, 100)];
            [cancelAfterSalesButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.trailing.mas_equalTo(self.bottomView).offset(-18);
                make.width.mas_equalTo(cancelAfterSize.width+22);
                make.height.mas_equalTo(30);
                make.top.mas_equalTo(self.bottomView).offset(12);
            }];
            
            [cancelAfterSalesButton layoutIfNeeded];
            cancelAfterSalesButton.layer.cornerRadius = 15;

            
            [cancelAfterSalesButton renderB4];
            
            [cancelAfterSalesButton addTarget:self action:@selector(clickCancelAfsButton) forControlEvents:UIControlEventTouchUpInside];
            
        }
    }else{
        [self.tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.view).offset([UIWindow ka_uikit_navigationHeight]);
            make.leading.trailing.mas_equalTo(self.view);
            make.bottom.mas_equalTo(self.view);
        }];
    }
}
- (void)clickAddDeliveryInfoButton{
    JDISVKAAfterSaleDeliverView* deliverView = [[JDISVKAAfterSaleDeliverView alloc] initWithFrame:CGRectMake(0, 0, UIScreen.mainScreen.bounds.size.width-2*(16+24), 32*2+6)];
    JDWeakSelf
    [JDCDAlert alert].config
    .renderW4(Lang(@"after_sale_track_dialog_title"), Lang(@"after_sale_track_dialog_title_sec"))
    .jdcd_addCustomView(^(JDCDCustomView * _Nonnull custom) {
        custom.isAutoWidth = YES;
        custom.view = deliverView;
    })
    .ka_addAction(Lang(@"after_sale_cancel"), ^(UIButton * _Nonnull button) {
        [button renderB5];
    }, ^{
        
    })
    .ka_addAction(Lang(@"after_sale_reason_confirm"), ^(UIButton * _Nonnull button) {
        [button renderB1WithCornerRadius:15];
    }, ^{
        if (deliverView.expressId.length == 0) {
            [JDCDAlert alert].config
            .renderW1(Lang(@"after_sale_no_track"))
            .jdcd_show();
        }else if(deliverView.expressCampanyName.length == 0){
            [JDCDAlert alert].config
            .renderW1(Lang(@"after_sale_company_dialog_hint"))
            .jdcd_show();
        }else{
            self.viewModel.expressNumber = deliverView.expressId;
            self.viewModel.expressCompanyId = deliverView.expressCampanyId;
            self.viewModel.expressCompanyName = deliverView.expressCampanyName;
            [PlatformService showLoadingInView:self.view];
            JDStrongSelf
            
            [[self.viewModel addExpressNumberRequest] subscribeError:^(NSError * _Nullable error) {
                [JDCDAlert closeWithCompletionBlock:^{
                    [PlatformService dismissInView:self.view];
                    [JDCDAlert alert].config
                    .renderW2Error(Lang(@"after_sale_add_express_delivery_failed"))
                    .jdcd_show();
                }];
            } completed:^{
                [JDCDAlert closeWithCompletionBlock:^{
                    [PlatformService dismissInView:self.view];
                    [JDCDAlert alert].config
                    .renderW2Success(Lang(@"after_sale_add_express_delivery_success"))
                    .jdcd_show();
                    dispatch_async(dispatch_get_main_queue(), ^{
                        JDStrongSelf
                        [self reloadData];
                    });
                }];
            }];
        }
    })
    .jdcd_show();
}

- (void)clickCancelAfsButton{
    JDWeakSelf
    [JDCDAlert alert].config
    .renderW3(Lang(@"after_sale_detail_cancel_dialog_title"))
    .ka_addAction(Lang(@"after_sale_cancel"), ^(UIButton * _Nonnull button) {
        //按钮样式有客户端自己确定
        [button renderB5];
    }, ^{
    })
    .ka_addAction(Lang(@"after_sale_reason_confirm"), ^(UIButton * _Nonnull button) {
        //按钮样式有客户端自己确定
        [button renderB1WithCornerRadius:15];
    }, ^{
        [JDCDAlert alert].config
        .renderW6(Lang(@"after_sale_canel_order"))
        .jdcd_show();
        JDStrongSelf
        [[self.viewModel cancelRequest] subscribeError:^(NSError * _Nullable error) {
            [JDCDAlert closeWithCompletionBlock:^{
                [JDCDAlert alert].config
                .renderW2Error(Lang(@"after_sale_canel_order_failed"))
                .jdcd_show();
            }];
        } completed:^{
            [JDCDAlert closeWithCompletionBlock:^{
                [JDCDAlert alert].config
                .renderW2Success(Lang(@"after_sale_canel_order_success"))
                .jdcd_show();
            }];
            [self reloadData];
        }];
    })
    .jdcd_show();
    
}

- (void)configTableView{
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).offset([UIWindow ka_uikit_navigationHeight]);
        make.leading.trailing.mas_equalTo(self.view);
        make.bottom.mas_equalTo(self.view);
    }];
    
    self.progressView = [[ASDetailProgressView alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, 118)];
    
//    self.headerView = [[JDISVKADetailHeaderView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, [self.viewModel.headViewModel cellHeight])];
//    [self.headerView config];
    self.tableView.tableHeaderView = self.progressView;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.showsHorizontalScrollIndicator = NO;
    self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
}

- (void)eventWithActionType:(JDISVKAAfterSaleAction *)action{
//    if (action.actionType == JDISVKAAfterSaleActionTypeWithDetailClickContactService) {
//        [self openIM:action.value];
//    }else if (action.actionType == JDISVKAAfterSaleActionTypeWithDetailClickPhone){
//        NSString *tellString = [NSString stringWithFormat:@"tel://%@",self.viewModel.shopPhone];
//        [[UIApplication   sharedApplication] openURL:[NSURL URLWithString:tellString]];
//    }else if (action.actionType == JDISVKAAfterSaleActionTypeWithDetailCopyOrderInfoSuccess){
//        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
//        [pasteboard setString:action.value];
//        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:Lang(@"after_sale_copy_success")];
//    }
}

-(void)openIM:(NSDictionary*)param{
    NSMutableDictionary* arg = [param mutableCopy];
    arg[@"controller"] = self;
    arg[@"venderId"] = @(self.viewModel.venderId);
    arg[@"orderId"] = @(self.viewModel.orderId);
    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeIM];
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/openAfterSaleIMController", moduleName] arg:[arg copy] error:nil completion:nil];
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    JDISVKAApplyDetailBaseViewModel *baseViewModel = self.viewModel.viewModelArray[indexPath.row];
    JDISVKAApplyDetailBaseCell *baseCell = [tableView dequeueReusableCellWithIdentifier:[baseViewModel identifier]];
    baseCell.selectionStyle = UITableViewCellSelectionStyleNone;
    baseCell.tableView = tableView;
    baseCell.viewController = self;
    [baseCell config:baseViewModel];
    JDWeakSelf
    [[baseCell.delegate takeUntil:baseCell.rac_prepareForReuseSignal] subscribeNext:^(JDISVKAAfterSaleAction *action) {
        JDStrongSelf
        [self eventWithActionType:action];
    }];
    return baseCell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.requestIsSuccess ? self.viewModel.viewModelArray.count : 0;
}


// 注释掉手动高度计算，使用自动计算高度
// - (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
//     JDISVKAApplyDetailBaseViewModel *baseViewModel = self.viewModel.viewModelArray[indexPath.row];
//     return baseViewModel.cellHeight;
// }

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVKAApplyDetailBaseCell *baseCell = (JDISVKAApplyDetailBaseCell *)cell;
    [baseCell render];
}


- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    CGFloat currentY = scrollView.contentOffset.y;
    self.backgroundItem.alpha = currentY/50;
    
}


- (UITableView *)tableView{
    if (!_tableView) {
        _tableView = [[UITableView alloc] init];
        _tableView.bounces = NO;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;

        // 设置自动计算高度
        _tableView.estimatedRowHeight = 200; // 预估高度
        _tableView.rowHeight = UITableViewAutomaticDimension; // 自动计算高度

        // 注册原有的 Cell
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKADetailCommodityCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKADetailCommodityCell"];
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKADetailOrderInfoCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKADetailOrderInfoCell"];
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKADetailReturnMoneyCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKADetailReturnMoneyCell"];
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKADetailShopInfoCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKADetailShopInfoCell"];

        // 注册新的通用信息 Cell
        [_tableView registerClass:[ASDetailCommonInfoCell class] forCellReuseIdentifier:@"ASDetailCommonInfoCell"];
        [_tableView registerClass:[ASDetailProgressCell class] forCellReuseIdentifier:@"ASDetailProgressCell"];
        [_tableView registerClass:[ASDetailProductCell class] forCellReuseIdentifier:@"ASDetailProductCell"];
    }
    return _tableView;
}

- (JDISVKADetailMainViewModel *)viewModel{
    if (!_viewModel) {
        _viewModel = [[JDISVKADetailMainViewModel alloc] init];
    }
    return _viewModel;
}


@end
