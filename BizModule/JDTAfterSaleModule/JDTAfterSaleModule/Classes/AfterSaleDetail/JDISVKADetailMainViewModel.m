//
//  JDISVKADetailMainViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailMainViewModel.h"
#import "JDISVKADetailCommodityViewModel.h"
#import "JDISVKADetailHeaderViewModel.h"
#import "JDISVKADetailOrderInfoViewModel.h"
#import "JDISVKADetailReturnMoneyViewModel.h"
#import "JDISVKADetailShopInfoViewModel.h"
#import "JDISVKADetailOrderInfoModel.h"
#import "JDISVKAApplyDetailBaseViewModel.h"

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVCategoryModule/NSObject+JDCDExtend.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVYYModelModule/YYModel.h>
#import "JDISVKAAfterSaleCustomModel.h"
#import "JDISVKADetailOrderInfoModel.h"
#import "JDISVAfterTracker.h"
#import "ASApplyDetailModel.h"

#import "ASDetailProgressViewModel.h"
#import "ASDetailProductViewModel.h"
#import "ASDetailRefundViewModel.h"
#import "ASDetailOrderViewModel.h"
#import "ASDetailVenderViewModel.h"
#import "ASDetailCustomerViewModel.h"

@import JDTInfrastructureModule;

@interface JDISVKADetailMainViewModel()

@property(strong,nonatomic) NSDictionary* origDic;

@end

@implementation JDISVKADetailMainViewModel

- (instancetype)init {
    self = [super init];
    if (self) {
        
//        ASDetailProgressViewModel *progressVM = [[ASDetailProgressViewModel alloc] init];
        
//        self.headViewModel = [[JDISVKADetailHeaderViewModel alloc] init];
        
//        NSMutableArray *array = [[NSMutableArray alloc] init];
//        JDISVKADetailShopInfoViewModel *shopInfoViewModel = [[JDISVKADetailShopInfoViewModel alloc] init];
//        JDISVKADetailCommodityViewModel *commodityViewModel = [[JDISVKADetailCommodityViewModel alloc] init];
//        JDISVKADetailReturnMoneyViewModel *returnMoneyViewModel = [[JDISVKADetailReturnMoneyViewModel alloc] init];
//        JDISVKADetailOrderInfoViewModel *orderInfoViewModel = [[JDISVKADetailOrderInfoViewModel alloc] init];
        
//        [array addObject:progressVM];
        
//        [array addObject:shopInfoViewModel];
//        [array addObject:commodityViewModel];
//        [array addObject:returnMoneyViewModel];
//        [array addObject:orderInfoViewModel];
        
//        self.viewModelArray = [array copy];
    }
    return self;
}

- (RACSignal *)requestData{
    NSDictionary *parameters = @{@"afsServiceId":self.afsServiceId};
//    JDWeakSelf
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_record_detail" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            JDStrongSelf
//            {
//                //WT_TODO_fortest 售后服务单详情
////#ifdef DEBUG
////                NSString* path = [NSBundle.mainBundle pathForResource:@"afs_detail2" ofType:@"json"];
////                NSData* data = [NSData dataWithContentsOfFile:path];
////                id obj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:nil];
////                responseObject = obj;
////#endif
//            }
//            self.origDic = responseObject;
//            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
//            
//            if (customModel.success) {
//                NSDictionary *data = customModel.data;
//                [self setupViewModelWithRespose:data];
//                self.requestIsSuccess = YES;
//                [subscriber sendCompleted];
//            }else{
//                self.requestIsSuccess = NO;
//                [subscriber sendError:nil];
//            }
//            
//        }];
        
        [[OOPNetworkManager sharedManager] POST:@"afterSale/getAfterSaleInfo?apiCode=b2c.cbff.afterSale.getAfterSaleInfo" parameters:parameters headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                self.requestIsSuccess = NO;
                [subscriber sendError:error];
            } else {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    self.requestIsSuccess = YES;
                    ASApplyDetailModel *model = [ASApplyDetailModel yy_modelWithDictionary:responseObject[@"data"]];
                    [self setupViewModelWithModel:model];
                    [subscriber sendCompleted];
                }
            }
        }];
        
        return nil;
    }];
    return signal;
}

- (void)setupViewModelWithModel:(ASApplyDetailModel *)model {
    self.detailModel = model;
    
    NSMutableArray *vmMArr = [NSMutableArray array];
    
    ASDetailProgressViewModel *progressVM = [[ASDetailProgressViewModel alloc] init];
    progressVM.model = model.afsServiceProcessNodeVo;
    [vmMArr addObject:progressVM];
    
    ASDetailProductViewModel *productVM = [[ASDetailProductViewModel alloc] init];
    productVM.skuItem = model.afsServiceDetailVo.skuList.firstObject;
    productVM.vender = model.afsServiceDetailVo.venderInfo;
    [vmMArr addObject:productVM];
    
    ASDetailRefundViewModel *refundVM = [[ASDetailRefundViewModel alloc] init];
    refundVM.refundInfo = model.afsServiceDetailVo.refundInfoVO;
    [vmMArr addObject:refundVM];

    ASDetailOrderViewModel *orderVM = [[ASDetailOrderViewModel alloc] init];
    orderVM.info = model.afsServiceDetailVo;
    [vmMArr addObject:orderVM];

    ASDetailVenderViewModel *venderVM = [[ASDetailVenderViewModel alloc] init];
    venderVM.info = model.afsServiceDetailVo;
    [vmMArr addObject:venderVM];

    ASDetailCustomerViewModel *customerVM = [[ASDetailCustomerViewModel alloc] init];
    customerVM.customerInfo = model.afsServiceDetailVo.returnCustomerInfo;
    NSMutableString *receiveInfo = [NSMutableString stringWithString:@""];
    if (customerVM.customerInfo.contactName.length > 0) {
        [receiveInfo appendString:customerVM.customerInfo.contactName];
    }
    if (customerVM.customerInfo.contactTel.length > 0) {
        [receiveInfo appendFormat:@"  %@", customerVM.customerInfo.contactTel];
    }
    if (customerVM.customerInfo.addressInfo.addressDetail.length > 0) {
        [receiveInfo appendFormat:@"\n%@", customerVM.customerInfo.addressInfo.addressDetail];
    }
    customerVM.receiveInfo = receiveInfo;
    [vmMArr addObject:customerVM];
    
    self.viewModelArray = [vmMArr copy];
}

//- (void)setupViewModelWithRespose:(NSDictionary *)responseObject{
//    JDISVKADetailOrderInfoModel *mainModel = [JDISVKADetailOrderInfoModel yy_modelWithDictionary:responseObject];
//    
//    self.orderId = mainModel.afsServiceDetailVo.orderId;
////    JDISVKAAfsServiceProcessNodeVo *processNode = mainModel.afsServiceProcessNodeVo;
////    self.headViewModel.progressStatusTextArray = processNode.nodeStatusList;
////    self.headViewModel.afsServiceStep = mainModel.afsServiceDetailVo.afsServiceStep;
////    self.headViewModel.currentIndex = processNode.currentIndex;
////    self.headViewModel.afsServiceStepName = mainModel.afsServiceDetailVo.afsServiceStatusText;
////    if(self.headViewModel.afsServiceStepName.length == 0){
////        self.headViewModel.afsServiceStepName =
////        mainModel.afsServiceProcessNodeVo.nodeContentI18nKey.length >0?mainModel.afsServiceProcessNodeVo.nodeContentI18nKey:mainModel.afsServiceProcessNodeVo.nodeCodeI18nKey;
////    }
////    if (processNode == nil) {
////        self.headViewModel.isShowProgressView = NO;
////    }else{
////        self.headViewModel.isShowProgressView = YES;
////    }
//    
//    JDISVKADetailOrderInfoViewModel *orderViewModel = [self viewModelInDataArrayWithClass:JDISVKADetailOrderInfoViewModel.class];
//    if (mainModel.afsServiceDetailVo.afsServiceId) {
//        orderViewModel.serverOrderNumber = [NSString stringWithFormat:@"%ld",mainModel.afsServiceDetailVo.afsServiceId];
//    }else{
//        orderViewModel.serverOrderNumber = nil;
//    }
//    NSDate *myApplyDate = [NSDate dateWithTimeIntervalSince1970:([mainModel.afsServiceDetailVo.applyDate doubleValue] / 1000)];
//    //设置时间格式
//    NSDateFormatter *myApplyDateFormatter=[[NSDateFormatter alloc]init];
//    [myApplyDateFormatter setDateFormat:@"YYYY-MM-dd HH:mm:ss"];
//    //将时间转换为字符串
//    NSString *applyTimeStr = [myApplyDateFormatter stringFromDate:myApplyDate];
//    if (mainModel.afsServiceDetailVo.applyDate) {
//        orderViewModel.applyTime = [NSString stringWithFormat:@"%@",applyTimeStr];
//    }else{
//        orderViewModel.applyTime = nil;
//    }
//    
//    orderViewModel.reasonName = mainModel.afsServiceDetailVo.reasonName;
//    orderViewModel.questionDesc = mainModel.afsServiceDetailVo.questionDesc;
//    orderViewModel.serverType = mainModel.afsServiceDetailVo.customerExpect == 20 ? Lang(@"after_sale_bt_record_exchange") : Lang(@"after_sale_return");
//    orderViewModel.customerExpect = mainModel.afsServiceDetailVo.customerExpect;
//    orderViewModel.questionPic = mainModel.afsServiceDetailVo.questionPic;
//    
//    orderViewModel.returnModel = mainModel.afsServiceDetailVo.returnModel;
//    orderViewModel.afsServiceStatus = mainModel.afsServiceDetailVo.afsServiceStatus;
//    orderViewModel.returnModelStr = mainModel.afsServiceDetailVo.returnModelStr;
//    
//    orderViewModel.orderSelf = mainModel.afsServiceDetailVo.orderSelf;
//    //上门取件信息回显
//    if(orderViewModel.returnModel == 72){
//        orderViewModel.pickCustomerInfo = mainModel.afsServiceDetailVo.pickCustomerInfo;
//        orderViewModel.pickStatus = mainModel.afsServiceDetailVo.pickStatus;
//        orderViewModel.pickStatusStr = mainModel.afsServiceDetailVo.pickStatusStr;
//        if (!orderViewModel.orderSelf){//自营订单不展示上门取件时间
//            orderViewModel.afsApplyExpandInfoVo = mainModel.afsServiceDetailVo.afsApplyExpandInfoVo;
//        }
//    }
//    
//    
//    if (mainModel.afsServiceDetailVo.customerExpect != 20) {//非换新
//        orderViewModel.refundType = mainModel.afsServiceDetailVo.refundType;
//        orderViewModel.refundTypeStr = mainModel.afsServiceDetailVo.refundTypeStr;
//        if(mainModel.afsServiceDetailVo.refundType == 30){
//            orderViewModel.bankName = mainModel.afsServiceDetailVo.bankName;
//            orderViewModel.bankCode = mainModel.afsServiceDetailVo.bankCode;
//            orderViewModel.bankAccountHolder = mainModel.afsServiceDetailVo.bankAccountHolder;
//            orderViewModel.bankTel = mainModel.afsServiceDetailVo.bankTel;
//        }
//    }
//    
//    JDISVKAAfsContactInfoVo *customerInfo = mainModel.afsServiceDetailVo.returnCustomerInfo;
//    JDISVKAAfsContactInfoVo *venderInfo =  mainModel.afsServiceDetailVo.returnVenderInfo;
//    NSMutableString* tmpStr = [[NSMutableString alloc] init];
//    if(venderInfo.expressCompany.length){
//        [tmpStr appendString:venderInfo.expressCompany];
//    }
//    if(venderInfo.expressNo.length){
//        if(tmpStr.length){
//            [tmpStr appendString:@" "];
//        }
//        [tmpStr appendString:venderInfo.expressNo];
//    }
//    orderViewModel.deliveryInfo = tmpStr.length?[tmpStr copy]:nil;
//    orderViewModel.allowExpressNoInput = mainModel.afsServiceDetailVo.allowExpressNoInput;
//    
//    orderViewModel.orderType = mainModel.afsServiceDetailVo.orderType;
//    
//    self.allowExpressNoInput = orderViewModel.allowExpressNoInput;
//    
//    self.allowCancel = mainModel.afsServiceDetailVo.allowCancel;
//    
//    
//    if (mainModel.afsServiceDetailVo.returnModel != 70) {
//        orderViewModel.invoiceNumber = customerInfo.expressNo;
//    }else{
//        orderViewModel.deliveryInfo = nil;
//        orderViewModel.invoiceNumber = nil;
//    }
//    
//    if (mainModel.afsServiceDetailVo.customerExpect == 20 && mainModel.afsServiceDetailVo.returnModel != 70) {
//        orderViewModel.receiverName = customerInfo.contactName;
//        orderViewModel.expressInfos = customerInfo.expressInfos;
//        orderViewModel.returnExpressCompany = customerInfo.expressCompany;
//        orderViewModel.receiverPhone = customerInfo.contactTel;
//        orderViewModel.receiverAddress = customerInfo.addressInfo.address;
//        orderViewModel.isShowReceivingArea = YES;
//    }else{
//        orderViewModel.isShowReceivingArea = NO;
//    }
//    
//    self.customerExpect = mainModel.afsServiceDetailVo.customerExpect;
//    
//    JDISVKADetailShopInfoViewModel *shopInfoViewModel = [self viewModelInDataArrayWithClass:JDISVKADetailShopInfoViewModel.class];
//    JDISVKAAfsStoreInfo *storeInfo = mainModel.afsServiceDetailVo.storeInfo;
//    if(mainModel.afsServiceDetailVo.returnModel == 72){
//        //上门取件屏蔽换新、退货地址楼层
//        NSMutableArray *tempArray = [NSMutableArray arrayWithArray:self.viewModelArray];
//        [tempArray removeObject:shopInfoViewModel];
//        self.viewModelArray = [tempArray copy];
//    } else if (storeInfo || mainModel.afsServiceDetailVo.returnVenderInfo) {
//        if (mainModel.afsServiceDetailVo.returnModel == 40 ||
//            mainModel.afsServiceDetailVo.returnModel == 72) {
//            shopInfoViewModel.returnType = 1;
//            shopInfoViewModel.type1Value = venderInfo.addressInfo.address;
//            shopInfoViewModel.type2Value = venderInfo.contactTel;
//            shopInfoViewModel.cellMainName = venderInfo.contactName;
//            
//        }else{//70 : 历史逻辑补充,门店自提
//            shopInfoViewModel.cellMainName = storeInfo.storeName;
//            shopInfoViewModel.returnType = 2;
//            shopInfoViewModel.type1Value = [NSString stringWithFormat:Lang(@"after_sale_time"),storeInfo.businessTime];
//            shopInfoViewModel.type2Value = storeInfo.storeAddress;
//            shopInfoViewModel.phone = storeInfo.phone;
//            self.shopPhone = storeInfo.phone;
//        }
//    }else{
//        NSMutableArray *tempArray = [NSMutableArray arrayWithArray:self.viewModelArray];
//        [tempArray removeObject:shopInfoViewModel];
//        self.viewModelArray = [tempArray copy];
//    }
//    
//    
//    JDISVKADetailCommodityViewModel *commodityViewModel = [self viewModelInDataArrayWithClass:JDISVKADetailCommodityViewModel.class];
//    
//    JDISVKAAfsSkuDetailVo *skuDetail;
//    NSMutableArray* skus = [NSMutableArray array];
//    for (NSDictionary *dic in mainModel.afsServiceDetailVo.skuList) {
//        skuDetail = [JDISVKAAfsSkuDetailVo yy_modelWithDictionary:dic];
//        if(skuDetail.skuId){
//            [skus addObject:[@(skuDetail.skuId) stringValue]];
//        }
//    }
//    commodityViewModel.shopName = mainModel.afsServiceDetailVo.venderInfo.venderName;
//    commodityViewModel.skuName = skuDetail.skuName;
//    if(skuDetail.skuSingleShouldPrice){
//        commodityViewModel.skuSingleShouldPrice = [NSString stringWithFormat:@"%@",skuDetail.skuSingleShouldPrice?:@"0"];
//    }else{
//        commodityViewModel.skuSingleShouldPrice = @"--";
//    }
//    commodityViewModel.skuImage = skuDetail.skuImg;
//    commodityViewModel.skuCount = skuDetail.skuCount;
//    commodityViewModel.appliedCount = skuDetail.appliedCount;
//    commodityViewModel.isShowContactView = mainModel.afsServiceDetailVo.venderInfo.displayChatBtn;
//    if (KSAAPP){//KSA IM常驻
//        commodityViewModel.isShowContactView = YES;
//    }
//    commodityViewModel.venderId = mainModel.afsServiceDetailVo.venderInfo.venderId;
//    commodityViewModel.shopId = mainModel.afsServiceDetailVo.venderInfo.shopId;
//    commodityViewModel.skus = mainModel.afsServiceDetailVo.skuList;
//    
//    self.venderId = commodityViewModel.venderId;
//    self.shopId = commodityViewModel.shopId;
//    JDISVKADetailReturnMoneyViewModel *returnMoneyViewModel = [self viewModelInDataArrayWithClass:JDISVKADetailReturnMoneyViewModel.class];
//    returnMoneyViewModel.skuPrice = mainModel.afsServiceDetailVo.refundAmountStr;
//
//    if (returnMoneyViewModel.skuPrice == nil || mainModel.afsServiceDetailVo.customerExpect == 20) {
//        NSMutableArray *tempArray = [NSMutableArray arrayWithArray:self.viewModelArray];
//        [tempArray removeObject:returnMoneyViewModel];
//        self.viewModelArray = [tempArray copy];
//    }
//    
//    self.afsServiceStep = mainModel.afsServiceDetailVo.afsServiceStep;
//    
//    [shopInfoViewModel updataViewModel];
//    [commodityViewModel updataViewModel];
//    [returnMoneyViewModel updataViewModel];
//    [orderViewModel updataViewModel];
//    
//    {
//
//        NSString* skuStr = [skus componentsJoinedByString:@","];
//        NSInteger orderid = mainModel.afsServiceDetailVo.orderId;
//        NSInteger afsServiceId = mainModel.afsServiceDetailVo.afsServiceId;
//        [JDISVAfterTracker PV:@"Repair_SerivceDetail"
//                        param:@{@"page_id":@"Repair_SerivceDetail",
//                                @"service_orderid":@(afsServiceId),
//                                @"orderid":@(orderid),
//                                @"skuId":skuStr?:@""
//                              }];
//        
//    }
//    
//}

-(NSInteger)orderType{
    NSNumber* orderType = self.origDic[@"data"][@"afsServiceDetailVo"][@"orderType"];
    return orderType.intValue;
}

- (RACSignal *)cancelRequest{
    NSDictionary *parameters = @{@"afsServiceId":self.afsServiceId};
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_record_cancel" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
            
            if (customModel.success) {
                [subscriber sendCompleted];
            }else{
                [subscriber sendError:nil];
            }
            
        }];
        return nil;
    }];
    return signal;
}

- (RACSignal *)addExpressNumberRequest{
    NSDictionary *parameters = @{@"afsServiceId":self.afsServiceId,@"wayBillNo":self.expressNumber,@"expressCompany":self.expressCompanyName};
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        NSString* path = @"/afterSale/saveReturnTrackNoAndExpress/2.0";
        NSString* path = @"se_afs_return_tarckno_press_save/2.0";
        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:path function:@"" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
            
            if (customModel.success) {
                [subscriber sendCompleted];
            }else{
                [subscriber sendError:nil];
            }
            
        }];
        return nil;
    }];
    return signal;
}

- (RACSignal *)requestProgressHistory {
    NSDictionary *params = @{
        @"afsServiceId": self.afsServiceId
    };
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [[OOPNetworkManager sharedManager] POST:@"afterSale/getAfsHistory?apiCode=b2c.cbff.afterSale.getAfsHistory" parameters:params headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if ([responseObject[@"code"] isEqualToString:@"0"]) {
                NSArray <ASApplyDetailProcessNodeModel *> *modelArr = [NSArray yy_modelArrayWithClass:[ASApplyDetailProcessNodeModel class] json:responseObject[@"data"]];
                [subscriber sendNext:modelArr];
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        return nil;
    }];
    return signal;
}

//- (__kindof JDISVKAApplyDetailBaseViewModel *)viewModelInDataArrayWithClass:(Class)aClass{
//    for (JDISVKAApplyDetailBaseViewModel *viewModel in self.viewModelArray) {
//        if ([viewModel isKindOfClass:aClass]) {
//            return viewModel;
//        }
//    }
//    return nil;
//}

//- (JDISVKADetailHeaderViewModel *)headViewModel{
//    if (!_headViewModel) {
//        _headViewModel = [[JDISVKADetailHeaderViewModel alloc] init];
//    }
//    return _headViewModel;
//}

- (NSArray<__kindof JDISVKAApplyDetailBaseViewModel *> *)viewModelArray {
    if (!_viewModelArray) {
        _viewModelArray = [[NSArray alloc] init];
    }
    return _viewModelArray;
}

@end
