//
//  ASDetailVenderViewModel.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailVenderViewModel.h"

@import JDISVThemeModule;
@import JDISVCategoryModule;

@implementation ASDetailVenderViewModel

- (NSString *)identifier {
    return @"ASDetailCommonInfoCell";
}

- (CGFloat)cellHeight {
    return [self heightForContainer];
}

- (CGFloat)heightForContainer {
    CGFloat height = 0;
    // 间距
    CGFloat space = 12;
    // 上下内边距
    CGFloat padding = 36;
    if (self.info.returnModelStr.length > 0) {
        CGFloat modeHeight = 22;
        height += modeHeight;
    }
    CGFloat width = [UIScreen mainScreen].bounds.size.width - 16 - 36 - 80 - 12;
    UIFont *font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    if (self.info.returnVenderInfo.contactName.length > 0) {
        CGFloat venderNameHeight = [self.info.returnVenderInfo.contactName jdcd_sizeWithContainer:CGSizeMake(width, CGFLOAT_MAX) textFont:font maxNumberOfLines:0].height;
        height += (space + venderNameHeight);
    }
    if (self.info.returnVenderInfo.addressInfo.addressDetail.length > 0) {
        CGFloat addressDetailHeight = [self.info.returnVenderInfo.addressInfo.addressDetail jdcd_sizeWithContainer:CGSizeMake(width, CGFLOAT_MAX) textFont:font maxNumberOfLines:0].height;
        height += (space + addressDetailHeight);
    }
    if (self.info.returnVenderInfo.contactTel.length > 0) {
        CGFloat telHeight = 22;
        height += (space + telHeight);
    }
    if (self.info.returnVenderInfo.expressNo.length > 0) {
        CGFloat expressNoHeight = 48;
        height += (space + expressNoHeight);
    } else if (self.info.allowExpressNoInput) {
        CGFloat expressNoHeight = 22;
        height += (space + expressNoHeight);
    }
    if (height > 0) {
        height += padding;
    }
    return height;
}

@end
