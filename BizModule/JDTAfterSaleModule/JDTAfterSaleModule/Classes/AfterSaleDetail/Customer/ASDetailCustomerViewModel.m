//
//  ASDetailCustomerViewModel.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailCustomerViewModel.h"

@import JDISVThemeModule;
@import JDISVCategoryModule;

@implementation ASDetailCustomerViewModel

- (NSString *)identifier {
    return @"ASDetailCommonInfoCell";
}

- (CGFloat)cellHeight {
    //  + 内容高度
    return [self heightForContainer];
}

- (CGFloat)heightForContainer {
    CGFloat height = 0;
    CGFloat space = 12;
    // 上下内边距
    CGFloat padding = 36;
    if (self.receiveInfo.length > 0) {
        CGFloat width = [UIScreen mainScreen].bounds.size.width - 16 - 36 - 80 - 12;
        UIFont *font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        CGFloat receiveInfoHeight = [self.receiveInfo jdcd_sizeWithContainer:CGSizeMake(width, CGFLOAT_MAX) textFont:font maxNumberOfLines:0].height;
        height += receiveInfoHeight;
    }
    if (self.customerInfo.expressNo.length > 0) {
        CGFloat expressNoHeight = 22;
        height += (space + expressNoHeight);
    }
    if (height > 0) {
        height += padding;
    }
    return height;
}

@end
