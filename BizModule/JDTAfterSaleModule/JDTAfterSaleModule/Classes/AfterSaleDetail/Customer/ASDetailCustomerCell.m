//
//  ASDetailCustomerCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailCustomerCell.h"
#import "ASDetailCustomerViewModel.h"

@interface ASDetailCustomerCell ()

@property (nonatomic, strong) ASDetailCustomerViewModel *viewModel;

@end

@implementation ASDetailCustomerCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    self.viewModel = viewModel;
}

- (void)render {
    
}

@end
