//
//  ASDetailRefundCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailRefundCell.h"
#import "ASDetailRefundViewModel.h"
#import <JDISVMasonryModule/Masonry.h>

@import JDISVThemeModule;

@interface ASDetailRefundCell ()

@property (nonatomic, strong) ASDetailRefundViewModel *viewModel;

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *detailLabel;

@end

@implementation ASDetailRefundCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    [self.contentView addSubview:self.bgView];
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.detailLabel];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(8, 8, 8, 8));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        
    }];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    self.viewModel = viewModel;
}

- (void)render {
    
}

#pragma mark - Getter and Setter
- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
        _bgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _bgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        _bgView.layer.masksToBounds = YES;
    }
    return _bgView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _titleLabel;
}

- (UILabel *)detailLabel {
    if (!_detailLabel) {
        _detailLabel = [[UILabel alloc] init];
        _detailLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _detailLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _detailLabel;
}

@end
