//
//  ASDetailRefundCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailRefundCell.h"
#import "ASDetailRefundViewModel.h"

@interface ASDetailRefundCell ()

@property (nonatomic, strong) ASDetailRefundViewModel *viewModel;

@end

@implementation ASDetailRefundCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    self.viewModel = viewModel;
}

- (void)render {
    
}

@end
