//
//  ASDetailProductCellExample.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/11.
//  使用示例

#import "ASDetailProductCell.h"
#import "ASDetailProductViewModel.h"
#import "ASApplyListModel.h"

// 使用示例
void exampleUsageForProductCell() {
    // 创建商品数据模型
    ASApplyListItemSkuItemModel *skuItem = [[ASApplyListItemSkuItemModel alloc] init];
    skuItem.skuImg = @"https://example.com/product.jpg";
    skuItem.skuId = @"123456";
    skuItem.skuName = @"美的空调 全直流变频一级能效挂式冷暖安静智能WiFi挂机 以旧换新";
    skuItem.skuCount = 1;
    skuItem.skuPrice = @(1688.88);
    skuItem.skuSingleShouldPrice = @(1688.88);
    skuItem.canApply = YES;
    skuItem.canApplyCount = @(1);
    
    // 创建 ViewModel
    ASDetailProductViewModel *viewModel = [[ASDetailProductViewModel alloc] init];
    viewModel.skuItem = skuItem;
    
    // 在 TableView 中使用
    /*
    - (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
        ASDetailProductCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ASDetailProductCell" forIndexPath:indexPath];
        [cell config:viewModel];
        [cell render];
        return cell;
    }
    
    - (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
        return viewModel.cellHeight;
    }
    */
}

/*
 * 集成说明：
 * 
 * 1. 在 TableViewController 中注册 Cell：
 *    [self.tableView registerClass:[ASDetailProductCell class] forCellReuseIdentifier:@"ASDetailProductCell"];
 * 
 * 2. 在数据源方法中使用：
 *    - (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
 *        ASDetailProductCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ASDetailProductCell" forIndexPath:indexPath];
 *        
 *        // 创建 ViewModel
 *        ASDetailProductViewModel *viewModel = [[ASDetailProductViewModel alloc] init];
 *        viewModel.skuItem = self.dataSource[indexPath.row]; // 你的数据源
 *        
 *        [cell config:viewModel];
 *        [cell render];
 *        return cell;
 *    }
 * 
 * 3. 设置行高：
 *    - (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
 *        return 178; // 或者使用 viewModel.cellHeight
 *    }
 */
