//
//  ASDetailProductCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/10.
//

#import "ASDetailProductCell.h"
#import "ASDetailProductViewModel.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

@interface ASDetailProductCell ()

@property (nonatomic, strong) ASDetailProductViewModel *viewModel;

// UI 组件
@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIImageView *shopIconView;
@property (nonatomic, strong) UILabel *shopNameLabel;
@property (nonatomic, strong) UIImageView *productImageView;
@property (nonatomic, strong) UILabel *productNameLabel;
@property (nonatomic, strong) UILabel *priceLabel;
@property (nonatomic, strong) UILabel *quantityLabel;

@end

@implementation ASDetailProductCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];

    [self.contentView addSubview:self.bgView];
    [self.bgView addSubview:self.shopIconView];
    [self.bgView addSubview:self.shopNameLabel];
    [self.bgView addSubview:self.productImageView];
    [self.bgView addSubview:self.productNameLabel];
    [self.bgView addSubview:self.priceLabel];
    [self.bgView addSubview:self.quantityLabel];

    [self setupConstraints];
}

- (void)setupConstraints {
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(8, 8, 8, 8));
    }];

    [self.shopIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.bgView).offset(18);
        make.leading.mas_equalTo(self.bgView).offset(18);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];

    [self.shopNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.shopIconView.mas_trailing).offset(2);
        make.centerY.mas_equalTo(self.shopIconView);
        make.trailing.mas_lessThanOrEqualTo(self.bgView).offset(-18);
    }];

    [self.productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.shopIconView.mas_bottom).offset(22);
        make.leading.mas_equalTo(self.bgView).offset(18);
        make.size.mas_equalTo(CGSizeMake(80, 80));
    }];

    [self.productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productImageView);
        make.leading.mas_equalTo(self.productImageView.mas_trailing).offset(12);
        make.trailing.mas_equalTo(self.bgView).offset(-18);
        make.height.mas_lessThanOrEqualTo(42);
    }];

    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productNameLabel);
        make.bottom.equalTo(self.productImageView.mas_bottom).offset(-5);
        make.height.mas_equalTo(20);
    }];

    [self.quantityLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.priceLabel.mas_trailing).offset(12);
        make.trailing.mas_equalTo(self.bgView).offset(-18);
        make.bottom.mas_equalTo(self.priceLabel);
        make.height.mas_equalTo(20);
    }];
    
    [self.priceLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel {
    self.viewModel = viewModel;
}

- (void)render {
    if (!self.viewModel || !self.viewModel.skuItem) {
        return;
    }

    ASApplyListItemSkuItemModel *skuItem = self.viewModel.skuItem;
    
    // 设置店铺名称
    self.shopNameLabel.text = self.viewModel.vender.shopName;
    
    // 设置商品名称
    self.productNameLabel.text = skuItem.skuName ?: @"";
    
    NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
    NSAttributedString *priceLabelPart = [[NSAttributedString alloc] initWithString:@"单价 "];
    [priceAttributedString appendAttributedString:priceLabelPart];
    
    NSMutableAttributedString *priceValuePart = [[NSMutableAttributedString alloc] init];
    [priceValuePart KA_renderWithPrice:skuItem.skuPrice.floatValue type:KAPriceTypeP4 colorType:@"#C7"];
    [priceAttributedString appendAttributedString:priceValuePart];

    self.priceLabel.attributedText = priceAttributedString;

    NSMutableAttributedString *quantityAttributedString = [[NSMutableAttributedString alloc] init];
    NSAttributedString *quantityLabelPart = [[NSAttributedString alloc] initWithString:@"数量 "];
    [quantityAttributedString appendAttributedString:quantityLabelPart];

    NSAttributedString *quantityValuePart = [[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%ld", (long)skuItem.skuCount]
                                                                            attributes:@{
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
    }];
    [quantityAttributedString appendAttributedString:quantityValuePart];

    self.quantityLabel.attributedText = quantityAttributedString;

    // 设置商品图片
    NSString *imageUrl = [PlatformService getCompleteImageUrl:skuItem.skuImg moduleType:@""];
    UIImage *placeholder = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
    [self.productImageView jdcd_setImage:imageUrl placeHolder:placeholder contentMode:UIViewContentModeScaleAspectFit];
}

#pragma mark - Lazy Loading

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
        _bgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _bgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        _bgView.layer.masksToBounds = YES;
    }
    return _bgView;
}

- (UIImageView *)shopIconView {
    if (!_shopIconView) {
        _shopIconView = [[UIImageView alloc] init];
        _shopIconView.image = [UIImage ka_iconWithName:JDIF_ICON_SHOP_LINE
                                             imageSize:CGSizeMake(20, 20)
                                                 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    }
    return _shopIconView;
}

- (UILabel *)shopNameLabel {
    if (!_shopNameLabel) {
        _shopNameLabel = [[UILabel alloc] init];
        _shopNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
        _shopNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _shopNameLabel.numberOfLines = 1;
    }
    return _shopNameLabel;
}

- (UIImageView *)productImageView {
    if (!_productImageView) {
        _productImageView = [[UIImageView alloc] init];
        _productImageView.contentMode = UIViewContentModeScaleAspectFit;
        _productImageView.layer.cornerRadius = 8;
        _productImageView.layer.masksToBounds = YES;
        _productImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _productImageView;
}

- (UILabel *)productNameLabel {
    if (!_productNameLabel) {
        _productNameLabel = [[UILabel alloc] init];
        _productNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightRegular);
        _productNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _productNameLabel.numberOfLines = 0;
        _productNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    }
    return _productNameLabel;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[UILabel alloc] init];
        _priceLabel.numberOfLines = 1;
        _priceLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _priceLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _priceLabel;
}

- (UILabel *)quantityLabel {
    if (!_quantityLabel) {
        _quantityLabel = [[UILabel alloc] init];
        _quantityLabel.numberOfLines = 1;
        _quantityLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _quantityLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _quantityLabel;
}

@end
