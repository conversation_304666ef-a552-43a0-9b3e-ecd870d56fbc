# ASDetailProductCell 商品展示组件

## 概述

`ASDetailProductCell` 是一个用于在售后详情页面展示商品信息的自定义 TableViewCell 组件，数据源为 `ASApplyListItemSkuItemModel`。

## 功能特性

- ✅ 展示店铺信息（图标 + 店铺名称）
- ✅ 展示商品图片（80x80pt，圆角设计）
- ✅ 展示商品名称（支持多行显示）
- ✅ 展示商品价格（使用项目价格格式化）
- ✅ 展示商品数量
- ✅ 使用项目主题系统（颜色、字体、间距、圆角）
- ✅ 自适应布局，支持不同屏幕尺寸
- ✅ 圆角卡片式设计

## 视觉设计

### 布局结构
```
┌─────────────────────────────────────┐
│ 🏪 iBasso数码旗舰店                    │
│                                     │
│ [商品图片]  美的空调 全直流变频一级能效挂式  │
│  80x80    冷暖安静智能WiFi挂机 以旧换新   │
│           ¥1688.88                  │
│           数量 1                     │
└─────────────────────────────────────┘
```

### 样式规范
- **背景**：白色圆角卡片（#C1）
- **边距**：外边距 12pt，内边距 18pt
- **圆角**：使用主题系统 #R1
- **商品图片**：80x80pt，8pt 圆角
- **字体**：使用主题系统 #T7
- **颜色**：主色 #C7，辅助色 #C5

## 数据模型

### ASApplyListItemSkuItemModel

| 属性 | 类型 | 说明 | 在 Cell 中的用途 |
|------|------|------|------------------|
| `skuImg` | NSString | 商品图片 URL | 商品图片展示 |
| `skuName` | NSString | 商品名称 | 商品标题 |
| `skuCount` | NSInteger | 商品数量 | 数量显示 |
| `skuSingleShouldPrice` | NSNumber | 商品实际应付价格 | 价格展示 |
| `skuId` | NSString | 商品 ID | 标识符（暂未使用） |

## 使用方法

### 1. 注册 Cell

```objc
[self.tableView registerClass:[ASDetailProductCell class] 
       forCellReuseIdentifier:@"ASDetailProductCell"];
```

### 2. 配置数据源

```objc
- (UITableViewCell *)tableView:(UITableView *)tableView 
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    
    ASDetailProductCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ASDetailProductCell" 
                                                                forIndexPath:indexPath];
    
    // 创建 ViewModel
    ASDetailProductViewModel *viewModel = [[ASDetailProductViewModel alloc] init];
    viewModel.skuItem = self.dataSource[indexPath.row]; // 你的 ASApplyListItemSkuItemModel 数据
    
    [cell config:viewModel];
    [cell render];
    return cell;
}
```

### 3. 设置行高

```objc
- (CGFloat)tableView:(UITableView *)tableView 
           heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 178; // 固定高度
}
```

## 技术实现

### 架构模式
- **MVVM 模式**：使用 `ASDetailProductViewModel` 作为数据适配层
- **基类继承**：继承自 `JDISVKAApplyDetailBaseCell`
- **主题系统**：完全集成项目主题系统

### 关键组件
- `bgView`：白色圆角背景容器
- `shopIconView`：店铺图标
- `shopNameLabel`：店铺名称
- `productImageView`：商品图片
- `productNameLabel`：商品名称（支持 2 行）
- `priceLabel`：价格标签
- `quantityLabel`：数量标签

### 约束布局
- 使用 `Masonry` 进行自动布局
- 响应式设计，适配不同屏幕尺寸
- 固定商品图片尺寸，文字内容自适应

## 注意事项

1. **图片加载**：使用项目的图片加载框架，支持占位图
2. **价格格式化**：使用 `KA_renderWithPrice` 方法格式化价格显示
3. **店铺信息**：当前使用固定店铺名称，实际使用时需要从数据源获取
4. **文字截断**：商品名称超过 2 行时自动截断
5. **主题适配**：完全使用项目主题系统，支持主题切换

## 扩展建议

1. **店铺信息**：可以扩展支持从 `ASApplyListItemVenderModel` 获取店铺信息
2. **点击事件**：可以添加商品点击跳转功能
3. **状态显示**：可以根据 `canApply` 等字段显示不同状态
4. **规格信息**：可以扩展显示商品规格信息
