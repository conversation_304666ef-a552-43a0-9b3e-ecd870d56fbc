//
//  JDISVKAApplyDetailReturnMoneyCell.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/26.
//

#import "JDISVKAApplyDetailReturnMoneyCell.h"
#import "JDISVKAApplyDetailReturnMoneyViewModel.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVKAAfterSaleUtils.h"
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import "JDISVKAAfterSaleAction.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import "NSString+JDISVAfterExtension.h"


@interface JDISVKAApplyDetailReturnMoneyCell()<UITextFieldDelegate>

@property (nonatomic,strong)UIView *returnMoneyView;

@property (nonatomic,strong)UILabel *returnMoneyLabel;
@property (nonatomic,strong)UILabel *returnMoneyValue;
@property (nonatomic,strong)UIImageView *returnMoneyArrowImg;
@property (nonatomic,strong) UIButton* returnMoneyBtn;


@property (nonatomic,strong)UIView *warningView;
@property (nonatomic,strong)UIImageView *warningImg;
@property (nonatomic,strong)UILabel *warningLabel;
@property (nonatomic,strong)UILabel *warningPointLabel;

@property (nonatomic,strong)UILabel *bankNameLabel;
@property (nonatomic,strong)UIView *bankNameTapView;
@property (nonatomic,strong)UITextField *bankNameTextF;
@property (nonatomic,strong)UIImageView *bankNameArrowImg;
@property (nonatomic,strong)UIView *bankNameLine;

@property (nonatomic,strong)UILabel *bankCardNumLabel;
@property (nonatomic,strong)UILabel *bankCardNumCodeLabel;

@property (nonatomic,strong)UILabel *bankAccountNameLabel;

@property (nonatomic,strong)UILabel *phoneNumberCodeLabel;

@property (nonatomic,strong)JDISVKAApplyDetailReturnMoneyViewModel *viewModel;

@end

@implementation JDISVKAApplyDetailReturnMoneyCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupCell];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(textFieldDidChanged:) name:UITextFieldTextDidChangeNotification object:nil];

    }
    return self;
}

- (void)render{
    self.returnMoneyValue.text = self.viewModel.returnMoneyValue;
    self.bankNameTextF.text = self.viewModel.bankName;
    if(self.viewModel.type == 30){
        if(self.viewModel.needShowPointRefund){
            if(self.viewModel.returnBank){
                [self setupCODBankView:YES];
            }else{
                self.returnMoneyValue.text = self.viewModel.refundPointinfo;
                [self setupCODPointView];
            }
        }else{
            [self setupCODBankView:NO];
        }

    }
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
}

- (void)setupCell{
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    [self.contentView addSubview:self.returnMoneyView];
    [self.returnMoneyView addSubview:self.returnMoneyLabel];
    [self.returnMoneyView addSubview:self.returnMoneyValue];
    
    [self.returnMoneyView mas_makeConstraints:^(MASConstraintMaker *make){
        make.bottom.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]);
        make.top.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"]);
        make.leading.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
        make.trailing.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
    }];
    

    CGSize returnMoneyLabelSize = [self.returnMoneyLabel.text jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(MAXFLOAT, 20))];
    [self.returnMoneyLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(18);
        make.leading.mas_equalTo(18);
        make.height.mas_offset(20);
        make.width.mas_offset(returnMoneyLabelSize.width+1);

    }];
    
    [self.returnMoneyValue mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(18);
        make.leading.equalTo(self.returnMoneyLabel.mas_trailing).offset(10);
        make.trailing.mas_equalTo(-18);
        make.height.mas_offset(20);
    }];
    
    self.returnMoneyView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
}
-(void)tapRefundType:(UIButton*)btn{
    NSLog(@"tapRefundType");
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithRefund];
//    [self.delegate sendNext:action];
}

- (void)setupCODPointView{
    [self.returnMoneyView addSubview:self.warningView];
    [self.warningView addSubview:self.warningImg];
    [self.warningView addSubview:self.warningPointLabel];
    [self.returnMoneyView addSubview:self.returnMoneyArrowImg];
    [self addSubview:self.returnMoneyBtn];
    
    [self.returnMoneyArrowImg mas_makeConstraints:^(MASConstraintMaker *make){
        make.centerY.equalTo(self.returnMoneyLabel);
        make.trailing.mas_equalTo(-21);
        make.width.height.mas_equalTo(10);
    }];
    [self.returnMoneyValue mas_updateConstraints:^(MASConstraintMaker *make){
//        make.top.mas_equalTo(18);
//        make.leading.equalTo(self.returnMoneyLabel.mas_trailing).offset(10);
        make.trailing.equalTo(self.returnMoneyArrowImg.mas_leading).mas_offset(-6);
//        make.height.mas_offset(20);
    }];
    
    
    CGSize warningLabelSize = [Lang(@"after_sale_apply_point_warning") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightRegular)] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-93, 500)];
    
    [self.warningView mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.returnMoneyLabel.mas_bottom).offset(20);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(warningLabelSize.height+17);
    }];
    [self.warningImg mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(8);
        make.leading.mas_equalTo(10);
        make.width.height.mas_equalTo(18);
    }];
    [self.warningPointLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(8);
        make.leading.equalTo(self.warningImg.mas_trailing).offset(5);
        make.trailing.mas_equalTo(-10);
        make.height.mas_equalTo(warningLabelSize.height+1);
    }];
    
    [self.returnMoneyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.returnMoneyValue.mas_top).mas_offset(-5);
        make.bottom.mas_equalTo(self.returnMoneyValue.mas_bottom).mas_offset(5);
        make.leading.mas_equalTo(self.returnMoneyValue.mas_leading);
        make.trailing.mas_equalTo(self.returnMoneyArrowImg.mas_trailing);
    }];
}



- (void)setupCODBankView:(BOOL)canSelect{
    [self.returnMoneyView addSubview:self.warningView];
    [self.warningView addSubview:self.warningImg];
    [self.warningView addSubview:self.warningLabel];
    if(canSelect){
        [self.returnMoneyView addSubview:self.returnMoneyArrowImg];
        [self addSubview:self.returnMoneyBtn];
        [self.returnMoneyArrowImg mas_makeConstraints:^(MASConstraintMaker *make){
            make.centerY.equalTo(self.returnMoneyLabel);
            make.trailing.mas_equalTo(-21);
            make.width.height.mas_equalTo(10);
        }];
        [self.returnMoneyValue mas_updateConstraints:^(MASConstraintMaker *make){
            make.trailing.equalTo(self.returnMoneyArrowImg.mas_leading).mas_offset(-6);
        }];
        
        [self.returnMoneyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.returnMoneyValue.mas_top).mas_offset(-5);
            make.bottom.mas_equalTo(self.returnMoneyValue.mas_bottom).mas_offset(5);
            make.leading.mas_equalTo(self.returnMoneyValue.mas_leading);
            make.trailing.mas_equalTo(self.returnMoneyArrowImg.mas_trailing);
        }];
    }
    
    [self.returnMoneyView addSubview:self.bankNameLabel];
    [self.returnMoneyView addSubview:self.bankNameTapView];
    [self.returnMoneyView addSubview:self.bankNameTextF];
    [self.returnMoneyView addSubview:self.bankNameArrowImg];
    [self.returnMoneyView addSubview:self.bankNameLine];

    [self.returnMoneyView addSubview:self.bankCardNumLabel];
    [self.returnMoneyView addSubview:self.bankCardNumCodeLabel];
    [self.returnMoneyView addSubview:self.bankCardNumTextF];
    [self.returnMoneyView addSubview:self.bankCardNumLine];

    [self.returnMoneyView addSubview:self.bankAccountNameLabel];
    [self.returnMoneyView addSubview:self.bankAccountNameTextF];
    [self.returnMoneyView addSubview:self.bankAccountNameLine];

    [self.returnMoneyView addSubview:self.phoneNumberLabel];
    [self.returnMoneyView addSubview:self.phoneNumberCodeLabel];
    [self.returnMoneyView addSubview:self.phoneNumberTextF];
    [self.returnMoneyView addSubview:self.phoneNumberLine];
    
    CGSize warningLabelSize = [Lang(@"after_sale_apply_warning") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightRegular)] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-93, 500)];
    
    [self.warningView mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.returnMoneyLabel.mas_bottom).offset(20);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(warningLabelSize.height+17);
    }];
    [self.warningImg mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(8);
        make.leading.mas_equalTo(10);
        make.width.height.mas_equalTo(18);
    }];
    [self.warningLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(8);
        make.leading.equalTo(self.warningImg.mas_trailing).offset(5);
        make.trailing.mas_equalTo(-10);
        make.height.mas_equalTo(warningLabelSize.height+1);
    }];
    
    //银行名称
    [self.bankNameLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.warningView.mas_bottom).offset(20);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(17);
    }];
    [self.bankNameTapView mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankNameLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(30);
    }];
    [self.bankNameTextF mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankNameLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(self.bankNameArrowImg.mas_leading).offset(-5);
        make.height.mas_equalTo(20);
    }];
    [self.bankNameArrowImg mas_makeConstraints:^(MASConstraintMaker *make){
        make.centerY.equalTo(self.bankNameTextF);
        make.trailing.mas_equalTo(-21);
        make.width.height.mas_equalTo(10);
    }];
    [self.bankNameLine mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.top.equalTo(self.bankNameTextF.mas_bottom).offset(10);
        make.width.height.mas_equalTo(0.6);
    }];

    
    //银行卡号
    [self.bankCardNumLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankNameLine.mas_bottom).offset(20);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(17);
    }];
    [self.bankCardNumCodeLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankCardNumLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(18);
        make.height.mas_equalTo(20);
        make.width.mas_equalTo(19);
    }];
    [self.bankCardNumTextF mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankCardNumLabel.mas_bottom).offset(12);
        make.leading.equalTo(self.bankCardNumCodeLabel.mas_trailing).offset(5);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(20);
    }];
    [self.bankCardNumLine mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.top.equalTo(self.bankCardNumTextF.mas_bottom).offset(10);
        make.width.height.mas_equalTo(0.6);
    }];
    
    //开户行
    [self.bankAccountNameLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankCardNumLine.mas_bottom).offset(20);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(17);
    }];
    [self.bankAccountNameTextF mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankAccountNameLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(20);
    }];
    [self.bankAccountNameLine mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.top.equalTo(self.bankAccountNameTextF.mas_bottom).offset(10);
        make.width.height.mas_equalTo(0.6);
    }];

    //手机号码
    [self.phoneNumberLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.bankAccountNameLine.mas_bottom).offset(20);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(17);
    }];
    [self.phoneNumberCodeLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.phoneNumberLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(18);
        make.height.mas_equalTo(20);
        make.width.mas_equalTo(45);
    }];
    [self.phoneNumberTextF mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.phoneNumberLabel.mas_bottom).offset(12);
        make.leading.equalTo(self.phoneNumberCodeLabel.mas_trailing).offset(5);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(20);
    }];
    [self.phoneNumberLine mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.top.equalTo(self.phoneNumberTextF.mas_bottom).offset(10);
        make.width.height.mas_equalTo(0.6);
    }];
  
}

- (void)tap{
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithSelectBankName];
//    [self.delegate sendNext:action];
}

- (void)textFieldDidChanged:(NSNotification *)notification {
    
    UITextField *textField = (UITextField *)notification.object;
    if (textField == self.bankAccountNameTextF){
        NSUInteger maxLength = 100;
        if (textField == self.bankAccountNameTextF){
            maxLength = 50;
        }
        
        // 需要限制的长度 邮箱最大64位
        NSString *contentText = textField.text;
        UITextRange *selectedRange = [textField markedTextRange];
        
        NSInteger markedTextLength = [textField offsetFromPosition:selectedRange.start toPosition:selectedRange.end];
        
        if (markedTextLength == 0) {
            
            if (contentText.length > maxLength) {
                
                NSRange rangeRange = [contentText rangeOfComposedCharacterSequencesForRange:NSMakeRange(0, maxLength)];
                textField.text = [contentText substringWithRange:rangeRange];
            }
        }
    }
}

#pragma mark - UITextFieldDelegate
-(void)textFieldDidEndEditing:(UITextField *)textField{
   
    if ([textField isEqual:self.bankCardNumTextF]) {
        self.viewModel.bankCode = [NSString stringWithFormat:@"SA%@",textField.text];

    }else if ([textField isEqual:self.bankAccountNameTextF]) {
        self.viewModel.bankAccountHolder = textField.text;

    }else if ([textField isEqual:self.phoneNumberTextF]) {
        self.viewModel.bankTel = textField.text;
        
        if(self.phoneNumberTextF.text.length == 0){
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage
                                                  message:Lang(@"aftersale_input_phone")];
        }else{
            if([self.phoneNumberTextF.text hasPrefix:@"0"]){
                if(self.phoneNumberTextF.text.length != 10){
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage
                                                          message:Lang(@"aftersale_input_phone_10digit")];
                    
                }
            }else{
                if(self.phoneNumberTextF.text.length != 9){
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage
                                                          message:Lang(@"aftersale_input_phone_9digit")];
                    
                }
            }
        }
    }
    
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithBankInfoEditied];
//    [self.delegate sendNext:action];
    
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [textField resignFirstResponder];
    return YES;
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{
    
    if([string isContainsEmoji]) return NO;
    
    // clicked delete button
    if ([string isEqualToString:@""]) { return YES; }
    
    if ([textField isEqual:self.bankAccountNameTextF]) {
        //检验输入英文、中文、阿语、空格和. 限制50个字符
//        NSString *pattern = @"^[a-zA-Z\\u4e00-\\u9fa5\u0621-\u064A\\s\\.]*$";
        NSString *pattern = @"^[a-zA-Z\\u4e00-\\u9fa5\\u0621-\u064A\\s\\.]{1,50}$";
        NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
        BOOL isMatch = [pred evaluateWithObject:string];
        return isMatch;
    } else if ([textField isEqual:self.bankCardNumTextF]) { // vaildate bank no
        if (string.length > 1) {
            [self handleIbanPaste:textField inputString:string];
            return NO;
        }
        if (![NSString isAlphaNumericString:string] || textField.text.length + string.length > 22) {
            return NO;
        }
    } else if ([textField isEqual:self.phoneNumberTextF]) {
        if (string.length > 1) { // paste usecase for phone number
            [self handlePhonePaste:textField inputString:string];
            return NO;
        }
        if (textField.text.length + string.length <= 10 && [NSString isNumericString:string]) {
            return YES;
        }
        return NO;
    }
    return YES;
}

-(void) handleIbanPaste:(UITextField *) textField inputString:(NSString *)string {
    NSString *reArrangeValidIBan = [string uppercaseString];
    reArrangeValidIBan = [NSString removeSpecialCharcters:reArrangeValidIBan];

    if ([reArrangeValidIBan hasPrefix:@"SA"]) {
        reArrangeValidIBan = [reArrangeValidIBan substringFromIndex:2];
    }
    if (reArrangeValidIBan.length == 22) {
        textField.text = reArrangeValidIBan;
    } else if (reArrangeValidIBan.length + textField.text.length <= 22) {
        textField.text = [NSString stringWithFormat:@"%@%@",textField.text,reArrangeValidIBan];
    }
}

-(void) handlePhonePaste:(UITextField *) textField inputString:(NSString *)string {
    NSString *phoneNumber = [string stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (phoneNumber.length <= 10 && [NSString isNumericString:phoneNumber]) {
        textField.text = phoneNumber;
    }
}

//- (BOOL)deptInput:(NSString *)str{
//    //检验输入中文、英文、阿语、空格和.
//    BOOL isMatch = NO;
//    NSString *match = @"(^[\u4e00-\u9fa5])";//中文
//    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF matches %@", match];
//    isMatch = [predicate evaluateWithObject:str];
//    if (isMatch) {
//        return YES;
//    }
//
//    NSString *match2 = @"[a-zA-Z]*";//英文
//    NSPredicate *predicate2 = [NSPredicate predicateWithFormat:@"SELF matches %@", match2];
//    isMatch = [predicate2 evaluateWithObject:str];
//    if (isMatch) {
//        return YES;
//    }
//
//    if ([str isEqualToString:@" "] || [str isEqualToString:@"."]) {
//        return YES;
//    }
//
//    return NO;
//}

#pragma mark - lazy
- (UIView *)returnMoneyView{
    if (!_returnMoneyView) {
        _returnMoneyView = [[UIView alloc] init];
        _returnMoneyView.backgroundColor =  [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    return _returnMoneyView;
}

- (UILabel *)returnMoneyLabel{
    if (!_returnMoneyLabel) {
        _returnMoneyLabel = [[UILabel alloc] init];
        _returnMoneyLabel.text = Lang(@"after_sale_refund_title");
        _returnMoneyLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        _returnMoneyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _returnMoneyLabel;
}

- (UILabel *)returnMoneyValue{
    if (!_returnMoneyValue) {
        _returnMoneyValue = [[UILabel alloc] init];
        _returnMoneyValue.textAlignment = NSTextAlignmentRight;
        _returnMoneyValue.text = Lang(@"after_sale_refund");
        _returnMoneyValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        _returnMoneyValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _returnMoneyValue;
}
-(UIButton*) returnMoneyBtn{
    if(!_returnMoneyBtn){
        _returnMoneyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_returnMoneyBtn setTitle:@" " forState:UIControlStateNormal];
//        _returnMoneyBtn.backgroundColor = UIColor.redColor;
        [_returnMoneyBtn addTarget:self action:@selector(tapRefundType:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _returnMoneyBtn;
}
- (UIView *)warningView{
    if (!_warningView) {
        _warningView = [[UIView alloc] init];
        _warningView.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"#FFA41C" alpha:0.1];// [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10" alpha:0.1];
        _warningView.layer.cornerRadius = 8;
    }
    return _warningView;
}
- (UIImageView *)warningImg{
    if (!_warningImg) {
        _warningImg = [[UIImageView alloc] init];
        _warningImg.image = JDISVKAAfterSaleModuleImageNamed(@"afterSale_apply_warning");
    }
    return _warningImg;
}
- (UILabel *)warningPointLabel{
    if (!_warningPointLabel) {
        _warningPointLabel = [[UILabel alloc] init];
        _warningPointLabel.text = Lang(@"after_sale_apply_point_warning");
        _warningPointLabel.numberOfLines = 0;
        _warningPointLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _warningPointLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];//#DE6A1C
    }
    return _warningPointLabel;
}

- (UILabel *)warningLabel{
    if (!_warningLabel) {
        _warningLabel = [[UILabel alloc] init];
        _warningLabel.text = Lang(@"after_sale_apply_warning");
        _warningLabel.numberOfLines = 0;
        _warningLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _warningLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];//#DE6A1C
    }
    return _warningLabel;
}
- (UILabel *)bankNameLabel{
    if (!_bankNameLabel) {
        _bankNameLabel = [[UILabel alloc] init];
        _bankNameLabel.text = Lang(@"after_sale_apply_bankName");
        _bankNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _bankNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _bankNameLabel;
}
- (UIView *)bankNameTapView{
    if (!_bankNameTapView) {
        _bankNameTapView = [[UIView alloc] init];
        _bankNameTapView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        UITapGestureRecognizer * tapGes = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tap)];
        [_bankNameTapView addGestureRecognizer:tapGes];
    }
    return _bankNameTapView;
}
- (UITextField *)bankNameTextF{
    if (!_bankNameTextF) {
        _bankNameTextF = [[UITextField alloc] init];
        _bankNameTextF.returnKeyType = UIReturnKeyDone;
        _bankNameTextF.delegate = self;
        _bankNameTextF.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)];
        _bankNameTextF.textAlignment = NSTextAlignmentLeft;
        _bankNameTextF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _bankNameTextF.attributedPlaceholder = [[NSAttributedString alloc]initWithString:Lang(@"after_sale_apply_input_bankName") attributes:@{
            NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)],NSParagraphStyleAttributeName:[NSMutableParagraphStyle new]
        }];
        _bankNameTextF.userInteractionEnabled = NO;
    }
    return _bankNameTextF;
}
- (UIImageView *)bankNameArrowImg{
    if (!_bankNameArrowImg) {
        _bankNameArrowImg = [[UIImageView alloc] init];
        _bankNameArrowImg.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    }
    return _bankNameArrowImg;
}

- (UIImageView *)returnMoneyArrowImg{
    if (!_returnMoneyArrowImg) {
        _returnMoneyArrowImg = [[UIImageView alloc] init];
        _returnMoneyArrowImg.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    }
    return _returnMoneyArrowImg;
}

- (UIView *)bankNameLine{
    if (!_bankNameLine) {
        _bankNameLine = [[UIView alloc] init];
        _bankNameLine.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"E0E0E0"];
    }
    return _bankNameLine;
}

- (UILabel *)bankCardNumLabel{
    if (!_bankCardNumLabel) {
        _bankCardNumLabel = [[UILabel alloc] init];
        _bankCardNumLabel.text = Lang(@"after_sale_apply_bankCard");
        _bankCardNumLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _bankCardNumLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _bankCardNumLabel;
}
- (UILabel *)bankCardNumCodeLabel{
    if (!_bankCardNumCodeLabel) {
        _bankCardNumCodeLabel = [[UILabel alloc] init];
        _bankCardNumCodeLabel.text = @"SA";
        _bankCardNumCodeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightMedium);
        _bankCardNumCodeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _bankCardNumCodeLabel;
}
- (UITextField *)bankCardNumTextF{
    if (!_bankCardNumTextF) {
        _bankCardNumTextF = [[UITextField alloc] init];
        _bankCardNumTextF.keyboardType = UIKeyboardTypeASCIICapable;
        _bankCardNumTextF.delegate = self;
        _bankCardNumTextF.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)];
        _bankCardNumTextF.textAlignment = NSTextAlignmentLeft;

        _bankCardNumTextF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _bankCardNumTextF.attributedPlaceholder = [[NSAttributedString alloc]initWithString:Lang(@"after_sale_apply_input_bankCard") attributes:@{
            NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)],NSParagraphStyleAttributeName:[NSMutableParagraphStyle new]
        }];
    }
    return _bankCardNumTextF;
}
- (UIView *)bankCardNumLine{
    if (!_bankCardNumLine) {
        _bankCardNumLine = [[UIView alloc] init];
        _bankCardNumLine.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"E0E0E0"];
    }
    return _bankCardNumLine;
}

- (UILabel *)bankAccountNameLabel{
    if (!_bankAccountNameLabel) {
        _bankAccountNameLabel = [[UILabel alloc] init];
        _bankAccountNameLabel.text = Lang(@"after_sale_apply_bankAccountName");
        _bankAccountNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _bankAccountNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _bankAccountNameLabel;
}
- (UITextField *)bankAccountNameTextF{
    if (!_bankAccountNameTextF) {
        _bankAccountNameTextF = [[UITextField alloc] init];
        _bankAccountNameTextF.returnKeyType = UIReturnKeyDone;
        _bankAccountNameTextF.delegate = self;
        _bankAccountNameTextF.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)];
        _bankAccountNameTextF.textAlignment = NSTextAlignmentLeft;

        _bankAccountNameTextF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _bankAccountNameTextF.attributedPlaceholder = [[NSAttributedString alloc]initWithString:Lang(@"after_sale_apply_input_bankAccountName") attributes:@{
            NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)],NSParagraphStyleAttributeName:[NSMutableParagraphStyle new]
        }];
    }
    return _bankAccountNameTextF;
}
- (UIView *)bankAccountNameLine{
    if (!_bankAccountNameLine) {
        _bankAccountNameLine = [[UIView alloc] init];
        _bankAccountNameLine.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"E0E0E0"];
    }
    return _bankAccountNameLine;
}

- (UILabel *)phoneNumberLabel{
    if (!_phoneNumberLabel) {
        _phoneNumberLabel = [[UILabel alloc] init];
        _phoneNumberLabel.text = Lang(@"after_sale_apply_phoneNumber");
        _phoneNumberLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _phoneNumberLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _phoneNumberLabel;
}
- (UILabel *)phoneNumberCodeLabel{
    if (!_phoneNumberCodeLabel) {
        _phoneNumberCodeLabel = [[UILabel alloc] init];
        _phoneNumberCodeLabel.text = @"+966";
        _phoneNumberCodeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightMedium);
        _phoneNumberCodeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _phoneNumberCodeLabel;
}
- (UITextField *)phoneNumberTextF{
    if (!_phoneNumberTextF) {
        _phoneNumberTextF = [[UITextField alloc] init];
        _phoneNumberTextF.keyboardType = UIKeyboardTypeNumberPad;
        _phoneNumberTextF.delegate = self;
        _phoneNumberTextF.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)];
        _phoneNumberTextF.textAlignment = NSTextAlignmentLeft;
        _phoneNumberTextF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _phoneNumberTextF.attributedPlaceholder = [[NSAttributedString alloc]initWithString:Lang(@"after_sale_apply_input_phoneNumber") attributes:@{
            NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)],NSParagraphStyleAttributeName:[NSMutableParagraphStyle new]
        }];
    }
    return _phoneNumberTextF;
}
- (UIView *)phoneNumberLine{
    if (!_phoneNumberLine) {
        _phoneNumberLine = [[UIView alloc] init];
        _phoneNumberLine.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"E0E0E0"];
    }
    return _phoneNumberLine;
}

@end
