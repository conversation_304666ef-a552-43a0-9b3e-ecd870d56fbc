//
//  JDISVKAApplyDetailReturnReceiveCell.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailReturnReceiveCell.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import "JDISVKAApplyDetailReturnReceiveViewModel.h"
#import "JDISVKAAfterSaleAction.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

#import <JDISVCategoryModule/UIView+JDCDGesture.h>
#import <JDISVReactiveObjCModule/RACSubject.h>
@interface JDISVKAApplyDetailReturnReceiveCell()

@property (nonatomic,strong)NSString *pageType;

@property (nonatomic,strong)UIView *returnTypeView;
@property (nonatomic,strong)UILabel *returnModeLabel;
//@property (nonatomic,strong)UITextField *returnModeValue;
//@property (nonatomic,strong)UIView *returnModeValueTapView;
//@property (nonatomic,strong)UIImageView *arrowImg;
//@property (nonatomic,strong)UIView *lineView;




@property (nonatomic,strong)UIView *returnAddressView;//上门取件地址

@property (nonatomic,strong)UILabel *receiverName;

@property (nonatomic,strong)UILabel *receiverPhone;

@property (nonatomic,strong)UILabel *receiverAddress;

@property (nonatomic,strong)UIImageView *moreImageView;

@property (nonatomic,strong)JDISVKAApplyDetailReturnReceiveViewModel *viewModel;

@end

@implementation JDISVKAApplyDetailReturnReceiveCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self setupCell];
}

- (void)render{
//    self.returnModeValue.text = self.viewModel.returnTypeValue;
//    if (self.viewModel.isShowAddress) {//展示地址2种情况(1.换新+客户自寄 2.上门取件)
        self.receiverAddress.text = self.viewModel.receiverAddress;
        self.receiverName.text = self.viewModel.receiverName?: @"-";
        if(self.viewModel.receiverPhone.length>0){
//            self.receiverPhone.text = self.viewModel.receiverPhone;
            self.receiverPhone.text = [NSString stringWithFormat:@"+966 %@",self.viewModel.receiverPhone];
        }else{
            self.receiverPhone.text = @"-";
        }
        [self addAddressView];
        
//    }else{
//        for (UIView * subView in self.returnAddressView.subviews) {
//            [subView removeFromSuperview];
//        }
//        [self.returnAddressView removeFromSuperview];
//    }
    
    //包含上门取件才显示箭头
//    BOOL isShowArrow = NO;
//    for (applyDetailGeneralTypeVo *returnModel in self.viewModel.returnModelList) {
//        if(returnModel.type == 72){//上门取件
//            isShowArrow = YES;
//            break;
//        }
//    }
//    if(!isShowArrow){
//        self.arrowImg.hidden = NO;
//        self.returnModeValueTapView.hidden = NO;
//    }else{
//        self.arrowImg.hidden = YES;
//        self.returnModeValueTapView.hidden = YES;
//    }
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
}

- (void)clickAddressView{
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithReplaceNewClickAddress];
//    action.value = @"cliclReturnReceiveCell";
//    [self.delegate sendNext:action];
}

- (void)setupCell{
    [self.returnAddressView jd_addTapAction:@selector(clickAddressView) withTarget:self];
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    [self.contentView addSubview:self.returnTypeView];
//    [self.returnTypeView addSubview:self.returnModeValueTapView];
    [self.returnTypeView addSubview:self.returnModeLabel];
//    [self.returnTypeView addSubview:self.returnModeValue];
//    [self.returnTypeView addSubview:self.arrowImg];
//    [self.returnTypeView addSubview:self.lineView];
    
    [self.returnTypeView mas_makeConstraints:^(MASConstraintMaker *make){
        make.bottom.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]);
        make.top.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"]);
        make.leading.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
        make.trailing.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
    }];
    [self.returnModeLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self.returnTypeView).mas_equalTo(18);
        make.leading.mas_equalTo(self.returnTypeView).offset(18);
        make.height.mas_offset(17);
    }];
//    [self.returnModeValue mas_makeConstraints:^(MASConstraintMaker *make){
//        make.leading.mas_equalTo(18);
//        make.top.mas_equalTo(self.returnModeLabel.mas_bottom).mas_equalTo(12);
////        make.trailing.mas_equalTo(self.returnTypeView).offset(-30-18);
//        make.height.mas_offset(20);
//    }];
//    [self.arrowImg mas_makeConstraints:^(MASConstraintMaker *make){
//        make.centerY.mas_equalTo(self.returnModeValue);
//        make.trailing.mas_equalTo(self.returnTypeView).offset(-21);
//        make.size.mas_equalTo(CGSizeMake(10, 10));
//    }];
//    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make){
//        make.top.mas_equalTo(self.returnModeValue.mas_bottom).mas_equalTo(12);
//        make.leading.mas_equalTo(18);
//        make.trailing.mas_equalTo(-18);
//        make.height.mas_offset(0.6);
//    }];
//    [self.returnModeValueTapView mas_makeConstraints:^(MASConstraintMaker *make){
//        make.top.mas_equalTo(self.returnModeLabel.mas_bottom).mas_equalTo(12);
//        make.leading.mas_equalTo(18);
//        make.trailing.mas_equalTo(-18);
//        make.height.mas_offset(32);
//    }];
    
    self.returnTypeView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
}

- (void)addAddressView{
    [self.returnTypeView addSubview:self.returnAddressView];
    [self.returnAddressView addSubview:self.receiverName];
    [self.returnAddressView addSubview:self.receiverPhone];
    [self.returnAddressView addSubview:self.receiverAddress];
    [self.returnAddressView addSubview:self.moreImageView];
    [self.returnAddressView mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self.returnModeLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(self.returnTypeView).offset(18);
        make.trailing.mas_equalTo(self.returnTypeView).offset(-18);
        make.bottom.mas_equalTo(self.returnTypeView).offset(-18);
    }];
    
    [self.receiverName mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(12);
        make.leading.mas_equalTo(12);
        make.height.mas_equalTo(21);
        make.trailing.mas_equalTo(self.returnAddressView).offset(-32);

    }];
    [self.receiverPhone mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self.receiverName.mas_bottom).offset(6);
        make.leading.mas_equalTo(12);
        make.height.mas_equalTo(15);
        make.trailing.mas_equalTo(self.returnAddressView).offset(-32);

    }];
    [self.receiverAddress mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self.receiverPhone.mas_bottom).offset(6);
        make.leading.mas_equalTo(self.receiverName.mas_leading);
        make.trailing.mas_equalTo(self.returnAddressView).offset(-32);

    }];
    [self.moreImageView mas_makeConstraints:^(MASConstraintMaker *make){
        make.centerY.mas_equalTo(self.returnAddressView);
        make.trailing.mas_equalTo(self.returnAddressView).offset(-15);
        make.size.mas_equalTo(CGSizeMake(10, 10));
    }];
}

#pragma mark - lazy
- (UIView *)returnTypeView{
    if (!_returnTypeView) {
        _returnTypeView = [[UIView alloc] init];
        _returnTypeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    return _returnTypeView;
}

- (UILabel *)returnModeLabel{
    if (!_returnModeLabel) {
        _returnModeLabel = [[UILabel alloc] init];
        _returnModeLabel.text = Lang(@"after_sale_detail_return_address_receive");
        _returnModeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _returnModeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _returnModeLabel;
}

//- (UIView *)returnModeValueTapView{
//    if (!_returnModeValueTapView) {
//        _returnModeValueTapView = [[UIView alloc] init];
//        _returnModeValueTapView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
//        UITapGestureRecognizer * tapGes = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tap)];
//        [_returnModeValueTapView addGestureRecognizer:tapGes];
//    }
//    return _returnModeValueTapView;
//}

- (void)tap{
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithSelectReturnModel];
//    action.value = self.viewModel.returnModelList;
//    [self.delegate sendNext:action];
}

//- (UITextField *)returnModeValue{
//    if (!_returnModeValue) {
//        _returnModeValue = [[UITextField alloc] init];
//        _returnModeValue.returnKeyType = UIReturnKeyDone;
//        _returnModeValue.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightMedium)];
//        _returnModeValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
//        _returnModeValue.textAlignment = NSTextAlignmentLeft;
//
//        _returnModeValue.attributedPlaceholder = [[NSAttributedString alloc]initWithString:Lang(@"after_sale_reason_select") attributes:@{
//            NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
//            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)],
//            NSParagraphStyleAttributeName:[NSMutableParagraphStyle new],
//        }];
//        _returnModeValue.userInteractionEnabled = NO;
//    }
//    return _returnModeValue;
//}

//- (UIImageView *)arrowImg{
//    if (!_arrowImg) {
//        _arrowImg = [[UIImageView alloc] init];
//        _arrowImg.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
//    }
//    return _arrowImg;
//}
//- (UIView *)lineView{
//    if (!_lineView) {
//        _lineView = [[UIView alloc] init];
//        _lineView.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"E0E0E0"];
//    }
//    return _lineView;
//}


- (UIView *)returnAddressView{
    if (!_returnAddressView) {
        _returnAddressView = [[UIView alloc] init];
        _returnAddressView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C17"];
        _returnAddressView.layer.cornerRadius = 8;

    }
    return _returnAddressView;
}

- (UILabel *)receiverName{
    if (!_receiverName) {
        _receiverName = [[UILabel alloc] init];
        _receiverName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T6",UIFontWeightSemibold);
        _receiverName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _receiverName;
}

- (UILabel *)receiverPhone{
    if (!_receiverPhone) {
        _receiverPhone = [[UILabel alloc] init];
        _receiverPhone.jdisv_fontPicker = JDISVJDFontPickerWithKey(@"#T9");
        _receiverPhone.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _receiverPhone;
}

- (UILabel *)receiverAddress{
    if (!_receiverAddress) {
        _receiverAddress = [[UILabel alloc] init];
        _receiverAddress.numberOfLines = 0;
        _receiverAddress.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _receiverAddress.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _receiverAddress;
}

- (UIImageView *)moreImageView{
    if (!_moreImageView) {
        _moreImageView = [[UIImageView alloc] init];
        _moreImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    }
    return _moreImageView;
}

@end
