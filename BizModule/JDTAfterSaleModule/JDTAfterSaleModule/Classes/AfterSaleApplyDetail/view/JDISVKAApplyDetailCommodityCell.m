//
//  JDISVKAApplyDetailCommodityCell.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailCommodityCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVKAUIKitModule/KAStepper.h>
#import "JDISVKAApplyDetailCommodityViewModel.h"
#import <JDISVMasonryModule/Masonry.h>
#import "JDISVKAAfterSaleAction.h"

#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVReactiveObjCModule/RACSubject.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVKAApplyDetailCommodityGiftView.h"


@interface JDISVKAApplyDetailCommodityCell()<KAStepperDelegate>

@property (weak, nonatomic) IBOutlet UIView *mainView;

@property (weak, nonatomic) IBOutlet UIImageView *shopImageView;

@property (weak, nonatomic) IBOutlet UILabel *shopNameLabel;

@property (weak, nonatomic) IBOutlet UIImageView *commodityImageView;

@property (weak, nonatomic) IBOutlet UILabel *commodityNameLabel;

@property (weak, nonatomic) IBOutlet UILabel *priceLabel;

@property (weak, nonatomic) IBOutlet UILabel *priceValue;

@property (weak, nonatomic) IBOutlet UILabel *countLabel;

@property (weak, nonatomic) IBOutlet UILabel *countValue;

@property (weak, nonatomic) IBOutlet UILabel *applyCountLabel;

@property (nonatomic,strong)JDISVKAApplyDetailCommodityViewModel *viewModel;

@property (nonatomic,strong)KAStepper *stepper;

@property (nonatomic,strong)JDISVKAApplyDetailCommodityGiftView *giftView;

#pragma mark 约束

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewRightConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewBottomConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewTopConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewLeftConstraint;

@end

@implementation JDISVKAApplyDetailCommodityCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self setupCell];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
}

- (void)render{
    self.shopNameLabel.text = self.viewModel.shopName;
    self.commodityNameLabel.text = self.viewModel.skuName;
    self.countValue.text = [NSString stringWithFormat:@"%ld",self.viewModel.skuCount];
//    self.priceValue.text = [NSString stringWithFormat:@"%@%@",PRICETAG,self.viewModel.skuSingleShouldPrice];
    NSMutableAttributedString *priceStr = [[NSMutableAttributedString alloc] init];
    [priceStr KA_renderWithPrice:self.viewModel.skuSingleShouldPrice.floatValue type:KAPriceTypeP4 colorType:@"#C7"];
    self.priceValue.attributedText = priceStr;
    self.priceValue.textAlignment = NSTextAlignmentCenter;
    
    self.stepper.maxLimit = self.viewModel.skuApplyCount;
    [self.stepper reloadWithCount:self.viewModel.userSelectCount>1?self.viewModel.userSelectCount:1];
    
    NSString* str = [PlatformService getCompleteImageUrl:self.viewModel.skuImage];

    UIImage* placeHolder = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
    
    
    [self.commodityImageView jdcd_setImage:str placeHolder:placeHolder contentMode:UIViewContentModeScaleAspectFit];
    self.giftView.arr = self.viewModel.gitfArr;
}

- (void)setupCell{
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    self.mainView.layer.cornerRadius = 12;
    
    
    self.shopNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightMedium);
    self.shopNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.commodityNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.priceLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.priceLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.priceLabel.text = Lang(@"after_sale_price");
    self.priceValue.jdisv_fontPicker = JDISVJDFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.priceValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.countLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.countLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.countValue.jdisv_fontPicker = JDISVJDFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.countValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.countLabel.text = Lang(@"after_sale_count");
    
    self.applyCountLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyCountLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.shopImageView.image = [UIImage ka_iconWithName:JDIF_ICON_SHOP_LINE imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    self.applyCountLabel.text = Lang(@"after_sale_exchange_num");
    
    self.stepper = [[KAStepper alloc] init];
    [self.stepper showBorder];
    self.stepper.cornerRedius = 0;
    self.stepper.imageInsets = UIEdgeInsetsMake(9, 9, 9, 9);
    self.stepper.delegate = self;
    [self.mainView addSubview:self.stepper];
    
    [self.stepper mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(self.mainView).offset(-18);
        make.centerY.mas_equalTo(self.applyCountLabel);
        make.width.mas_equalTo(30+30+72);
        make.height.mas_equalTo(30);
    }];
    
    self.giftView = [[JDISVKAApplyDetailCommodityGiftView alloc]init];
    [self.mainView addSubview:self.giftView];
    [self.giftView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.mainView).offset(18);
        make.trailing.mas_equalTo(self.mainView).offset(-18);
        make.top.mas_equalTo(self.applyCountLabel.mas_bottom);
    }];
    
    self.mainViewLeftConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.mainViewTopConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    self.mainViewBottomConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.mainViewRightConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    
    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.commodityImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.commodityImageView.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"] colorWithAlphaComponent:0.02];
}

#pragma mark - KAStepperDelegate
- (void)stepper:(KAStepper *)stepper changeOfCount:(NSInteger)count {
    self.viewModel.userSelectCount = count;
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithStepperClicked];
//    [self.delegate sendNext:action];
}

- (void)stepper:(KAStepper *)stepper touchOffMaxLimit:(NSInteger)maxLimit {
//    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithStepperReachMax];
//    [self.delegate sendNext:action];
}



@end
