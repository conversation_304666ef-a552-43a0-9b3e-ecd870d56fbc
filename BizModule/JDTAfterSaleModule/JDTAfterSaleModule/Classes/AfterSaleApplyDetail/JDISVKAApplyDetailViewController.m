//
//  JDISVKAApplyDetailViewController.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailViewController.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKAApplyDetailCommodityCell.h"
#import "JDISVKAApplyDetailCauseCell.h"
#import "JDISVKAApplyDetailReturnCell.h"
#import "JDISVKAApplyDetailReturnReceiveCell.h"
#import "JDISVKAApplyDetailMainViewModel.h"
#import "JDISVKAApplyDetailBaseViewModel.h"
#import "JDISVKAApplyDetailReturnReasonCell.h"
#import "JDISVKAAfterSaleUtils.h"
#import "JDISVKAApplyDetailCommodityViewModel.h"
#import "JDISVKAApplyDetailReturnReasonViewModel.h"
#import "JDISVKAApplyDetailReturnMoneyCell.h"

#import <JDISVCategoryModule/NSObject+JDCDExtend.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>
#import "JDISVKAApplyDetailReturnMoneyCell.h"

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDBRouterModule/JDRouter.h>
#import "JDISVKAAfterSaleAction.h"
#import "JDISVAfterTracker.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVKAApplyDetailReasonSelectViewController.h"
#import "JDISVKAApplyDetailBankSelectVC.h"
#import "JDISVKAApplyReturnModeSelectVC.h"
#import "JDISVKAApplyDetailSubmitModel.h"

#import "JDISVKAApplyDetailCauseViewModel.h"
#import "NSString+JDISVAfterExtension.h"

#import "JDISVKAApplyDetailReturnMoneyViewModel.h"

@interface JDISVKAApplyDetailViewController ()<UITableViewDelegate,UITableViewDataSource>


@property(nonatomic,assign)CGFloat delta;

@property(nonatomic,strong)UITableView *tableView;

@property (nonatomic,assign)BOOL userIsHandleView;

@property (nonatomic,strong)UIView *bottomView;

@property(nonatomic,strong)JDISVKAApplyDetailMainViewModel *viewModel;

@property (nonatomic,assign)BOOL IQKeyBoardEnable;

@property (nonatomic,assign)BOOL IQKeyBoardShouldResignOnTouchOutside;

@property (nonatomic,strong)KAEmptyView *errorView;

@property(nonatomic,strong)NSNumber *serverTimeStamp;
@property(nonatomic,strong)NSArray *bankInfoArr;

@property (nonatomic,assign)NSInteger serviceType;

@end

@implementation JDISVKAApplyDetailViewController

+ (instancetype)initWithData:(NSDictionary *)data{
    JDISVKAApplyDetailViewController *vc = [[JDISVKAApplyDetailViewController alloc] init];
    vc.viewModel.requestParameters = data;
    vc.serviceType = [[data objectForKey:@"serviceType"] integerValue];
    return vc;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self.navigationController.navigationBar setHidden:YES];
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    JDWeakSelf
    navigationBar
    .decorator
    .addBackground(^(KANavigationBarBackgrounItem * _Nonnull item) {
        item.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    })
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        JDStrongSelf
        if (self.userIsHandleView) {
            [JDCDAlert alert].config
            .renderW3(Lang(@"after_sale_commit_return_hint"))
            .ka_addAction(Lang(@"after_sale_cancel"), ^(UIButton * _Nonnull button) {
                //按钮样式有客户端自己确定
                [button renderB5];
            }, ^{
                ISVLog(Lang(@"back"));
            })
            .ka_addAction(Lang(@"after_sale_reason_confirm"), ^(UIButton * _Nonnull button) {
                //按钮样式有客户端自己确定
                
                [button renderB1WithCornerRadius:15];
            }, ^{
                [self.navigationController popViewControllerAnimated:YES];
            })
            .jdcd_show();
        }else{
            [self.navigationController popViewControllerAnimated:YES];
        }
    })
    .title(self.pageType, NSTextAlignmentCenter) //标题
    .render();
    
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    self.viewModel.pageType = self.pageType;
    self.viewModel.serviceType = self.serviceType;
    
    [self reloadData];
    
    {
        NSNumber* orderId = self.viewModel.requestParameters[@"orderId"];
        NSNumber* skuId = self.viewModel.requestParameters[@"skuId"];
        
        [JDISVAfterTracker PV:@"Repair_SerivceDetail"
                        param:@{@"page_id":@"Repair_SerivceDetail",
                                @"service_orderid":@"",
                                @"orderid":orderId,
                                @"skuId":skuId
                              }];
        
    }
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardAction:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardAction:) name:UIKeyboardWillHideNotification object:nil];
    
    [self requestReason];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    [[IQKeyboardManager sharedManager] setShouldResignOnTouchOutside:YES];
    [[IQKeyboardManager sharedManager] setEnable:NO];
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
}

-(void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    [[IQKeyboardManager sharedManager] setShouldResignOnTouchOutside:self.IQKeyBoardShouldResignOnTouchOutside];
    [[IQKeyboardManager sharedManager] setEnable:self.IQKeyBoardEnable];
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
}

- (CGFloat )firstResponderCellMaxY {
    
    __block CGFloat cellMaxY;
    [self.tableView.visibleCells enumerateObjectsUsingBlock:^(__kindof UITableViewCell * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        //焦点所在的textField
        if([obj isKindOfClass:JDISVKAApplyDetailReturnMoneyCell.class]){
            JDISVKAApplyDetailReturnMoneyCell * moneyCell = (JDISVKAApplyDetailReturnMoneyCell *)obj;
            if ([moneyCell.bankCardNumTextF isFirstResponder]) {

                cellMaxY = moneyCell.mj_y + moneyCell.bankCardNumLine.mj_y  - self.tableView.contentOffset.y;

            }else if([moneyCell.bankAccountNameTextF isFirstResponder]){
                
                cellMaxY = moneyCell.mj_y + moneyCell.bankAccountNameLine.mj_y  - self.tableView.contentOffset.y;

            }else if([moneyCell.phoneNumberTextF isFirstResponder]){

                cellMaxY = moneyCell.mj_y + moneyCell.phoneNumberLine.mj_y  - self.tableView.contentOffset.y;

            }
        }
    }];
    return cellMaxY;
}

// 键盘监听事件
- (void)keyboardAction:(NSNotification*)sender{
    NSDictionary *useInfo = [sender userInfo];
//    NSValue *value = [useInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardFrame = [[useInfo objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];

    CGFloat keyboardY = keyboardFrame.origin.y;
//    CGFloat tableViewViewHeight = self.tableView.frame.size.height;
    if ([sender.name isEqualToString:UIKeyboardWillShowNotification]) {
        //键盘弹出时
        CGFloat CellTFBottom = [self firstResponderCellMaxY]+[UIWindow ka_uikit_navigationHeight];
        if (CellTFBottom >0 && CellTFBottom > keyboardY) {
            //记录delta值，键盘收起恢复原来位置时使用
            self.delta = CellTFBottom-keyboardY;
            self.tableView.contentOffset = CGPointMake(0, self.tableView.contentOffset.y+self.delta);

        }
        
    } else{
        //键盘收起时
        self.tableView.contentOffset = CGPointMake(0, self.tableView.contentOffset.y-self.delta);
        self.delta = 0;
    }
    
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self.view endEditing:YES];
}

- (void)reloadData{
    [PlatformService showLoadingInView:self.view];
    if (self.errorView) {
        [self.errorView removeFromSuperview];
    }
    [[self.viewModel requestData] subscribeError:^(NSError * _Nullable error) {
        [PlatformService dismissInView:self.view];
        self.errorView = [[KAEmptyView alloc] initWithFrame:CGRectMake(0, [UIWindow ka_uikit_navigationHeight], self.view.frame.size.width, self.view.frame.size.height) type:KAEmptyViewTypeNormal];
        self.errorView.decrible = Lang(@"after_sale_load_error");
        self.errorView.coverImage = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypeFailToLoad];
        JDWeakSelf
        [self.errorView addActionButtonWithTitle:Lang(@"after_sale_retry") render:^(UIButton * _Nonnull sender) {
            [sender renderB3];
        } action:^(UIButton * _Nonnull sender) {
            JDStrongSelf
            [self reloadData];
        }];
        [self.view addSubview:self.errorView];
    } completed:^{
        [PlatformService dismissInView:self.view];
        [self.view addSubview:self.tableView];
        [self configBottomView];
    }];
}

- (void)requestGiftInfo{
    [PlatformService showLoadingInView:self.view];
    
    [[self.viewModel requestGiftInfo] subscribeError:^(NSError * _Nullable error) {
        [PlatformService dismissInView:self.view];
        
    } completed:^{
        [PlatformService dismissInView:self.view];
        [self.tableView reloadData];
    }];
}



- (void)requestBankInfo:(void (^)(void))nextBlock{
    if(self.bankInfoArr.count>0){
        nextBlock();
    }else{
        [PlatformService showLoadingInView:self.view];
        [[self.viewModel requestBankInfo] subscribeNext:^(NSArray *arr) {
            [PlatformService dismissInView:self.view];

            self.bankInfoArr = arr;
            nextBlock();
            
        }error:^(NSError * _Nullable error) {
            [PlatformService dismissInView:self.view];

            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:error.localizedDescription];
        }];
    }
}

- (void)configBottomView{
    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.mas_equalTo(self.view);
        make.height.mas_equalTo(self.viewModel.safeBottom + 50);
    }];
    
    UIButton *submitButton = [[UIButton alloc] init];
    [submitButton setTitle:Lang(@"after_sale_commit") forState:UIControlStateNormal];
    submitButton.enabled = YES;
    [self.view addSubview:submitButton];
    [submitButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.bottomView).offset(5);
        make.leading.mas_equalTo(self.bottomView).offset(18);
        make.trailing.mas_equalTo(self.bottomView).offset(-18);
        make.height.mas_equalTo(40);
    }];
    [submitButton layoutIfNeeded];
    [submitButton renderB2];
    [submitButton addTarget:self action:@selector(clickDoneButton) forControlEvents:UIControlEventTouchUpInside];
}

- (void)eventWithActionType:(JDISVKAAfterSaleAction *)action{
//    if(action.actionType == JDISVKAAfterSaleActionTypeWithSelectReason){
//        self.userIsHandleView = YES;
//        [self selectReason];
//    }
//    else if(action.actionType == JDISVKAAfterSaleActionTypeWithSelectBankName){
//        self.userIsHandleView = YES;
//        [self requestBankInfo:^{
//            [self selectBankName];
//        }];
//        
//    }else if(action.actionType == JDISVKAAfterSaleActionTypeWithSelectReturnModel){
//        self.userIsHandleView = YES;
//        [self getCurrentServerTime:^{
//            [self selectReturnModel:action.value];
//        }];
//
//    }else if (action.actionType == JDISVKAAfterSaleActionTypeWithStepperReachMax) {
//        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:Lang(@"after_sale_count_warn_message")];
//    }else if (action.actionType == JDISVKAAfterSaleActionTypeWithApplyCauseEditied) {
//        self.userIsHandleView = YES;
//        
//    }else if (action.actionType == JDISVKAAfterSaleActionTypeWithBankInfoEditied) {
//        self.userIsHandleView = YES;
//        
//    }else if (action.actionType == JDISVKAAfterSaleActionTypeWithStepperClicked){
//        self.userIsHandleView = YES;
//        //请求赠品数据 (买赠的主品需要调用)
//        if(self.viewModel.isRequestGiftInfo){
//            [self requestGiftInfo];
//        }
//        
//    }else if(action.actionType == JDISVKAAfterSaleActionTypeWithRefund){
//        [self selectRefund];
//    }else if (action.actionType == JDISVKAAfterSaleActionTypeWithReplaceNewClickAddress) {
//
//        self.userIsHandleView = YES;
//        NSDictionary* param =@{@"canSelect":@1,@"addressId":self.viewModel.addressId};
//        JDWeakSelf
//        NSString *addressListViewControllerURL = [NSString stringWithFormat:@"router://%@/addressListViewController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
//        [JDRouter openURL:addressListViewControllerURL arg:param error:nil completion:^(NSDictionary *object) {
//            UIViewController *vc = [object objectForKey:@"viewController"];
//            if (vc) {
//                [self.navigationController pushViewController:vc animated:YES];
//            }
//            id addr = [object objectForKey:@"info"];
//            if (addr) {
//                JDStrongSelf
//                if ([action.value isEqualToString:@"cliclReturnReceiveCell"]) {
//                    [self.viewModel updateReceiveAddressCell:addr];
//                }else{
//                    [self.viewModel updateAddressCell:addr];
//                }
//                [self.tableView reloadData];
//            }
//            
//        }];
//    }
}

- (void)clickDoneButton{
    
    NSString* rId = [self.viewModel reasonId];
    if(rId == nil || rId.length == 0){
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_commit_reason_select_error")];
    }
    else if (self.viewModel.userApplyCauseString.length <= 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_commit_reason_empty_error")];
    }else if (self.viewModel.userApplyCauseString.length < 10){
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_commit_reason_range_error")];
    }else{

        if(self.viewModel.refundType == 30){
            if (self.viewModel.bankName.length == 0) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_input_bankName")];
                return;
            }
            
            //银行卡号
            if (self.viewModel.bankCode.length == 0) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_input_bankCard")];
                return;
            }
            
            if (self.viewModel.bankCode.length != 24) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_bankCard_illegal_length")];
                return;
            }
            
            //开户行
            if (self.viewModel.bankAccountHolder.length == 0) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_input_bankAccountName")];
                return;
            }
            if (self.viewModel.bankAccountHolder.length > 50) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_bankAccountName_illegal")];
                return;
            }
            
            //手机号
            if (self.viewModel.bankTel.length == 0) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_input_phoneNumber")];
                return;
            }
            NSRange phoneRange = [self.viewModel.bankTel rangeOfCharacterFromSet:[[NSCharacterSet characterSetWithCharactersInString:@"**********"] invertedSet]];
            BOOL invalidatePhone = phoneRange.length > 0;
            if([self.viewModel.bankTel hasPrefix:@"0"]){
                if (self.viewModel.bankTel.length != 9 || invalidatePhone) {
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_phoneNumber_illegal_10")];
                    return;
                }
            }else{
                
                if (self.viewModel.bankTel.length != 9 || invalidatePhone) {
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_phoneNumber_illegal")];
                    return;
                }
            }
        }
        
        //如果退货方式有多种(自寄/上门取件) 需要选择一个
        if(self.viewModel.returnViewModel.returnModelList.count>1 &&
           ![self.viewModel.returnViewModel.returnTypeValue jdcd_validateString]){
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_select_returnMethod")];
            return;
        }
        
//        if(self.viewModel.submitModel.returnModel.integerValue == 72 && self.viewModel.submitModel.reserveDateBegin.integerValue == 0 && !self.viewModel.orderSelf){
//            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"after_sale_apply_pickUp_SelectTime")];
//            return;
//
//        }
        
        if (nil == self.viewModel.addressId || 1 > [self.viewModel.addressId integerValue]) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:Lang(@"aftersale_return_toast_missing_delivery_address")];
            return;
        }
        
        if([self.viewModel.bankTel hasPrefix:@"0"]){
            self.viewModel.bankTel = [self.viewModel.bankTel substringFromIndex:1];
        }
        
        // If the reason for after-sales service is 1004 or 2004, the image field must be transmitted
        NSString *reasonId = [self.viewModel reasonId];
        if ([reasonId isEqualToString:@"1004"] || [reasonId isEqualToString:@"2004"] ) {
            JDISVKAApplyDetailCauseViewModel *causeVM = [self.viewModel causeViewModel];
            if (nil == causeVM || nil == causeVM.photos || 1 > causeVM.photos.count) {
                NSString *errorMsg = Lang(@"after_sale_apply_upload_photo_tips");
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:errorMsg];
                return;
            }
        }
        
        
        [self submitForm];
        
    }
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    JDISVKAApplyDetailBaseViewModel *baseViewModel = self.viewModel.viewModelArray[indexPath.row];
    JDISVKAApplyDetailBaseCell *baseCell = [tableView dequeueReusableCellWithIdentifier:[baseViewModel identifier]];
    if([[baseViewModel identifier] isEqualToString:@"JDISVKAApplyDetailReturnMoneyCell"]){
        baseCell = [[JDISVKAApplyDetailReturnMoneyCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"JDISVKAApplyDetailReturnMoneyCell"];
    }
    baseCell.selectionStyle = UITableViewCellSelectionStyleNone;
    [baseCell config:baseViewModel];
    if ([baseViewModel isKindOfClass:[JDISVKAApplyDetailCauseViewModel class]]) {
        JDISVKAApplyDetailCauseViewModel *causeVM = (JDISVKAApplyDetailCauseViewModel *)baseViewModel;
        causeVM.tableView = tableView;
        causeVM.viewController = self;
    }
    JDWeakSelf
    [[baseCell.delegate takeUntil:baseCell.rac_prepareForReuseSignal] subscribeNext:^(JDISVKAAfterSaleAction *action) {
        JDStrongSelf
        [self eventWithActionType:action];
    }];
    return baseCell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.viewModelArray.count;
}


- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVKAApplyDetailBaseViewModel *baseViewModel = self.viewModel.viewModelArray[indexPath.row];
    return baseViewModel.cellHeight;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVKAApplyDetailBaseCell *baseCell = (JDISVKAApplyDetailBaseCell *)cell;
    [baseCell render];
}

-(void)selectReason{
    @weakify(self);
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel requestReason] subscribeNext:^(NSArray*  _Nullable x) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        JDISVKAApplyDetailReasonSelectViewController* selectReason = [[JDISVKAApplyDetailReasonSelectViewController alloc] init];
        
        KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:selectReason presentingViewController:self];
        presentationVC.type = KAFloatLayerTypeCustom;
        selectReason.transitioningDelegate = presentationVC;
        
        
        selectReason.selectedReasonId = [self.viewModel reasonId];
        selectReason.datas = x;
        selectReason.sureButtonCallBack = ^(NSString * _Nonnull reasonId, NSString * _Nonnull reasonText) {
            @strongify(self)
            [self.viewModel setReason:reasonId reasonText:reasonText];
            [self.tableView reloadData];
        };
        
        presentationVC.presentedView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
        [self presentViewController:selectReason animated:YES completion:nil];
        } error:^(NSError * _Nullable error) {
            [PlatformService dismissInView:self.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:error.localizedDescription];
        }];
    
}

- (void)requestReason {
    // 货损售后，需要查询出问题原因文字，显示，隐藏选择问题原因的UI
    if (30 != self.serviceType) { return; }
    
    @weakify(self);
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel requestReason] subscribeNext:^(NSArray *reasonList) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        
        for (JDISVKAPllyDetailReasonModel *resonModel in reasonList) {
            if ([@"1004" isEqualToString:resonModel.reasonId]) {
                [self.viewModel setReason:resonModel.reasonId reasonText:resonModel.reasonText];
                [self.viewModel setUnselectReason:YES];
                [self.tableView reloadData];
                return;
            }
        }
    } error:^(NSError * _Nullable error) {
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:error.localizedDescription];
    }];
}

-(void)selectRefund{
    @weakify(self);
    JDISVKAApplyDetailReasonSelectViewController* selectReason = [[JDISVKAApplyDetailReasonSelectViewController alloc] init];
    selectReason.isRefond = YES;
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:selectReason presentingViewController:self];
    presentationVC.type = KAFloatLayerTypeCustom;
    selectReason.transitioningDelegate = presentationVC;
    
    
    selectReason.selectedReasonId = @"Point Refund";
    
    JDISVKAApplyDetailReturnMoneyViewModel* returnMoney;
    for (JDISVKAApplyDetailReturnMoneyViewModel* model in self.viewModel.viewModelArray){
        if([model isKindOfClass:JDISVKAApplyDetailReturnMoneyViewModel.class]){
            returnMoney = model;
        }
    }
    JDISVKAPllyDetailReasonModel* model1 = [[JDISVKAPllyDetailReasonModel alloc] init];
    model1.reasonId =@"40";
    model1.reasonText = returnMoney.refundPointinfo;
    
    JDISVKAPllyDetailReasonModel* model2 = [[JDISVKAPllyDetailReasonModel alloc] init];
    model2.reasonId =@"30";
    model2.reasonText = returnMoney.returnMoneyValue;
    
    
    if(returnMoney.returnBank){
        model2.selected = YES;
    }else{
        model1.selected = YES;
    }
    selectReason.datas = @[model1,model2];
    selectReason.sureButtonCallBack = ^(NSString * _Nonnull reasonId, NSString * _Nonnull reasonText) {
        @strongify(self)
        self.viewModel.submitModel.refundType = @(reasonId.intValue);
        self.viewModel.refundType = reasonId.intValue;
        [self.viewModel setRefund:reasonId];
        [self.tableView reloadData];
    };
    
    presentationVC.presentedView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [self presentViewController:selectReason animated:YES completion:nil];
    
}

-(void)selectBankName{
    @weakify(self);
    JDISVKAApplyDetailBankSelectVC* selectBankNameVC = [[JDISVKAApplyDetailBankSelectVC alloc] init];
    
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:selectBankNameVC presentingViewController:self];
    presentationVC.type = KAFloatLayerTypeCustom;
    selectBankNameVC.transitioningDelegate = presentationVC;
    
    selectBankNameVC.selectedBankName = [self.viewModel bankName];

    selectBankNameVC.datasource = self.bankInfoArr;
    
    selectBankNameVC.sureButtonCallBack = ^(NSString * _Nonnull bankName) {
        @strongify(self)
        [self.viewModel setBankName:bankName];
        [self.tableView reloadData];
    };
    
    presentationVC.presentedView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [self presentViewController:selectBankNameVC animated:YES completion:nil];

}

- (void)getCurrentServerTime:(void (^)(void))nextBlock{
    if(self.serverTimeStamp.doubleValue>0){
        nextBlock();
    }else{
        [PlatformService showLoadingInView:self.view];

        [[self.viewModel getCurrentServerDateTime] subscribeNext:^(NSNumber * timestamp) {
            [PlatformService dismissInView:self.view];

            self.serverTimeStamp = timestamp;
            nextBlock();

        } error:^(NSError * _Nullable error){
            [PlatformService dismissInView:self.view];

            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:error.localizedDescription];
        }];
    }
}

-(void)selectReturnModel:(NSArray *)returnModelList{
    @weakify(self);
    JDISVKAApplyReturnModeSelectVC* selectReturnModelVC = [[JDISVKAApplyReturnModeSelectVC alloc] init];
    selectReturnModelVC.timestamp = self.serverTimeStamp;
    selectReturnModelVC.orderSelf = self.viewModel.orderSelf;
    
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:selectReturnModelVC presentingViewController:self];
    presentationVC.type = KAFloatLayerTypeCustom;
    selectReturnModelVC.transitioningDelegate = presentationVC;
    
    selectReturnModelVC.returnModelList = returnModelList;

    selectReturnModelVC.selectedReturnModel = self.viewModel.selectedReturnModel;

    selectReturnModelVC.sureButtonCallBack = ^(applyDetailGeneralTypeVo *selectedReturnModel) {
        @strongify(self)
        self.viewModel.selectedReturnModel = selectedReturnModel;
        [self.tableView reloadData];
        
        NSIndexPath *indexpath = [NSIndexPath indexPathForRow:self.viewModel.viewModelArray.count-1 inSection:0];
        [self.tableView scrollToRowAtIndexPath:indexpath atScrollPosition:UITableViewScrollPositionBottom animated:NO];
    };
    
    presentationVC.presentedView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [self presentViewController:selectReturnModelVC animated:YES completion:nil];

}


- (UITableView *)tableView{
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, [UIWindow ka_uikit_navigationHeight], self.view.frame.size.width, self.view.frame.size.height - (50 + self.viewModel.safeBottom) - [UIWindow ka_uikit_navigationHeight])];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKAApplyDetailCauseCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKAApplyDetailCauseCell"];
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKAApplyDetailCommodityCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKAApplyDetailCommodityCell"];
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKAApplyDetailReturnCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKAApplyDetailReturnCell"];
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKAApplyDetailReturnReceiveCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKAApplyDetailReturnReceiveCell"];
        [_tableView registerClass:JDISVKAApplyDetailReturnMoneyCell.class forCellReuseIdentifier:@"JDISVKAApplyDetailReturnMoneyCell"];
        [_tableView registerClass:JDISVKAApplyDetailReturnReasonCell.class forCellReuseIdentifier:@"JDISVKAApplyDetailReturnReasonCell"];
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.showsHorizontalScrollIndicator = NO;
    }
    return _tableView;
}

- (JDISVKAApplyDetailMainViewModel *)viewModel{
    if (!_viewModel) {
        _viewModel = [[JDISVKAApplyDetailMainViewModel alloc] init];
    }
    return _viewModel;
}

- (UIView *)bottomView{
    if (!_bottomView) {
        _bottomView = [[UIView alloc] init];
        _bottomView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    return _bottomView;
}

- (void)submitForm {
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel submitAfterSaleRequest] subscribeError:^(NSError * _Nullable error) {
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:error.localizedDescription];

    } completed:^{
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:Lang(@"after_sale_commit_success")];
        if (self.submitApplySuccess) {
            self.submitApplySuccess();
        }
        [self.navigationController popViewControllerAnimated:NO];
    }];
}

@end
