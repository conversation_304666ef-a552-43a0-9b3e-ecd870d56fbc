//
//  JDISVOrderDetailMainViewModel.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/2/24.
//

#import "JDISVOrderDetailMainViewModel.h"

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVFloorRenderModule/JDISVFloorConfigManager.h>
#import "JDISVOrderDetailModuleMacro.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@import JDTInfrastructureModule;
@import JDTCommonToolModule;

@interface JDISVOrderDetailMainViewModel ()
@property (nonatomic, assign) NSInteger sourceFrom;
@property (nonatomic, copy) NSString *orderId;
@end

@implementation JDISVOrderDetailMainViewModel
- (instancetype)initWithOrderId:(NSString * _Nonnull)orderId sourceFrom:(NSInteger)sourceFrom
{
    self = [super init];
    if (self) {
       
        _orderId = [orderId copy];
        _sourceFrom = sourceFrom;

    }
    return self;
}

+ (NSDictionary *)readLocalFileWithName:(NSString *)name {
    // 获取文件路径
    NSString *path = [[NSBundle mainBundle] pathForResource:name ofType:@"json"];
    // 将文件数据化
    NSData *data = [[NSData alloc] initWithContentsOfFile:path];
    // 对数据进行JSON格式化并返回字典形式
    return [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
}

// Signals
- (RACSignal *)loadOrderDetailSignal {
//    __weak typeof(self) weakSelf = self;
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
//        [weakSelf requestOrderDetailDataWithOrderId:weakSelf.orderId needEncrypt:YES complete:^(id  _Nullable responseObject, NSError * _Nullable error) {
//            if ([[NSUserDefaults standardUserDefaults] objectForKey:@"OrderDetailTestMockData"]) {
//                responseObject = [JDISVOrderDetailMainViewModel readLocalFileWithName:[[NSUserDefaults standardUserDefaults] objectForKey:@"OrderDetailTestMockData"]];
//            }
//            
//            if ([weakSelf errorInResponseResultWith:responseObject]) {
//                NSError *errorInResponse = [weakSelf errorInResponseResultWith:responseObject];
//                [weakSelf processError:errorInResponse];
//                [weakSelf recordLogWithInfo:error.localizedDescription funcName:[NSString stringWithFormat:@"%s", __func__]];
//                [subscriber sendError:error];
//            } else {
//                // 无错误 - 成功
//                NSDictionary *resultdata = (NSDictionary *)[responseObject objectForKey:@"value"] ? : @{};
//                [weakSelf recordLogWithInfo:[NSString stringWithFormat:@"success--orderId:%@",weakSelf.orderId] funcName:[NSString stringWithFormat:@"%s", __func__]];
//                
//                [weakSelf requestInvoiceInfoDataWithOrderId:resultdata complete:^(NSDictionary *data, NSError * _Nullable error) {
//                    //本地生活订单类型
//                    if (((NSNumber *)data[@"others"][@"orderType"]).integerValue == 75) {
//                        NSArray *shopInfoDTOList = data[@"shopInfoDTOList"] ? : @[];
//                        NSMutableArray *skuids = @[].mutableCopy;
//                        for (NSDictionary *shopInfo in shopInfoDTOList){
//                            NSArray *wareInfoDTOList = shopInfo[@"wareInfoDTOList"] ? : @[];
//                            for (NSDictionary *wareInfo in wareInfoDTOList){
//                                NSString *wareId = wareInfo[@"wareId"];
//                                if ([wareId jdcd_validateString]) {
//                                    [skuids addObject:wareId];
//                                }
//                            }
//                        }
//                        [weakSelf requestLocOrderShowQrcodeDataWithOrderId:skuids complete:^(BOOL isThird, NSError * _Nullable error) {
//                            NSMutableDictionary *tempData = data.mutableCopy;
//                            if (!error) {
//                                if (isThird) {
//                                    [tempData setObject:@(isThird) forKey:@"isThirdProduct"];
//                                }
//                            }
//                            [weakSelf processFloorData:tempData.copy];
//                            [subscriber sendCompleted];
//                        }];
//                    }else{
//                            [weakSelf processFloorData:data];
//                            [subscriber sendCompleted];
//                    }
//                }];
//            }
//        }];
        
        NSDictionary *testParams = @{
            @"orderId": self.orderId
        };
        [[OOPNetworkManager sharedManager] POST:@"order/detail?apiCode=b2c.cbff.order.detail" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                ORDERListItemModel *model = [ORDERListItemModel yy_modelWithDictionary:responseObject[@"data"]];
                if (model.shipmentOrder.count > 0) {
                    // 有物流信息的需要请求物流信息
                    [self requestOrderShipmentWithOrderId:self.orderId complete:^(NSArray *shipArr, NSError *error) {
                        if (!error) {
                            NSMutableDictionary *data = [responseObject[@"data"] mutableCopy];
                            data[@"shipmentOrder"] = shipArr;
                            [self processFloorData:data];
                            [subscriber sendCompleted];
                        } else {
                            NSDictionary *data = responseObject[@"data"];
                            [self processFloorData:data];
                            [subscriber sendCompleted];
                        }
                    }];
                } else if (model.appliedCancel) {
                    // 已取消的订单需要查看取消进度
                    [self requestCancelProgress:model complete:^(NSArray *progressArr, NSError *error) {
                        if (!error) {
                            NSMutableDictionary *data = [responseObject[@"data"] mutableCopy];
                            data[@"cancelProgress"] = progressArr;
                            [self processFloorData:data];
                            [subscriber sendCompleted];
                        } else {
                            NSDictionary *data = responseObject[@"data"];
                            [self processFloorData:data];
                            [subscriber sendCompleted];
                        }
                    }];
                } else {
                    NSDictionary *data = responseObject[@"data"];
                    [self processFloorData:data];
                    [subscriber sendCompleted];
                }
            }
        }];
        
        return nil;
    }];
}

- (void)requestOrderShipmentWithOrderId:(NSString *)orderId complete:(void (^)(NSArray *shipArr, NSError *error))completeBlock {
    NSDictionary *testParams = @{
        @"orderId": orderId
    };
    [[OOPNetworkManager sharedManager] POST:@"order/shipment?apiCode=b2c.cbff.order.shipment" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            completeBlock(nil, error);
        } else {
            NSArray <ORDERListItemShipmentOrderModel *> *shipmentArr = [NSArray yy_modelArrayWithClass:[ORDERListItemShipmentOrderModel class] json:responseObject[@"data"]];
            completeBlock(shipmentArr, nil);
        }
    }];
}

- (void)requestCancelProgress:(ORDERListItemModel *)model complete:(void (^)(NSArray *progressArr, NSError *error))completeBlock {
    NSDictionary *testParams = @{
        @"orderId": model.orderId,
        @"paid": @(model.paid)
    };
    [[OOPNetworkManager sharedManager] POST:@"order/cancelProgress?apiCode=b2c.cbff.order.cancelProgress" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            completeBlock(nil, error);
        } else {
            NSArray *cancelProgressArr = [NSArray yy_modelArrayWithClass:[ORDERDetailCancelProgressModel class] json:responseObject[@"data"]];
            completeBlock(cancelProgressArr, nil);
        }
    }];
}

#pragma mark - 请求发票接口

- (void)requestInvoiceInfoDataWithOrderId:(NSDictionary *)resultData
                                                            complete:(void(^)(NSDictionary *data, NSError *  _Nullable error))completeBlock{
        NSDictionary *param = @{@"orderId":self.orderId};
        @weakify(self)
        [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"user.order.queryOrderInvoiceInfo" function:@"" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            @strongify(self)
            if (error) {
                completeBlock(resultData, nil);
            } else {
                if ([self errorInResponseResultWith:responseObject]) {
                    completeBlock(resultData, nil);
                } else {
                    NSDictionary *OrderStatisticsInfoVO = [responseObject jdcd_getDicElementForKey:@"data"];
                    if (OrderStatisticsInfoVO && ([[OrderStatisticsInfoVO jdcd_getStringElementForKey:@"orderSummarayUrl"] jdcd_validateString]
                                                  || [[OrderStatisticsInfoVO jdcd_getStringElementForKey:@"taxInvoiceUrl"] jdcd_validateString])){
                        NSMutableDictionary *tempData = [NSMutableDictionary dictionaryWithDictionary:resultData];
                        [tempData setObject:OrderStatisticsInfoVO forKey:@"OrderStatisticsInfoVO"];
                        completeBlock(tempData, nil);
                    }else{
                        completeBlock(resultData, nil);
                    }
                }
            }
        }];
}

# pragma mark - 请求订单追踪接口
- (NSURLSessionDataTask * _Nonnull)requestTrackStatusDataWithOrderId:(NSString * _Nonnull)orderId
                                                            complete:(void(^)(NSDictionary *data, NSError *  _Nullable error))completeBlock {
    NSDictionary *param = @{@"orderId":orderId};
    return [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"orderMessage" version:@"1.0" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        
        if (error) {
            completeBlock(nil, error);
        } else {
            if ([self errorInResponseResultWith:responseObject]) {
                NSError *bussinessError = [self errorInResponseResultWith:responseObject];
                completeBlock(nil, bussinessError);
            } else {
                NSDictionary *data = (NSDictionary *)[responseObject objectForKey:@"value"];
                completeBlock(data, nil);
            }
        }
    }];
}

// Request
- (NSURLSessionDataTask * _Nonnull)requestOrderDetailDataWithOrderId:(NSString * _Nonnull)orderId
                                                         needEncrypt:(BOOL)isEncrypt
                                                            complete:(void(^)(id  _Nullable responseObject, NSError *  _Nullable error))completeBlock {
    NSNumber *encrypt = isEncrypt ? @(1) : @(0);
    NSDictionary *param = @{@"orderId":orderId, @"isEncrypt":encrypt};
    return [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"orderInfo" version:@"1.0" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        
        if (error) {
            completeBlock(nil, error);
        } else {
            completeBlock(responseObject, nil);
        }
    }];
}

//请求接口是否为三方商品, 用于判断是否展示loc订单核销码和门店
- (NSURLSessionDataTask * _Nonnull)requestLocOrderShowQrcodeDataWithOrderId:(NSArray * _Nonnull)skuids complete:(void(^)(BOOL isThird, NSError *  _Nullable error))completeBlock {
    NSDictionary *param = @{@"skuIds":skuids ?: @[]};
    return [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"is_third_party_batch1.0" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            completeBlock(NO, error);
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            NSNumber *success = [responseObject objectForKey:@"success"];
            NSString *message = [responseObject objectForKey:@"message"];
            if (success.boolValue) {
                NSDictionary *dataDic = [responseObject objectForKey:@"data"];
//                NSNumber *residueDegree = [dataDic objectForKey:@"residueDegree"];
//                NSNumber *numberOfDays = [dataDic objectForKey:@"numberOfDays"];
                NSArray *values = dataDic.allValues;
                BOOL tempvalue = NO;
                for (NSNumber *value in values) {
                    if (value.boolValue) {
                        tempvalue = YES;
                    }
                }
                completeBlock(tempvalue, nil);
            } else {
                completeBlock(NO, [NSError errorWithDomain:@"JDISVOrderDetailMainLocOrderShowQrcode" code:-999 userInfo:@{NSLocalizedDescriptionKey:OrderDetailL(@"ka_order_submit_error")}]);
            }
        }
    }];
}

#pragma mark - Data
// 预加载楼层
- (NSArray<NSArray<NSDictionary *> *> *)preloadingFloorData {
    NSArray *data = @[
        @[
            @{
                @"floorId":@"KaOrderNavigationFloor",
                @"data":@{
                        @"success":@(-1)
                },
                @"ext":@{
                        
                }
            },
        ]
    ];
    return [data copy];
}

// 数据请求后楼层
- (void)processFloorData:(NSDictionary * )rawData {
    
    NSMutableDictionary *dic = [rawData mutableCopy];
    
    self.allFloorOriginData = [dic mutableCopy];
    
    NSMutableArray *result = [NSMutableArray array];
        
//    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:KaOrderDetailPageId];
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:@"KaOrderDetailPageId2"];
    NSMutableArray *tempArray = [NSMutableArray arrayWithArray:config[@"layout"]];
    
    NSArray *floors = tempArray;
    NSDictionary *features = config[@"features"][@"floors"];
    for (NSArray *tempGroup in floors) {
        NSMutableArray *group = [NSMutableArray array];
        for (NSString *floorId in tempGroup) {
            NSMutableDictionary *floor = [NSMutableDictionary dictionary];
            if (floorId) {
                [floor setObject:floorId forKey:@"floorId"];
            }
            if (features[floorId]) {
                [floor setObject:features[floorId] forKey:@"ext"];
            }
            if ( self.allFloorOriginData) {
                [floor setObject: self.allFloorOriginData forKey:@"data"];
                [floor setObject:@(1) forKey:@"success"];
            } else {
                [floor setObject:@{} forKey:@"data"];
            }
            
            
            [group addObject:floor];
        }
        
        NSDictionary *deliver = @{
            @"floorId":@"KaOrderSepratorFloor",
            @"data":@{
                    
            },
            @"ext":@{
                    
            }
        };
        //
//        if ([tempGroup containsObject:@"KaOrderProductFloor"]) {
//            [group addObject:contactDic];
//        }
        
        if (![tempGroup containsObject:@"KaOrderNavigationFloor"] && ![tempGroup containsObject:@"KaOrderActionFloor"]) {
            [group addObject:deliver];
        }
        
       
        
        [result addObject:group];
        
    }
    
    self.floorGroups = [result copy];
}

// 错误展示页面
- (void)processError:(NSError * __nonnull)error {
    NSArray *data = @[
        @[
            @{
                @"floorId":@"KaOrderNavigationFloor",
                @"data":@{
                        @"success":@(0)
                },
                @"ext":@{
                        
                }
            },
        ],
        @[
            @{
                @"floorId":@"KaOrderLoadedFailFloor",
                @"data":error,
                @"ext":@{
                        
                }
            }
        ],
        @[
            @{
                @"floorId":@"KaTest",
                @"ext":@{
                        
                }
            }
        ]
    ];
    
    self.floorGroups = [data copy];
}

- (NSDictionary *)allFloorOriginData {
    if (!_allFloorOriginData) {
        _allFloorOriginData = [NSDictionary dictionary];
    }
    return _allFloorOriginData;
}

- (NSArray<NSArray<NSDictionary *> *> *)floorGroups {
    if (!_floorGroups) {
        _floorGroups = [NSArray array];
    }
    return _floorGroups;
}

#pragma mark - Private

- (NSError * _Nullable)errorInResponseResultWith:(id  _Nullable)responseObject {
    JDISVOrderDetailMainInterfaceResponseModel *responseModel = [JDISVOrderDetailMainInterfaceResponseModel yy_modelWithDictionary:responseObject];
    NSNumber *success = responseModel.success;
    if ([success boolValue]) return nil;
    
    // https://cf.jd.com/pages/viewpage.action?pageId=425079566
    NSNumber *resultCode = responseModel.resultCode;
    NSString *errorMsg = OrderDetailL(@"isv_order_failed_interface_exception");
    
    if (resultCode == nil) {
        return [NSError errorWithDomain:@"JDISVOrderDetail" code:-999 userInfo:@{NSLocalizedDescriptionKey:errorMsg}];
    }
    
    if ([resultCode isEqualToNumber:@(-1)]) {
    
        errorMsg = OrderDetailL(@"isv_order_failed_interface_exception");
    } else if ([resultCode isEqualToNumber:@(2)]) {
    
        errorMsg = OrderDetailL(@"isv_order_failed_interface_exception");
    } else if ([resultCode isEqualToNumber:@(3)]) {
    
        errorMsg = OrderDetailL(@"isv_order_failed_interface_exception");
    } else if ([resultCode isEqualToNumber:@(21)]) {
    
        // https://smart-agricultural.jd.com/my-wallet?jumpSource=native
        NSString *msg = responseModel.msg;
        if ([msg jdcd_validateString]) {
            errorMsg = [msg copy];
        } else {
            errorMsg = OrderDetailL(@"isv_order_account_balance​");
        }
    } else if ([resultCode isEqualToNumber:@(22)]) {
    
        NSString *msg = responseModel.msg;
        if (![msg jdcd_validateString]) {
            errorMsg = [msg copy];
        } else {
            errorMsg = OrderDetailL(@"isv_order_pay_done_refresh");
        }
    } else if ([resultCode isEqualToNumber:@(23)]) {
        
        NSString *msg = responseModel.msg;
        if (![msg jdcd_validateString]) {
            errorMsg = [msg copy];
        } else {
            errorMsg = OrderDetailL(@"isv_order_total_amount_limit");
        }
    } else if ([resultCode isEqualToNumber:@(24)]) {
        
        NSString *msg = responseModel.msg;
        if (![msg jdcd_validateString]) {
            errorMsg = [msg copy];
        } else {
            errorMsg = OrderDetailL(@"isv_order_network_error");
        }
    } else {
        errorMsg = OrderDetailL(@"isv_order_failed_interface_exception");
    }
    return [NSError errorWithDomain:@"JDISVOrderDetailMain" code:-999 userInfo:@{NSLocalizedDescriptionKey:errorMsg}];
}

// 用户记录
- (void)recordLogWithInfo:(NSString * _Nullable)info funcName:(NSString *)func {
    NSDate *date = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss zzz"];
    NSString *dateVaule = [dateFormatter stringFromDate:date];
    NSString *infoValue = @"";
    if ([info jdcd_validateString]) {
        infoValue = [info copy];
    }
    NSString *funcValue = @"";
    if ([func jdcd_validateString]) {
        funcValue = [func copy];
    }
    NSDictionary *paramDict = @{
        @"date": dateVaule,
        @"info": infoValue,
        @"func": funcValue,
        @"file": NSStringFromClass(self.class),
        @"module": @"JDISVOrderDetailSDKModule"
    };
    [JDRouter openURL:@"router://JDISVLogModule/saveLog" arg:paramDict error:nil completion:nil];
}
@end

@implementation JDISVOrderDetailMainInterfaceResponseModel

@end

//工作内容
