//
//  JDISVOrderDetailOrderTrackFloorModule.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/1.
//

#import "JDISVOrderDetailOrderTrackFloorModule.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

#import "JDISVOrderDetailOrderTrackFloorModel.h"
#import "JDISVOrderDetailOrderActionFloorModel.h"
#import "NSBundle+JDISVOrderDetailSDK.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

JDISVRegisterFloorModule(KaOrderTrackFloor, JDISVOrderDetailOrderTrackFloorModule);
@interface JDISVOrderDetailOrderTrackFloorModule ()
@property (nonatomic, copy) NSString *logisticsStatusText;
@property (nonatomic, copy) NSString *logisticsTimeText;
//
@property (nonatomic, copy) NSString *consigneeText;
@property (nonatomic, copy) NSString *consigneeAddressText;
@property (nonatomic, copy) NSString *consigneePhoneText;

@property (nonatomic, assign) CGFloat height;
@property (nonatomic, strong) JDISVOrderDetailOrderTrackFloorModel *model;
@end

@implementation JDISVOrderDetailOrderTrackFloorModule
#pragma mark - JDISVFloorModuleProtocol
- (UINib *)tableViewFloorNib {
    
    return [UINib nibWithNibName:@"JDISVOrderDetailOrderTrackFloor" bundle:[NSBundle jdisvOrderDetailSDK_bundle]];
}


- (CGFloat)floorHeight {
    
    return self.height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *allFloorData = data[@"data"] ? : @{};
    ORDERListItemModel *model = [ORDERListItemModel yy_modelWithDictionary:allFloorData];
    if (model.shipmentOrder) {
        self.shipmentArr = model.shipmentOrder;
    }
    if (model.orderState.state == ORDERListItemStateTypeCanceled) {
        self.cancelProgressArr = allFloorData[@"cancelProgress"];
    }
    self.orderId = allFloorData[@"orderId"];
    self.orderState = model.orderState.state;
    if (model.orderType == ProductTypeGoods) {
        [self updateTrackModelWith:model allFloorData:allFloorData];
    } else {
        self.height = 0;
    }
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel{
    NSDictionary *allFloorData = data[@"data"] ? : @{};
    NSDictionary *orderIdDic = allFloorData[@"orderIdInfoDTO"] ? : @{};
    self.model = [JDISVOrderDetailOrderTrackFloorModel yy_modelWithDictionary:allFloorData];
    
    self.orderId = orderIdDic[@"orderId"]?:@"";
    [self updateViewModelWith:_model allFloorData:allFloorData];
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _logisticsStatusText = @"";
        _logisticsTimeText = @"";
        self.consigneeText = @"";
        self.consigneeAddressText = @"";
        self.consigneePhoneText = @"";
    }
    return self;
}

- (void)updateTrackModelWith:(ORDERListItemModel *)model allFloorData:(NSDictionary *)allFloorData {
    // 1、物流进度
    if (model.orderState.state == ORDERListItemStateTypeCanceled) {
        // 已取消订单，根据order/cancelProgress接口的返回展示
        ORDERDetailCancelProgressModel *model = [allFloorData[@"cancelProgress"] firstObject];
        self.logisticsStatusText = model.statusDesc;
        self.logisticsTimeText = model.finishTime;
    } else if (model.orderState.state == ORDERListItemStateTypeWaitPay) {
        // 待支付不显示进度，只显示收货信息
        self.logisticsStatusText = @"";
        self.logisticsTimeText = @"";
    } else if (model.orderState.state == ORDERListItemStateTypeLocked) {
        // 已锁定状态显示 finished 字段为 YES 的进度
        NSArray <ORDERDetailCancelProgressModel *> *modelArr = allFloorData[@"cancelProgress"];
        for (ORDERDetailCancelProgressModel *model in modelArr) {
            if (model.finished) {
                self.logisticsStatusText = model.statusDesc;
                self.logisticsTimeText = model.finishTime;
                break;
            }
        }
    } else {
        if (self.shipmentArr.count > 1) {
            self.logisticsStatusText = [NSString stringWithFormat:OrderDetailL(@"isv_order_order_detail_multi_package_num"), @(self.shipmentArr.count), @(self.shipmentArr.count)];
            self.logisticsTimeText = @"";
        } else {
            self.logisticsStatusText = self.shipmentArr.firstObject.orderUms.firstObject.content;
            self.logisticsTimeText = self.shipmentArr.firstObject.orderUms.firstObject.msgTime;
        }
    }
    if (self.logisticsStatusText.length > 0) {
        self.logisticsStatusAttributedText = [[NSAttributedString alloc] initWithString:self.logisticsStatusText attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"], NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
        CGFloat statusLabelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 54.f/*leading*/ - 42.f/*trailing*/ - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]*2;
        CGSize size = [self.logisticsStatusText jdcd_sizeWithContainer:CGSizeMake(statusLabelWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2];
        self.logisticsStatusLabelHeight = ceil(size.height);
        self.logisticsStatusLabelWidth = ceil(size.width);
    } else {
        self.logisticsStatusLabelHeight = 0;
        self.logisticsTimeLabelHeight = 0;
        self.logisticsStatusLabelWidth = 0;
    }
    if (self.logisticsTimeText.length > 0) {
        self.logisticsTimeAttributedText = [[NSAttributedString alloc] initWithString:self.logisticsTimeText attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"], NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]}];
        CGFloat timeLabelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 54.f/*leading*/ - 42.f/*trailing*/ - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]*2;
        self.logisticsTimeLabelHeight = ceil([self.logisticsTimeText jdcd_sizeWithContainer:CGSizeMake(timeLabelWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:2].height);
    } else {
        self.logisticsTimeLabelHeight = 0;
    }
    
    self.topHeight = self.logisticsStatusLabelHeight + self.logisticsTimeLabelHeight;
    
    // 2、收货信息
    if (model.orderConsignee) {
        self.consigneeText = model.orderConsignee.fullName;
        self.consigneeAddressText = model.orderConsignee.address;
        self.consigneePhoneText = model.orderConsignee.mobileDes;
        self.consigneeAttributedString = [[NSAttributedString alloc] initWithString:self.consigneeText attributes:@{NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"], NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]}];
        self.consigneeWidth = ceil([self.consigneeText jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
        self.consigneePhoneAttributedString = [[NSAttributedString alloc] initWithString:self.consigneePhoneText attributes:@{NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium], NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]}];
        self.consigneeHeight = 20.f;
        
        self.consigneeAddressAttributedString = [[NSAttributedString alloc] initWithString:self.consigneeAddressText attributes:@{NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"], NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]}];
        CGFloat labelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 54.f - 18.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]*2;
        self.consigneeAddressHeight = ceil([self.consigneeAddressText jdcd_sizeWithContainer:CGSizeMake(labelWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:2].height);
        self.bottomHeight = self.consigneeHeight + self.consigneeAddressHeight;
    }
    if (self.topHeight > 0) {
        self.consigneeLabelTop = 18.f+ self.topHeight + 18.f;
    } else {
        self.consigneeLabelTop = 18.f;
    }
    
    if (self.bottomHeight) {
        self.height = self.consigneeLabelTop + self.bottomHeight + 18.f;
    }
}

- (void)updateViewModelWith:(JDISVOrderDetailOrderTrackFloorModel *)model allFloorData:(NSDictionary *)allFloorData{
    //待支付，不展示物流
    JDISVOrderDetailOrderActionFloorModel *actionmodel = [JDISVOrderDetailOrderActionFloorModel yy_modelWithDictionary:allFloorData];
    self.isFixOrder = NO;
//    for (JDISVOrderDetailOrderActionFloorModelButtonInfoModel *buttonmodel in actionmodel.buttonInfoList) {
//        if (buttonmodel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeModifyOrder){
//            //修改订单
//            self.isFixOrder = YES;
//            break;
//        }
//    }
    NSString *orderstatus = allFloorData[@"orderStatusDTO"][@"orderStatusId"] ? : @"";
    if (![orderstatus isEqualToString:@"1"] && model.logisticsInfoModel) {
        //一单多包裹
        NSDictionary* otherDic = allFloorData[@"others"];
        NSString *multiPackageInfoJson = otherDic[@"multiPackageInfo"];
        if(multiPackageInfoJson){
            NSDictionary *multiPackageInfoDic = [self dictionaryWithJsonString:multiPackageInfoJson];
            NSNumber *packageSumNum = multiPackageInfoDic[@"packageSumNum"];//总包裹数
            NSNumber *packageSendNum = multiPackageInfoDic[@"packageSumNum"];//已发数量
            self.logisticsStatusText = OrderDetailL(@"isv_order_oisv_order_order_detail_multi_package_statusrder_detail_multi_package_status");
            self.logisticsTimeText = [NSString stringWithFormat:OrderDetailL(@"isv_order_order_detail_multi_package_num"),packageSumNum,packageSendNum];
            self.isSmipleTrack = NO;
        } else
        if ([_model.logisticsInfoModel.message jdcd_validateString] && [_model.logisticsInfoModel.messageTime jdcd_validateString]){//完整版物流信息
            self.logisticsStatusText = _model.logisticsInfoModel.message;
            self.logisticsTimeText = _model.logisticsInfoModel.messageTime;
            self.isSmipleTrack = NO;
        }
//        else if ([_model.logisticsInfoModel.carrier jdcd_validateString] && [_model.logisticsInfoModel.waybillCode jdcd_validateString]){
//            //简版物流信息
//            self.logisticsStatusText = _model.logisticsInfoModel.carrier;
//            self.logisticsTimeText = _model.logisticsInfoModel.waybillCode;
//            self.isSmipleTrack = YES;
//        }
        else{
            self.isSmipleTrack = NO;
            self.logisticsStatusText = @"";
            self.logisticsTimeText = @"";
        }
    } else {
        self.isSmipleTrack = NO;
        self.logisticsStatusText = @"";
        self.logisticsTimeText = @"";
    }
    if ([_logisticsStatusText jdcd_validateString]) {
        self.logisticsStatusAttributedText = [[NSAttributedString alloc] initWithString:_logisticsStatusText
                                                                             attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                                                                                          NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
        CGFloat labelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 54.f/*leading*/ - 42.f/*trailing*/ - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]*2;
        CGSize size = [self.logisticsStatusText jdcd_sizeWithContainer:CGSizeMake(labelWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2];
        self.logisticsStatusLabelHeight = ceil(size.height);
        self.logisticsStatusLabelWidth = ceil(size.width);
    } else {
        self.logisticsStatusAttributedText = nil;
        self.logisticsStatusLabelHeight = 20;
    }
    
    if ([_logisticsTimeText jdcd_validateString]) {
        if (self.isSmipleTrack){
            self.logisticsTimeAttributedText = [[NSAttributedString alloc] initWithString:_logisticsTimeText
                                                                                 attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                                                                                              NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
            self.logisticsTimeLabelHeight = 0;
        }else{
            self.logisticsTimeAttributedText = [[NSAttributedString alloc] initWithString:_logisticsTimeText
                                                                                 attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
                                                                                              NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]}];
            CGFloat labelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 54.f/*leading*/ - 42.f/*trailing*/ - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]*2;
            self.logisticsTimeLabelHeight = ceil([self.logisticsTimeText jdcd_sizeWithContainer:CGSizeMake(labelWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:2].height);
        }
    } else {
        self.logisticsTimeAttributedText = nil;
        self.logisticsTimeLabelHeight = 16;
    }
   
    
    
    if (self.logisticsTimeLabelHeight != 0 && self.logisticsStatusLabelHeight != 0) {
        self.topHeight = self.logisticsStatusLabelHeight + 4.f + self.logisticsTimeLabelHeight;
    }else if (self.logisticsTimeLabelHeight == 0 && self.logisticsStatusLabelHeight != 0){
        self.topHeight = self.logisticsStatusLabelHeight + self.logisticsTimeLabelHeight;
    }
    else {
        self.topHeight = 0;
    }
    
    
    // 收件人地址信息
    if (model.addressInfoModel) {
        if ([_model.addressInfoModel.customerName jdcd_validateString]) {
            self.consigneeText = _model.addressInfoModel.customerName;
        } else {
            self.consigneeText = @"";
        }
        
        if ([_model.addressInfoModel.address jdcd_validateString]) {
            self.consigneeAddressText = _model.addressInfoModel.address;
        } else {
            self.consigneeAddressText = @"";
        }
        
        if ([_model.addressInfoModel.mobile jdcd_validateString]) {
            self.consigneePhoneText = _model.addressInfoModel.mobile;
        } else {
            self.consigneePhoneText = @"";
        }
    } else {
        self.consigneeText = @"";
        self.consigneeAddressText = @"";
        self.consigneePhoneText = @"";
    }
        
    
    if ([_consigneeText jdcd_validateString]) {
        self.consigneeAttributedString = [[NSAttributedString alloc] initWithString:_consigneeText
                                                                         attributes:@{NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
                                                                                      NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]}];
    } else {
        self.consigneeAttributedString = nil;
    }
    
    // 收件人姓名宽度计算
    if (_consigneeAttributedString) {
        self.consigneeWidth = ceil([_consigneeText jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
        
    } else {
        self.consigneeWidth = 0;
    }
    
    if ([_consigneePhoneText jdcd_validateString]) {
        self.consigneePhoneAttributedString = [[NSAttributedString alloc] initWithString:_consigneePhoneText
                                                                         attributes:@{NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium],
                                                                                      NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]}];;
    } else {
        self.consigneePhoneAttributedString = nil;
    }
    
    // 第一行高度
    if (_consigneeAttributedString || _consigneePhoneAttributedString) {
        self.consigneeHeight = 20.f;
    } else {
        self.consigneeHeight = 0.f;
    }
    
    
    if ([_consigneeAddressText jdcd_validateString]) {
        self.consigneeAddressAttributedString = [[NSAttributedString alloc] initWithString:_consigneeAddressText
                                                                                attributes:@{NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"],
                                                                                             NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]}];
        CGFloat labelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 54.f - 18.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]*2;
        self.consigneeAddressHeight = ceil([_consigneeAddressText jdcd_sizeWithContainer:CGSizeMake(labelWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:2].height);
    } else {
        self.consigneeAddressAttributedString = nil;
        self.consigneeAddressHeight = 0;
    }
    
    if (self.consigneeHeight == 0 && self.consigneeAddressHeight == 0) {
        self.bottomHeight = 0;
    } else if (self.consigneeHeight != 0 && self.consigneeAddressHeight != 0) {
        self.bottomHeight = self.consigneeHeight + 4.f + self.consigneeAddressHeight;
    } else {
        self.bottomHeight = self.consigneeHeight + self.consigneeAddressHeight;
    }
    if (self.topHeight > 0) {
        self.consigneeLabelTop = 18.f+ self.topHeight + 18.f;
    } else {
        self.consigneeLabelTop = 18.f;
    }
    
    if (self.bottomHeight) {
        self.height = self.consigneeLabelTop + self.bottomHeight + 18.f;
    }
    
    
    if (self.topHeight == 0 && self.bottomHeight == 0) {
        self.height = 0;
    }
    
    //修改地址审批的黄色展示条
    NSDictionary* otherDic = allFloorData[@"others"];
    NSDictionary *extData = otherDic[@"extData"];
    NSString *onEditAddressAlert = extData[@"onEditAddressAlert"];//修改地址审批中的小黄条文案
    NSDictionary *orderModifyAddressExt = extData[@"orderModifyAddressExt"];//修改地址审批中，申请单以及地址信息
    self.orderModifyAddressModel = [JDISVOrderDetailOrderTrackFloorModifyAddressModel yy_modelWithDictionary:orderModifyAddressExt];
    if ((self.orderModifyAddressModel.applyStatus.intValue == 1 || self.orderModifyAddressModel.applyStatus.intValue == 3) && [onEditAddressAlert jdcd_validateString]) {
        CGFloat H = [onEditAddressAlert jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightRegular] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width - 54-42, 30)].height  + 1.f;
        self.height += 20/*上边距*/ + H - 8;
        self.onEditAddressAlertText = onEditAddressAlert;
    }
    
    
    //隐藏物流楼层：自提 || 本地生活
    if (model.isSelfMention || ((NSNumber *)allFloorData[@"others"][@"orderType"]).integerValue == 75) {
        self.height = 0;
        self.logisticsStatusAttributedText = nil;
        self.logisticsStatusLabelHeight = 0;
        self.logisticsTimeAttributedText = nil;
        self.logisticsTimeLabelHeight = 0;
        self.consigneeAttributedString = nil;
        self.consigneeHeight = 0.f;
        self.consigneeAddressAttributedString = nil;
        self.consigneeAddressHeight = 0;
        self.topHeight = 0;
        self.bottomHeight = 0;
        self.consigneeLabelTop = 0;
    }
}

- (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString
{
    if (jsonString == nil) {
        return nil;
    }

    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err) {
        return nil;
    }
    return dic;
}
@end
