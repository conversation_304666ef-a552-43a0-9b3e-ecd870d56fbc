//
//  JDISVOrderDetailOrderTrackFloor.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/1.
//

#import "JDISVOrderDetailOrderTrackFloor.h"
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVOrderDetailOrderTrackFloorModule.h"
#import <JDISVCategoryModule/UIImage+JDCDRTL.h>
#import "JDISVOrderDetailOrderTrackModifyAddressController.h"

static NSString * const kJDISVOrderDetailOrderTrackFloorActionTrackOrder = @"kJDISVOrderDetailOrderTrackFloorActionTrackOrder";
static NSString * const kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyAddress = @"kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyAddress";
static NSString * const kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyOrderAddress = @"kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyOrderAddress";

@interface JDISVOrderDetailOrderTrackFloor ()<JDCDISVActionTransferProtocol>
@property (weak, nonatomic) IBOutlet UIView *tapHotView;
// 物流信息
@property (weak, nonatomic) IBOutlet UIImageView *logisticsLogoImageView;
@property (weak, nonatomic) IBOutlet UILabel *logisticsStatusLabel;
@property (weak, nonatomic) IBOutlet UILabel *logisticsTimeLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *logisticsLabelHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *logisticsTimeLableHeight;

@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImageView;
// 收件人信息
@property (weak, nonatomic) IBOutlet UIImageView *consigneeLogoImageView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *consigneeLabelTop;
@property (weak, nonatomic) IBOutlet UILabel *consigneeLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *consigeeLabelWidth;
@property (weak, nonatomic) IBOutlet UILabel *consigeePhoneLabel;
@property (weak, nonatomic) IBOutlet UILabel *consigneeAddressLabel;
@property (weak, nonatomic) IBOutlet UIImageView *addressRightArrow;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *consigneeAddressLabelHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *consigneeLabelHeight;
@property (weak, nonatomic) IBOutlet UILabel *wayillCode;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mrrginLeft;
@property (weak, nonatomic) IBOutlet UIButton *codeCopyBtn;
//@property (weak, nonatomic) IBOutlet NSLayoutConstraint *codeCopyBtnWidth;


@property (weak, nonatomic) IBOutlet UIView *modifyTipLine;
@property (weak, nonatomic) IBOutlet UILabel *modifyTipLabel;
@property (weak, nonatomic) IBOutlet UIImageView *tipRightArrow;


@property (nonatomic, strong) JDISVOrderDetailOrderTrackFloorModule *viewModel;
@end

@implementation JDISVOrderDetailOrderTrackFloor

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.tapHotView.backgroundColor = [UIColor clearColor];
    [self.tapHotView jd_addTapAction:@selector(trackOrderInfoAction) withTarget:self];
    
    self.rightArrowImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.logisticsLogoImageView.image = [UIImage ka_iconWithName:JDIF_ICON_LOGISTICS imageSize:CGSizeMake(24.f, 24.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    
    self.consigneeLogoImageView.image = [UIImage ka_iconWithName:JDIF_ICON_LOCATION_FILL_SMALL imageSize:CGSizeMake(24.f, 24.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    self.addressRightArrow.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.addressRightArrow.userInteractionEnabled = YES;
    [self.consigneeAddressLabel jd_addTapAction:@selector(trackOrderModifyOrderAction) withTarget:self];
    [self.addressRightArrow jd_addTapAction:@selector(trackOrderModifyOrderAction) withTarget:self];
    
    self.tipRightArrow.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.tipRightArrow.userInteractionEnabled = YES;
    self.modifyTipLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    [self.modifyTipLabel jd_addTapAction:@selector(trackOrderModifyAction) withTarget:self];
    [self.tipRightArrow jd_addTapAction:@selector(trackOrderModifyAction) withTarget:self];
    self.modifyTipLine.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C3"];

    
//    [self.codeCopyBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateNormal];
    NSString *title = OrderDetailL(@"isv_order_text_copy");
    [self.codeCopyBtn setTitle:title forState:UIControlStateNormal];
    self.codeCopyBtn.layer.cornerRadius = 12.f;
    self.codeCopyBtn.layer.borderWidth = 0.5;
    self.codeCopyBtn.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"].CGColor;
    self.codeCopyBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 5.f, 0, 5.f);
//    self.codeCopyBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
//    [self.codeCopyBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateNormal];
//    self.codeCopyBtnWidth.constant = 40.f;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    self.codeCopyBtn.hidden = !self.viewModel.isSmipleTrack;
    self.wayillCode.hidden = !self.viewModel.isSmipleTrack;
//    self.codeCopyBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    if (_viewModel.topHeight == 0) {
        self.rightArrowImageView.hidden = YES;
        self.logisticsLogoImageView.hidden = YES;
    } else {
        self.rightArrowImageView.hidden = NO;
        self.logisticsLogoImageView.hidden = NO;
    }
    
    if (_viewModel.bottomHeight == 0) {
        self.consigneeLogoImageView.hidden = YES;
    } else {
        self.consigneeLogoImageView.hidden = NO;
    }
    
    if (self.viewModel.isSmipleTrack){
        self.rightArrowImageView.hidden = YES;
        self.mrrginLeft.constant = -self.viewModel.logisticsStatusLabelWidth - 10;
    }
    
    if (_viewModel.logisticsStatusAttributedText) {
        self.logisticsStatusLabel.attributedText = _viewModel.logisticsStatusAttributedText;
        self.logisticsStatusLabel.hidden = NO;
        self.logisticsLabelHeight.constant = _viewModel.logisticsStatusLabelHeight;
    } else {
        self.logisticsStatusLabel.hidden = YES;
        self.logisticsLabelHeight.constant = 0;
    }
    
    if (_viewModel.logisticsTimeAttributedText) {
        self.logisticsTimeLabel.attributedText = _viewModel.logisticsTimeAttributedText;
        self.logisticsTimeLabel.hidden = NO;
        self.logisticsTimeLableHeight.constant = _viewModel.logisticsTimeLabelHeight;
        self.wayillCode.attributedText = _viewModel.logisticsTimeAttributedText;
    } else {
        self.logisticsTimeLabel.hidden = YES;
        self.logisticsTimeLableHeight.constant = 0;
    }
    
    // 收件人地址信息
    self.consigneeLabelTop.constant = _viewModel.consigneeLabelTop;
    
    if (_viewModel.consigneeAttributedString) {
        self.consigneeLabel.attributedText = _viewModel.consigneeAttributedString;
        self.consigneeLabel.hidden = NO;
        self.consigeeLabelWidth.constant = _viewModel.consigneeWidth;
    } else {
        self.consigneeLabel.hidden = YES;
        self.consigeeLabelWidth.constant = 0;
    }
    
    if (_viewModel.consigneePhoneAttributedString) {
        _consigeePhoneLabel.attributedText = _viewModel.consigneePhoneAttributedString;
        _consigeePhoneLabel.hidden = NO;
    } else {
        _consigeePhoneLabel.hidden = YES;
    }
    
    
    self.consigneeLabelHeight.constant = _viewModel.consigneeHeight;
    
    if (_viewModel.consigneeAddressAttributedString) {
        self.consigneeAddressLabel.attributedText = _viewModel.consigneeAddressAttributedString;
        self.consigneeAddressLabel.hidden = NO;
        self.consigneeAddressLabelHeight.constant = _viewModel.consigneeAddressHeight;
    } else {
        self.consigneeAddressLabel.hidden = YES;
        self.consigneeAddressLabelHeight.constant = 0;
    }
    
    if (self.viewModel.isFixOrder){
        self.addressRightArrow.hidden = NO;
    }else{
        self.addressRightArrow.hidden = YES;
    }
    
    if (_viewModel.onEditAddressAlertText) {
        self.modifyTipLabel.text = _viewModel.onEditAddressAlertText;
        self.modifyTipLine.hidden = NO;
        self.modifyTipLabel.hidden = NO;
        self.tipRightArrow.hidden = NO;
        //        self.consigneeAddressLabelHeight.constant = _viewModel.consigneeAddressHeight;
    } else {
        self.modifyTipLine.hidden = YES;
        self.modifyTipLabel.hidden = YES;
        self.tipRightArrow.hidden = YES;
        //        self.consigneeAddressLabelHeight.constant = _viewModel.consigneeAddressHeight;
    }
}

- (void)trackOrderInfoAction {
    JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVOrderDetailOrderTrackFloorActionTrackOrder];
    if (self.viewModel.orderState == ORDERListItemStateTypeCanceled) {
        action.value = self.viewModel.cancelProgressArr;
    } else {
        action.value = self.viewModel.shipmentArr;
    }
    [self isv_sendAction:action];
}

- (void)trackOrderModifyOrderAction {
    //打开修改地址页面
    if (self.viewModel.isFixOrder){
        JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyOrderAddress broadcastAction:YES];
    //    action.value = self.viewModel.orderId;
        [self isv_sendAction:action];
    }
}

- (void)trackOrderModifyAction {
    //打开修改的地址信息浮层
    JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyAddress];
//    action.value = self.viewModel.orderId;
    [self isv_sendAction:action];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDISVOrderDetailOrderTrackFloorActionTrackOrder]) {
        UIViewController *vc;
        if (self.viewModel.orderState == ORDERListItemStateTypeCanceled) {
            NSArray <ORDERDetailCancelProgressModel *> *progrssArr = (NSArray *)action.value;
            vc = [JDRouter openURL:@"router://JDISVOrderListSDKModule/getOrderTrackViewController" arg:@{
                @"contentType": @(2),
                @"progressDataList": progrssArr,
                @"orderId": self.viewModel.orderId
            } error:nil completion:nil];
        } else {
            NSArray <ORDERListItemShipmentOrderModel *> *shipmentArr = (NSArray *)action.value;
            vc = [JDRouter openURL:@"router://JDISVOrderListSDKModule/getOrderTrackViewController" arg:@{
                @"contentType": @(1),
                @"trackDataList": shipmentArr
            } error:nil completion:nil];
        }
        
        KAFloatLayerPresentationController *floatVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:vc presentingViewController:controller];
        floatVC.contentHeight = 0.68 * [[UIScreen mainScreen] bounds].size.height;
        floatVC.defultHeader.titleLabel.text = (self.viewModel.orderState == ORDERListItemStateTypeCanceled ? @"退款进度" : @"订单跟踪");
        vc.transitioningDelegate = floatVC;
        [controller presentViewController:vc animated:YES completion:nil];
        
//        NSDictionary *arg = @{@"orderId":orderId, @"sourceFrom":@(0)};
//        UIViewController *vc = [JDRouter openURL:@"router://JDISVOrderDetailSDKModule/getOrderDetailTrackStatusController" arg:arg error:nil completion:nil];
//        
//        [controller.navigationController pushViewController:vc animated:YES];
        
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyAddress]) {
        
        JDISVOrderDetailOrderTrackModifyAddressController* LocShop = [[JDISVOrderDetailOrderTrackModifyAddressController alloc] init];
        KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:LocShop presentingViewController:controller];
        presentationVC.type = KAFloatLayerTypeCustom;
        presentationVC.contentHeight = 1106/2;
        presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
        LocShop.transitioningDelegate = presentationVC;
        LocShop.orderModifyAddressModel = self.viewModel.orderModifyAddressModel;
        [controller presentViewController:LocShop animated:YES completion:nil];
        return YES;
    }
    
    return NO;
}
- (IBAction)userCopyCode:(id)sender {
    UIPasteboard* pasteboard = [UIPasteboard generalPasteboard];
    pasteboard.string = self.viewModel.logisticsTimeAttributedText.string;
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish showTime:2.0 message:OrderDetailL(@"isv_order_copy_success")];
}

@end
