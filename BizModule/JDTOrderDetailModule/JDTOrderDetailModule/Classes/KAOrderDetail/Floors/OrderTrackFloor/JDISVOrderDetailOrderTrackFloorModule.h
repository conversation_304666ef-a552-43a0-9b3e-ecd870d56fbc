//
//  JDISVOrderDetailOrderTrackFloorModule.h
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/1.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>

@import JDTCommonToolModule;

@class JDISVOrderDetailOrderTrackFloorModifyAddressModel, ORDERListItemShipmentOrderModel;

NS_ASSUME_NONNULL_BEGIN

@interface JDISVOrderDetailOrderTrackFloorModule : NSObject <JDISVFloorModuleProtocol>
@property (nonatomic, copy, nullable) NSAttributedString *logisticsStatusAttributedText;
@property (nonatomic, copy, nullable) NSAttributedString *logisticsTimeAttributedText;
@property (nonatomic, assign) CGFloat logisticsStatusLabelHeight;
@property (nonatomic, assign) CGFloat logisticsStatusLabelWidth;
@property (nonatomic, assign) CGFloat logisticsTimeLabelHeight;
//
@property (nonatomic, assign) CGFloat consigneeLabelTop;
@property (nonatomic, copy, nullable) NSAttributedString *consigneeAttributedString;
@property (nonatomic, copy, nullable) NSAttributedString *consigneeAddressAttributedString;
@property (nonatomic, copy, nullable) NSAttributedString *consigneePhoneAttributedString;
@property (nonatomic, assign) CGFloat consigneeWidth;
@property (nonatomic, assign) CGFloat consigneeHeight;
@property (nonatomic, assign) CGFloat consigneeAddressHeight;

@property (nonatomic, assign) CGFloat topHeight;
@property (nonatomic, assign) CGFloat bottomHeight;

@property (nonatomic, copy) NSString *orderId;

@property (nonatomic, assign) ORDERListItemStateType orderState;

@property (nonatomic, assign) BOOL isSmipleTrack;

@property (nonatomic, assign) BOOL isFixOrder;//是否有修改订单入口

@property (nonatomic, copy) NSString *onEditAddressAlertText;
@property (nonatomic, strong) JDISVOrderDetailOrderTrackFloorModifyAddressModel *orderModifyAddressModel;
/// 展示包裹使用
@property (nonatomic, copy) NSArray <ORDERListItemShipmentOrderModel *> *shipmentArr;
/// 展示取消进度
@property (nonatomic, copy) NSArray <ORDERDetailCancelProgressModel *> *cancelProgressArr;

@end

NS_ASSUME_NONNULL_END
