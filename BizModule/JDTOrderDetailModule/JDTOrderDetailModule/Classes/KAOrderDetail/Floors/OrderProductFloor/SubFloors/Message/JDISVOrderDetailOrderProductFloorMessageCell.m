//
//  JDISVOrderDetailOrderProductFloorMessageCell.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/2.
//

#import "JDISVOrderDetailOrderProductFloorMessageCell.h"
#import "JDISVOrderDetailOrderProductFloorMessageCellViewModel.h"

@interface JDISVOrderDetailOrderProductFloorMessageCell ()
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet UILabel *messageLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *messageLabelHeight;
@property (nonatomic, strong) JDISVOrderDetailOrderProductFloorMessageCellViewModel *viewModel;
@end

@implementation JDISVOrderDetailOrderProductFloorMessageCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    self.messageLabel.textAlignment = NSTextAlignmentLeft;
}

- (void)updateCellWithViewModel:(__kindof JDISVOrderDetailOrderProductFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVOrderDetailOrderProductFloorMessageCellViewModel.class]) {
        self.viewModel = (JDISVOrderDetailOrderProductFloorMessageCellViewModel *)viewModel;
        if (_viewModel.height != 0) {
            self.titleLabel.hidden = NO;
            self.messageLabel.hidden = NO;
            
            self.titleLabel.attributedText = _viewModel.titleAttributedText;
            self.messageLabel.attributedText = _viewModel.messageAttributedText;
            self.messageLabelHeight.constant = _viewModel.messageHeight;
        } else {
            self.titleLabel.hidden = YES;
            self.messageLabel.hidden = YES;
        }
    }
}

@end
