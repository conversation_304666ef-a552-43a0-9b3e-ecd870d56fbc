//
//  JDISVOrderDetailOrderPriceFloorModule.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/3.
//

#import "JDISVOrderDetailOrderPriceFloorModule.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import "JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel.h"
#import "JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel.h"
#import "JDISVOrderDetailOrderPriceFloorModel.h"
#import "JDISVOrderDetailOrderPriceFloor.h"

#import "NSBundle+JDISVOrderDetailSDK.h"
#import "JDISVOrderDetailOrderPriceFloor.h"
#import "JDISVOrderProcessFloorModel.h"
#import "JDISVOrderDetailModuleMacro.h"
#import "JDISVOrderDetailPresaleStatusFloorModule.h"
@import JDTCommonToolModule;

JDISVRegisterFloorModule(KaOrderPriceFloor, JDISVOrderDetailOrderPriceFloorModule);
@interface JDISVOrderDetailOrderPriceFloorModule ()
@property (nonatomic, assign) CGFloat height;
@end

@implementation JDISVOrderDetailOrderPriceFloorModule
#pragma mark - JDISVFloorModuleProtocol
- (UIView *)floorView {
    UIView* v = [[JDISVOrderDetailOrderPriceFloor alloc] init];
    return v;
}


- (CGFloat)floorHeight {
    
    return self.height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

//- (BOOL)isAssociateScrollFloor {
//    return YES;
//}
- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *allFloorData = data[@"data"] ? : @{};
    
    ORDERListItemModel *order = [ORDERListItemModel yy_modelWithDictionary:allFloorData];
    NSMutableArray *sections = [NSMutableArray array];
    [sections addObject:[self updateOrderPriceSectionWith:order.orderAmount allFloorData:allFloorData]];
    self.sectionViewModels = [NSArray arrayWithArray:sections];
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel{
    NSDictionary *allFloorData = data[@"data"] ? : @{};
    
    JDISVOrderDetailOrderPriceFloorModel *model = [JDISVOrderDetailOrderPriceFloorModel yy_modelWithDictionary:allFloorData];
    NSMutableArray *sections = [NSMutableArray array];
    [sections addObject:[self updateOrderAcmountSectionWith:model.priceInfoModel allFloorData:allFloorData]];
    self.sectionViewModels = [NSArray arrayWithArray:sections];
}

#pragma mark - ViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.height = 0.f;
        self.sectionViewModels = [NSArray array];
    }
    return self;
}

- (NSArray *)updateOrderPriceSectionWith:(ORDERListItemAmountModel *)model allFloorData:(NSDictionary *)allFloorData {
    NSMutableArray *tempArray = [NSMutableArray array];
    
    // 商品总价格
    JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *totalAmount = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
    [totalAmount updateWithData:model forType:ORDERDetailPriceFloorTypeTotalAmount];
    [tempArray addObject:totalAmount];
    // 总运费
    JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *totalFreightAmount = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
    [totalFreightAmount updateWithData:model forType:ORDERDetailPriceFloorTypeTotalFreightAmount];
    [tempArray addObject:totalFreightAmount];
    // 优惠券
    JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *totalVoucherDiscount = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
    [totalVoucherDiscount updateWithData:model forType:ORDERDetailPriceFloorTypeTotalVoucherDiscount];
    [tempArray addObject:totalVoucherDiscount];
    
    // 促销
    JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *totalPromoDiscount = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
    [totalPromoDiscount updateWithData:model forType:ORDERDetailPriceFloorTypeTotalPromoDiscount];
    [tempArray addObject:totalPromoDiscount];
    
    // 运费抵扣
    JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *totalFreightDiscountAmount = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
    [totalFreightDiscountAmount updateWithData:model forType:ORDERDetailPriceFloorTypeTotalFreightDiscountAmount];
    [tempArray addObject:totalFreightDiscountAmount];
    
    // 消费券抵扣
    JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *totalConsumptionVoucherAmount = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
    [totalConsumptionVoucherAmount updateWithData:model forType:ORDERDetailPriceFloorTypeTotalConsumptionVoucherAmount];
    [tempArray addObject:totalConsumptionVoucherAmount];
    
    // 优惠码抵扣
    JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *totalCouponCodeDiscount = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
    [totalCouponCodeDiscount updateWithData:model forType:ORDERDetailPriceFloorTypeTotalCouponCodeDiscount];
    [tempArray addObject:totalCouponCodeDiscount];
    
    // 实付款
    JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel *shouldPayAmount = [[JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel alloc] init];
    [shouldPayAmount updateWithData:model forType:0];
    [tempArray addObject:shouldPayAmount];
    
    return [NSArray arrayWithArray:tempArray];
}

// 更新订单金额ViewModel
- (NSArray *)updateOrderAcmountSectionWith:(JDISVOrderDetailOrderPriceFloorPriceInfoModel *)model allFloorData:(NSDictionary *)allFloorData{
//    NSString *orderstatus = allFloorData[@"orderStatusDTO"][@"orderStatusId"] ? : @"";
    NSString *orderstate = allFloorData[@"orderStatusDTO"][@"state"] ? : @"";

    BOOL presellFlag = NO;
    BOOL groupBuyFlag = NO;
    NSString *sendpay = allFloorData[@"others"][@"sendPay"];
    if (sendpay.length > 44 && [sendpay characterAtIndex:43] == '1') {//预售
        presellFlag = YES;
    }else if (sendpay.length > 433 && [sendpay characterAtIndex:432] == '1'){//拼团
        groupBuyFlag = YES;
    }
    JDISVStatusFloorItemModel *itemModel;
    JDISVOrderProcessFloorModel *presellModel;
    if (presellFlag) {
        presellModel = [[JDISVOrderProcessFloorModel alloc]init];
        NSDictionary *Dtodic = allFloorData[@"presaleInfoDTO"];
        itemModel = [JDISVStatusFloorItemModel yy_modelWithDictionary:Dtodic];
        presellModel.earnest = itemModel.bargin;
        presellModel.endPayment = itemModel.balance;
        presellModel.expamount = itemModel.discountAmountContent;
        if (itemModel.state == 0 || itemModel.state == 1) {//待付定金
            presellModel.status = @"0";
        } else if(itemModel.state == 2 || itemModel.state == 3 || itemModel.state == 4){//已付定金，待付尾款
            presellModel.status = @"1";
        } else if(itemModel.state == 5 || itemModel.state == 7){//已付尾款
            presellModel.status = @"2";
        } else if(itemModel.state == 121 || orderstate.integerValue < 104){//未付完定金订单取消状态
            presellModel.status = @"3";
        } else if(itemModel.state == 221 || orderstate.integerValue == 104){//未付完尾款订单取消状态
            presellModel.status = @"4";
        } else if(orderstate.integerValue > 104){//已付完尾款订单取消状态
            presellModel.status = @"5";
        }
        [presellModel uploadCellinfo];
    }
    
    NSMutableArray *tempArray = [NSMutableArray array];
    int index = 0;
    for (JDISVOrderDetailOrderPriceFloorPriceInfoItemModel *priceModel in model.priceItemInfoList) {
        NSLog(@"index == %d",index);
        if (index == 1 && presellModel) {
            [tempArray addObject:presellModel];
        }
     
        JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel *amountItemViewModel = [[JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel alloc] init];
        [amountItemViewModel updateWithData:priceModel forType:0];
        if (priceModel.type == 16){
            presellModel.earnest = priceModel.price;
        } else if (priceModel.type == 38){
            presellModel.endPayment = priceModel.price;
        } else {
            [tempArray addObject:amountItemViewModel];
        }
        index++;
    }
    
    [presellModel uploadCellinfo];
    
    NSLog(@"%d",index);
    
    JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel *actualAmountViewModel = [[JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel alloc] init];
    if ([presellModel.status isEqualToString:@"0"] || [presellModel.status isEqualToString:@"3"]) {
        //预售付定金 || 未付定金已取消
        model.message = OrderDetailL(@"isv_order_detail_title_order_need_bargin");
        model.price = itemModel.bargin;
    } else if ([presellModel.status isEqualToString:@"1"] || [presellModel.status isEqualToString:@"4"]) {
        //预售付尾款 || 未付尾款已取消
        model.message = OrderDetailL(@"isv_order_detail_title_order_need_end");
        model.price = itemModel.balance;
    }
    
    [actualAmountViewModel updateWithData:model forType:0];
    [tempArray addObject:actualAmountViewModel];
  
    return [NSArray arrayWithArray:tempArray];
}

- (CGFloat)height {
    CGFloat totalHeight = 0;
    if (_sectionViewModels && _sectionViewModels.count > 0) {
        for (NSArray *cellViewModels in _sectionViewModels) {
            if (cellViewModels && cellViewModels.count > 0) {
                for (JDISVOrderDetailOrderPriceFloorBaseCellViewModel *vm in cellViewModels) {
                    totalHeight += vm.height;
                }
            }
            totalHeight += 12.f*2;
        }
        totalHeight += 8.f * (_sectionViewModels.count - 1);
    }
    return totalHeight;
}

@end
