//
//  JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/3.
//

#import "JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel.h"

#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVOrderDetailOrderPriceFloorModel.h"
@import JDTCommonToolModule;

@interface JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel ()
@property (nonatomic, copy) NSString *title;
@end

@implementation JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVOrderDetailOrderPriceFloorAmountItemCell";
        self.cellIdentifier = @"JDISVOrderDetailOrderPriceFloorAmountItemCell";
        self.height = 30.f;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:[ORDERListItemAmountModel class]]) {
        ORDERListItemAmountModel *model = (ORDERListItemAmountModel *)data;
        switch (type) {
            case ORDERDetailPriceFloorTypeTotalAmount: {
                self.title = OrderDetailL(@"ka_order_productTotalAmount") ;
                NSMutableAttributedString *priceAttStr = [[NSMutableAttributedString alloc] init];
                [priceAttStr KA_renderWithPriceStr:model.totalAmount.stringValue unit:[NSString getJDCDPriceTag] zfSymbol:@"" type:KAPriceTypeP3 colorType:@"#C7"];
                self.amountAttributedText = priceAttStr;
                break;
            }
            case ORDERDetailPriceFloorTypeTotalFreightAmount: {
                if (!model.totalFreightAmount || [model.totalFreightAmount isEqualToNumber:@0]) {
                    self.height = 0;
                    self.titleAttributedText = nil;
                    self.amountAttributedText = nil;
                    return;
                }
                self.title = OrderDetailL(@"ka_order_freightAmount");
                NSMutableAttributedString *freightAttStr = [[NSMutableAttributedString alloc] init];
                [freightAttStr KA_renderWithPriceStr:model.totalFreightAmount.stringValue unit:[NSString getJDCDPriceTag]  zfSymbol:@"" type:KAPriceTypeP4 colorType:@"#C7"];
                self.amountAttributedText = freightAttStr;
                break;
            }
            case ORDERDetailPriceFloorTypeTotalVoucherDiscount: {
                self.amountAttributedText = [self getPriceAttributedString: model.totalVoucherDiscount title:OrderDetailL(@"ka_order_voucherAmount")];
                if(!self.amountAttributedText) {
                    return ;
                }
                
                break;
            }
                
            case ORDERDetailPriceFloorTypeTotalPromoDiscount:{
                self.amountAttributedText = [self getPriceAttributedString: model.totalPromoDiscount title: OrderDetailL(@"ka_order_promotAmount")];
                if(!self.amountAttributedText) {
                    return ;
                }
                
                break;
            }
                
            case ORDERDetailPriceFloorTypeTotalFreightDiscountAmount:{
                self.amountAttributedText = [self getPriceAttributedString: model.totalFreightDiscountAmount title: OrderDetailL(@"ka_order_freightDiscountAmount")];
                if(!self.amountAttributedText) {
                    return ;
                }
                
                break;
            }
                
            case ORDERDetailPriceFloorTypeTotalConsumptionVoucherAmount: {
                self.amountAttributedText = [self getPriceAttributedString: model.totalConsumptionVoucherAmount title:OrderDetailL(@"ka_order_consumptionVoucherAmount")];
                if(!self.amountAttributedText) {
                    return ;
                }
                
                break;
            }
                
            case ORDERDetailPriceFloorTypeTotalCouponCodeDiscount: {
                self.amountAttributedText = [self getPriceAttributedString: model.totalCouponCodeDiscount title:OrderDetailL(@"ka_order_couponCodeAmount")];
                if(!self.amountAttributedText) {
                    return ;
                }
                break;
            }
                
            default:
                break;
        }
        self.titleAttributedText = [self cellTitleAttributedStringWith:self.title];
        CGSize valueSize = [self.amountAttributedText.string jdcd_sizeWithContainer:CGSizeMake(MAXFLOAT, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0];
        CGFloat titleHeight = [self.titleAttributedText.string jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2) - 36.f - 18.f - valueSize.width, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0].height + 12.f;
        self.height = MAX(titleHeight, valueSize.height + 12.f);
    } else {
        self.height = 0;
        self.titleAttributedText = nil;
        self.amountAttributedText = nil;
    }
}

- (NSMutableAttributedString * __nullable) getPriceAttributedString:(NSNumber * __nullable) price  title:(NSString *)title {
    if (!price || [price isEqualToNumber:@0]) {
        self.height = 0;
        self.titleAttributedText = nil;
        self.amountAttributedText = nil;
        return nil;
    }
    self.title = title;
    NSMutableAttributedString *priceAttStr = [[NSMutableAttributedString alloc] init];
    [priceAttStr KA_renderWithPriceStr:price.stringValue unit:[NSString getJDCDPriceTag]  zfSymbol:@"-" type:KAPriceTypeP4 colorType:@"#C9"];
    return priceAttStr;
}

- (void)updateWithData2:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVOrderDetailOrderPriceFloorPriceInfoItemModel.class]) {
        JDISVOrderDetailOrderPriceFloorPriceInfoItemModel *model = (JDISVOrderDetailOrderPriceFloorPriceInfoItemModel *)data;
        if ([model.title jdcd_validateString]) {
            self.title = model.title;
        } else {
            self.height = 0;
            return;
        }
        
        NSString *zfStr = @"";
        if ([model.zfMark jdcd_validateString]) {
            zfStr = [model.zfMark copy];
        }
        
        NSString *unit = @"";
        if ([model.unit jdcd_validateString]) {
            unit = [model.unit copy];
        }
        
        NSString *price = @"-";
        if ([model.price jdcd_validateString]) {
            if ([model.price containsString:@"."]) {
                price = model.price;
            } else {
                price = [NSString stringWithFormat:@"%@.00",model.price];
            }
        }
        
        self.titleAttributedText = [self cellTitleAttributedStringWith:_title];
        NSMutableAttributedString *priceMutableAtrributedString = [[NSMutableAttributedString alloc] initWithString:@""];
        if ([zfStr containsString:@"-"]) {
            // 红色 #C9
            [priceMutableAtrributedString KA_renderWithPriceStr:price unit:unit zfSymbol:zfStr type:KAPriceTypeP4 colorType:@"#C9"];
        } else {
            // 黑色 #C7
            [priceMutableAtrributedString KA_renderWithPriceStr:price unit:unit zfSymbol:zfStr type:KAPriceTypeP4 colorType:@"#C7"];
        }
        self.amountAttributedText = [priceMutableAtrributedString copy];
        
        CGSize valueSize = [self.amountAttributedText.string jdcd_sizeWithContainer:CGSizeMake(MAXFLOAT, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0];
        CGFloat titleHeight = [self.titleAttributedText.string jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2) - 36.f - 18.f - valueSize.width, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0].height + 12.f;
        self.height = MAX(titleHeight, valueSize.height + 12.f);
        
    } else {
        self.height = 0;
        self.titleAttributedText = nil;
        self.amountAttributedText = nil;
        return;
    }
    
}

- (NSAttributedString *)cellTitleAttributedStringWith:(NSString * _Nonnull)title {
    return [[NSAttributedString alloc] initWithString:title attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"], NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
}
@end
