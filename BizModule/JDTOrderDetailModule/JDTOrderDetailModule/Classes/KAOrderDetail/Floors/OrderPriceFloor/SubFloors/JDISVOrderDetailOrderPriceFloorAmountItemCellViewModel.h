//
//  JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel.h
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/3.
//

#import "JDISVOrderDetailOrderPriceFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, ORDERDetailPriceFloorType) {
    ORDERDetailPriceFloorTypeTotalAmount,           // 商品总金额
    ORDERDetailPriceFloorTypeTotalFreightAmount,    // 运费
    ORDERDetailPriceFloorTypeTotalVoucherDiscount,  // 优惠券抵扣金额
    ORDERDetailPriceFloorTypeTotalPromoDiscount,    // 总促销优惠
    ORDERDetailPriceFloorTypeTotalFreightDiscountAmount,   // 运费抵扣
    ORDERDetailPriceFloorTypeTotalConsumptionVoucherAmount,   // 消费券抵扣
    ORDERDetailPriceFloorTypeTotalCouponCodeDiscount,   // 优惠码抵扣
};

@interface JDISVOrderDetailOrderPriceFloorAmountItemCellViewModel : JDISVOrderDetailOrderPriceFloorBaseCellViewModel

@property (nonatomic, copy, nullable) NSAttributedString *titleAttributedText;

@property (nonatomic, copy, nullable) NSAttributedString *amountAttributedText;

@end

NS_ASSUME_NONNULL_END
