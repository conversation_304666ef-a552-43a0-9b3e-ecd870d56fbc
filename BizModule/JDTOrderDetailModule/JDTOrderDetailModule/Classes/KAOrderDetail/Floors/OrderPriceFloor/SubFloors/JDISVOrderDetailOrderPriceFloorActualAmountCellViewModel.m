//
//  JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/3.
//

#import "JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

#import "JDISVOrderDetailOrderPriceFloorModel.h"
@import JDTCommonToolModule;

@interface JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel ()
@property (nonatomic, copy) NSString *title;
@end

@implementation JDISVOrderDetailOrderPriceFloorActualAmountCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVOrderDetailOrderPriceFloorActualAmountCell";
        self.cellIdentifier = @"JDISVOrderDetailOrderPriceFloorActualAmountCell";
        self.height = 30.f;
        
        self.title = @"";
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:[ORDERListItemAmountModel class]]) {
        ORDERListItemAmountModel *model = (ORDERListItemAmountModel *)data;
        self.title = OrderDetailL(@"ka_order_payAmount") ;
        self.titleAttributedString = [[NSAttributedString alloc] initWithString:self.title attributes:@{
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6"]
        }];
        NSMutableAttributedString *actualMoneyMutableAtrributedString = [[NSMutableAttributedString alloc] init];
        [actualMoneyMutableAtrributedString KA_renderWithPriceStr:model.shouldPayAmount.stringValue unit: [NSString getJDCDPriceTag] zfSymbol:@"" type:KAPriceTypeP3 colorType:@"#C9"];
        self.actualMoneyAttributedString = [actualMoneyMutableAtrributedString copy];
    } else {
        self.height = 0;
    }
}

- (void)updateWithData2:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVOrderDetailOrderPriceFloorPriceInfoModel.class]) {
        JDISVOrderDetailOrderPriceFloorPriceInfoModel *model = (JDISVOrderDetailOrderPriceFloorPriceInfoModel *)data;
        if ([model.message jdcd_validateString]) {
            self.title = model.message;
        } else {
            self.height = 0;
            return;
        }
        NSString *price = @"-";
        if ([model.price jdcd_validateString]) {
            if ([model.price containsString:@"."]) {
                price = [model.price copy];
            } else {
                price = [NSString stringWithFormat:@"%@.00", model.price ? : @""];
            }
        }
        NSString *unit = @"";
        if ([model.unit jdcd_validateString]) {
            unit = [model.unit copy];
        }
        NSMutableAttributedString *actualMoneyMutableAtrributedString = [[NSMutableAttributedString alloc] initWithString:@""];
        [actualMoneyMutableAtrributedString KA_renderWithPriceStr:price unit:unit zfSymbol:@"" type:KAPriceTypeP3 colorType:@"#C9"];
        self.actualMoneyAttributedString = [actualMoneyMutableAtrributedString copy];
        
        if ([self.title jdcd_validateString]) {
            self.titleAttributedString = [[NSAttributedString alloc] initWithString:_title attributes:@{
                NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6"]
            }];
        } else {
            self.titleAttributedString = nil;
        }
    } else {
        self.height = 0;
    }

}
@end
