//
//  JDISVOrderDetailOrderActionFloor.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/3.
//

#import "JDISVOrderDetailOrderActionFloor.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>

#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import "JDISVOrderDetailOrderActionFloorModule.h"
#import "JDISVOrderDetailOrderActionFloorButtonViewModel.h"
#import "JDISVOrderDetailOrderActionFloorModel.h"
#import "JDISVOrderDetailModuleMacro.h"
#import "JDISVOrderDetailFixController.h"
@import JDTCommonToolModule;

@import JDTInfrastructureModule;

JDCDISVActionType const kJDISVOrderDetailOrderActionFloorPayAction = @"JDISVOrderDetailOrderActionFloorPayAction"; /**< 立即支付 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorQueryLogisticsAction = @"JDISVOrderDetailOrderActionFloorQueryLogisticsAction"; /**< 查询物流 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorDeleteOrderAction = @"JDISVOrderDetailOrderActionFloorDeleteOrderAction"; /**< 删除订单 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorConfirmToReceiveAction = @"JDISVOrderDetailOrderActionFloorConfirmToReceiveAction"; /**< 确认收货 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorCancelOrderAction = @"JDISVOrderDetailOrderActionFloorCancelOrderAction"; /**< 取消订单 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorBuyAgainAction = @"JDISVOrderDetailOrderActionFloorBuyAgainAction"; /**< 再次购买 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorEvaluateAction = @"JDISVOrderDetailOrderActionFloorEvaluateAction"; /**< 评价 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorApplyAfterSale = @"kJDISVOrderDetailOrderActionFloorApplyAfterSale"; /**< 申请售后 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorShareFriend = @"kJDISVOrderDetailOrderActionFloorShareFriend"; /**< 邀请好友 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorFirstMoneyAction = @"kJDISVOrderDetailOrderActionFloorFirstMoneyAction"; /**< 支付定金 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorLastMoneyAction = @"kJDISVOrderDetailOrderActionFloorLastMoney"; /**< 支付尾款 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorDelayReceiveAction = @"kJDISVOrderDetailOrderActionFloorDelayReceiveAction"; /**< 延迟收货 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorApplyInvoiceAction = @"kJDISVOrderDetailOrderActionFloorApplyInvoiceAction"; /**< 申请开票 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorCheckInvoiceAction = @"kJDISVOrderDetailOrderActionFloorCheckInvoiceAction"; /**< 查看开票 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorModifyOrderAction = @"kJDISVOrderDetailOrderActionFloorModifyOrderAction"; /**< 修改订单 */

// 外部处理回调
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorDeleteOrderActionBlock = @"JDISVOrderDetailOrderActionFloorDeleteOrderActionBlock"; /**< 删除订单外部回调 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorConfirmToReceiveActionBlock = @"JDISVOrderDetailOrderActionFloorConfirmToReceiveActionBlock"; /**< 确认收货外部回调 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorCancelOrderActionBlock = @"JDISVOrderDetailOrderActionFloorCancelOrderActionBlock"; /**< 取消订单外部回调 */
JDCDISVActionType const kJDISVOrderDetailOrderActionFloorEvaluateActionBlock = @"JDISVOrderDetailOrderActionFloorEvaluateActionBlock"; /**< 评价外部回调 */

JDCDISVActionType const kJDISVOrderDetailOrderActionFloorLoadDataActionBlock = @"JDISVOrderDetailOrderActionFloorLoadDataActionBlock"; /**< 主控制器请求数据 */

@interface JDISVOrderDetailOrderActionFloor ()<JDCDISVActionTransferProtocol>
@property (nonatomic, strong) KAOrderDetailBottomBar *orderBottomBar;
@property (nonatomic, strong) JDISVOrderDetailOrderActionFloorModule *viewModel;
@property (nonatomic,strong) UIButton *paymentBtn;
@property (nonatomic,assign) BOOL isCanPay;
@property (nonatomic,assign) BOOL isFirstGetNotification;
@end

@implementation JDISVOrderDetailOrderActionFloor

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setup];
    }
    return self;
}

- (void)setup {
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.orderBottomBar = [[KAOrderDetailBottomBar alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), self.viewModel.bottomHeight)];
    self.orderBottomBar.backgroundColor = [UIColor clearColor];
    [self addSubview:self.orderBottomBar];
    [self.orderBottomBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(0);
        make.trailing.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
    }];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateBtnStyle:) name:@"updateBtnStyle" object:nil];
    self.isCanPay = NO;
    
    
}
- (void)updateBtnStyle:(NSNotification *)fication{
    if ([fication.object boolValue] == self.isCanPay && self.isFirstGetNotification) {
        return;
    }
    self.isFirstGetNotification = YES;
    if ([fication.object boolValue]) {
        self.isCanPay = YES;
    }else{
        self.isCanPay = NO;
    }
    
    NSMutableArray *buttonTitles = [NSMutableArray array];
    NSMutableArray *buttonStyles = [NSMutableArray array];
    for (JDISVOrderDetailOrderActionFloorButtonViewModel *viewModel in self.viewModel.buttonViewModels) {
        if (viewModel.buttonTitle) {
            [buttonTitles addObject:viewModel.buttonTitle];
            if (viewModel.styleType == JDISVOrderDetailOrderActionFloorButtonViewModelStyleTypeGray) {
                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB4)];
            } else {
                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB3)];
            }
//            if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePayLastMoney) {//支付尾款
//                if (self.isCanPay == NO) {
//                    [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleC4)];
//                }else{
//                    [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB3)];
//                }
//            }else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeWaitPayLastMoney){
//                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleC4)];
//            }
//            else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeShareFirend) {
//                
//                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB3)];
//            }else if (viewModel.styleType == JDISVOrderDetailOrderActionFloorButtonViewModelStyleTypeGray) {
//                // B4
//                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB4)];
//            } else {
//                // B3
//                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB3)];
//            }
        }
    }
    
    
    [self.orderBottomBar updateWithButtonTitles:[NSArray arrayWithArray:buttonTitles] styles:[NSArray arrayWithArray:buttonStyles]];
 
}
- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    self.isFirstGetNotification = NO;
    NSMutableArray *buttonTitles = [NSMutableArray array];
    NSMutableArray *buttonStyles = [NSMutableArray array];
    for (JDISVOrderDetailOrderActionFloorButtonViewModel *viewModel in self.viewModel.buttonViewModels) {
        if (viewModel.buttonTitle) {
           
            [buttonTitles addObject:viewModel.buttonTitle];

            if (viewModel.styleType == JDISVOrderDetailOrderActionFloorButtonViewModelStyleTypeGray) {
                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB4)];
            } else {
                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB3)];
            }
//            if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeShareFirend) {
//
//                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB3)];
//            }else if (viewModel.styleType == JDISVOrderDetailOrderActionFloorButtonViewModelStyleTypeGray) {
//                // B4
//                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB4)];
//            } else {
//                // B3
//                [buttonStyles addObject:@(KAOrderDetailBottomBarButtonStyleB3)];
//            }
        }
    }
    
    
    [self.orderBottomBar updateWithButtonTitles:[NSArray arrayWithArray:buttonTitles] styles:[NSArray arrayWithArray:buttonStyles]];
    @weakify(self)
    self.orderBottomBar.buttonActionBlocks = ^(NSInteger tapIndex) {
        @strongify(self)
        if (tapIndex < self.viewModel.buttonViewModels.count) {
            JDISVOrderDetailOrderActionFloorButtonViewModel *viewModel = [self.viewModel.buttonViewModels objectAtIndex:tapIndex];
            [self processBottomActionWith:viewModel];
        }
        
    };
}

- (void)processBottomActionWith:(JDISVOrderDetailOrderActionFloorButtonViewModel *)viewModel {
    if (!viewModel) return;
    if (viewModel.canJump) {

    } else {
        switch (viewModel.button.showLabelId) {
            case ORDERListItemButtonTypeToPay: {
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorPayAction value:self.viewModel.orderId];
                break;
            }
            case ORDERListItemButtonTypeQueryLogistics: {
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorQueryLogisticsAction value:self.viewModel.shipmentOrder];
                break;
            }
            case ORDERListItemButtonTypeConfirmReceipt: {
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorConfirmToReceiveAction value:self.viewModel.orderId];
                break;
            }
            case ORDERListItemButtonTypeCancelOrder: {
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorCancelOrderAction value:self.viewModel.orderId];
                break;
            }
            case ORDERListItemButtonTypeBuyAgain: {
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorBuyAgainAction value:self.viewModel.productModels];
                break;
            }
            case ORDERListItemButtonTypeApplyAfterSale: {
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorApplyAfterSale value:self.viewModel.orderId];
                break;
            }
            case ORDERListItemButtonTypeIssueInvoice: {
                // TODO:Juice H5实现
                break;
            }
            default:
                break;
        }
//        if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePay) {
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorPayAction value:self.viewModel.orderId];
//        }else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePayFirstMoney){
//            // 支付定金
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorFirstMoneyAction value:self.viewModel.orderId];
//        }else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePayLastMoney){
//            // 支付尾款
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorLastMoneyAction value:self.viewModel.orderId];
//        } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeDeleteOrder) {
//            // 删除订单
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorDeleteOrderAction value:self.viewModel.orderId];
//        } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeConfirmReceive) {
//            // 确认收货
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorConfirmToReceiveAction value:self.viewModel.orderId];
//        } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeCancelOrder) {
//            // 取消订单
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorCancelOrderAction value:self.viewModel.orderId];
//        } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeBuyAgain) {
//            // 再次购买
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorBuyAgainAction value:[self.viewModel productModels]];
//        } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeEvaluate) {
//            // 评价
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorEvaluateAction value:self.viewModel.orderId];
//        } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeApplyAfterSale) {
//            // 申请售后
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorApplyAfterSale value:self.viewModel.orderId];
//        } else if(viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeShareFirend){
//            //邀请好友
//            [self sendActionWith:kJDISVOrderDetailOrderActionFloorShareFriend value:self.viewModel.orderId];
//        }
////        else if(viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePayLastMoney){
////            //支付尾款
////           [self sendActionWith:kJDISVOrderDetailOrderActionFloorLastMoneyAction value:self.viewModel.orderId];
////       }
//        else if(viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeDelayReceive){
//           //延迟收货
//          [self sendActionWith:kJDISVOrderDetailOrderActionFloorDelayReceiveAction value:self.viewModel.orderId];
//       }else if(viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeApplyInvoice){
//           //申请开票
//          [self sendActionWith:kJDISVOrderDetailOrderActionFloorApplyInvoiceAction value:self.viewModel.orderId];
//       } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeCheckInvoice) {
//           //查看发票
//           [self sendActionWith:kJDISVOrderDetailOrderActionFloorCheckInvoiceAction value:viewModel.applyNo];
//       } else if (viewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeModifyOrder) {
//           //修改订单
//           [self sendActionWith:kJDISVOrderDetailOrderActionFloorModifyOrderAction value:self.viewModel.orderId];
//       }
    }
}

- (void)sendActionWith:(NSString *)actionType value:(id _Nullable)value{
    JDCDISVAction *action = [JDCDISVAction actionWithType:actionType];
    action.value = value;
    [self isv_sendAction:action];

}

#pragma mark - JDCDISVActionTransferProtocol
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorPayAction]) {
        // 立即支付
        NSString *orderId = (NSString *)action.value;
        [self payActionWith:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorQueryLogisticsAction]) {
        // 查询物流
        NSArray *shipmentOrder = (NSArray *)action.value;
        [self queryLogisticsActionWith:shipmentOrder controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorDeleteOrderAction]) {
        // 删除订单
        NSString *orderId = (NSString *)action.value;
        [self deleteOrderActionWith:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorConfirmToReceiveAction]) {
        // 确认收货
        NSString *orderId = (NSString *)action.value;
        [self confirmToReceiveActionWith:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorCancelOrderAction]) {
        // 取消订单
        NSString *orderId = (NSString *)action.value;
        [self cancelOrderAction:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorBuyAgainAction]) {
        // 再次购买
        NSArray <ORDERListItemSkuDetailModel *> *products = (NSArray *)action.value;
        [self buyAgainAction:products controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorEvaluateAction]) {
        // 评价
        NSString *orderId = (NSString *)action.value;
        [self evaluateActionWithOrderId:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorApplyAfterSale]) {
        // 申请售后
        [self applyAfterSaleActionWithController:controller];
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorShareFriend]) {
        // 邀请好友
        NSString *orderId = (NSString *)action.value;
        [self inviteFriendsActioWithOrderId:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorFirstMoneyAction]) {
        // 支付定金
        NSString *orderId = (NSString *)action.value;
        [self payFirstMoneyActioWithOrderId:orderId controller:controller];
        return YES;
    }  else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorLastMoneyAction]) {
        // 支付尾款
        NSString *orderId = (NSString *)action.value;
        [self payLastMoneyActioWithOrderId:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorDelayReceiveAction]) {
        // 延迟收货
        NSString *orderId = (NSString *)action.value;
        [self delayReceiveActioWithOrderId:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorApplyInvoiceAction]) {
        // 申请开票
        NSString *orderId = (NSString *)action.value;
        [self applyInvoiceActioWithOrderId:orderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorCheckInvoiceAction]) {
        // 查看发票
        NSString *applyNo = (NSString *)action.value;
        [self checkInvoiceActioWithOrderId:applyNo controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailOrderActionFloorModifyOrderAction]) {
        // 修改订单
        NSString *orderId = (NSString *)action.value;
        [self modifyOrderActioWithOrderId:orderId controller:controller];
        return YES;
    }
    
    return NO;
}

- (void)broadcastAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDCDISVFloorScrollAction]) {
        // 监听滚动
        [self.orderBottomBar hideMoreButtons];
    } else if ([action.actionType isEqualToString:@"kJDISVOrderDetailOrderTrackFloorActionTrackOrderModifyOrderAddress"]){
        //修改订单
        [self sendActionWith:kJDISVOrderDetailOrderActionFloorModifyOrderAction value:self.viewModel.orderId];
    }
}

#pragma - mark - Actions
// 查询物流
- (void)queryLogisticsActionWith:(NSArray <ORDERListItemShipmentOrderModel *> *)shipmentOrderArr controller:(UIViewController *)controller {
    UIViewController *vc = [JDRouter openURL:@"router://JDISVOrderListSDKModule/getOrderTrackViewController" arg:@{@"trackDataList": shipmentOrderArr} error:nil completion:nil];
    KAFloatLayerPresentationController *floatVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:vc presentingViewController:controller];
    floatVC.contentHeight = 0.68 * [[UIScreen mainScreen] bounds].size.height;
    vc.transitioningDelegate = floatVC;
    [controller presentViewController:vc animated:YES completion:nil];
}

// 立即支付
- (void)payActionWith:(NSString *)orderId controller:(UIViewController *)controller {
    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_button_pay_request_failed")];
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    [param setObject:@(3) forKey:@"sourceType"];//获取H5支付链接传3
    @weakify(self)
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"" function:@"getPayUrl" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self)
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_button_pay_request_failed")];
            NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
            NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
            [JDRouter openURL:url arg:@{@"name":@"GetPaymentUrlError"} error:nil completion:nil];//获取支付链接失败埋点
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            if ([code isEqual:@0]) {
                NSDictionary *dataDic = [responseObject objectForKey:@"value"];
                NSString *jsonstr = [dataDic objectForKey:@"url"];
                NSDictionary *jsonDic = [JDISVOrderDetailOrderActionFloor dictionaryWithJsonString:jsonstr];
                NSString *payUrl = [jsonDic objectForKey:@"httpUrl"];
                if ([payUrl jdcd_validateString]) {
                    //跳转收银台
                    NSDictionary* param = @{@"text_url":payUrl};
                    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
                    NSString* routerStr = [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
                    [JDRouter openURL:routerStr arg:param error:nil completion:^(id obj){
                        [controller.navigationController pushViewController:obj animated:YES];
                    }];
                }
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_button_pay_request_failed")];
                NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
                NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
                [JDRouter openURL:url arg:@{@"name":@"GetPaymentUrlError"} error:nil completion:nil];//获取支付链接失败埋点
            }
        }
    }];
}

- (void)deleteOrderActionWith:(NSString *)orderId controller:(UIViewController *)controller{
    // 移除订单
    @weakify(self)
    [KAAlert alert].config.renderW4Alignment(OrderDetailL(@"ka_order_dialog_delete_order_title"), OrderDetailL(@"ka_order_dialog_delete_order_content"), NSTextAlignmentCenter).ka_addAction(OrderDetailL(@"ka_order_dialog_btn_cancel"), ^(UIButton * _Nonnull button) {
        [button renderB5];
    }, ^{
        // 取消
    })
    .ka_addAction(OrderDetailL(@"isv_order_delete_comfirm"), ^(UIButton * _Nonnull button) {
        [button renderB3];
    }, ^{
        // 删除
        @strongify(self)
        [PlatformService showLoadingInView:controller.view];
        [self removeOrderInfoWithOrderId:orderId block:^(BOOL result) {
            @strongify(self)
            [PlatformService dismissInView:controller.view];
            if (result) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:OrderDetailL(@"ka_order_button_delete_request_success")];
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorDeleteOrderActionBlock value:orderId]; // 向主控制器发送触发外部回调
                [controller.navigationController popViewControllerAnimated:YES];
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_button_delete_request_failed")];
            }
        }];
        
    }).jdcd_show();
}

- (void)confirmToReceiveActionWith:(NSString *)orderId controller:(UIViewController *)controller{
    // 确认收货
    @weakify(self)
    [KAAlert alert].config.renderW3(OrderDetailL(@"isv_order_comfirm_receipt_des")).ka_addAction(OrderDetailL(@"ka_order_dialog_btn_cancel"), ^(UIButton * _Nonnull button) {
        [button renderB5];
    }, ^{
        // 取消
    })
    .ka_addAction(OrderDetailL(@"isv_order_confirm_receipt"), ^(UIButton * _Nonnull button) {
        [button renderB3];
    }, ^{
        @strongify(self)
        [PlatformService showLoadingInView:controller.view];
        [self comfirmOrderInfoWithOrderId:orderId block:^(BOOL result) {
            if (result) {
                @strongify(self)
                [PlatformService dismissInView:controller.view];
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:OrderDetailL(@"isv_order_comfirm_receipt_end")];
                
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorLoadDataActionBlock value:orderId]; // 主控制器请求数据
                [self sendActionWith:kJDISVOrderDetailOrderActionFloorConfirmToReceiveActionBlock value:orderId]; // 触发主控器外部回调
            } else {
                [PlatformService dismissInView:controller.view];
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_button_ensure_receive_request_failed")];
            }
        }];
        
    }).jdcd_show();
}

- (void)cancelOrderAction:(NSString *)orderId controller:(UIViewController *)controller {
    [self requestCancelReasonsComplete:^(ORDERDetailEnumsModel *model, NSError *error) {
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_button_cancel_request_failed")];
        } else {
            UIViewController *vc = [JDRouter openURL:@"router://JDISVOrderDetailSDKModule/selectCancelReasonController" arg:@{@"cancelReasons": model.CancelReasonEnum} error:nil completion:^(id  _Nullable object) {
                if ([object isKindOfClass:[ORDERDetailEnumItemModel class]]) {
                    ORDERDetailEnumItemModel *reason = object;
                    [self cancelOrderNew:orderId cancelReasonId:reason.code complete:^(BOOL success) {
                        if (success) {
                            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:OrderDetailL(@"ka_order_cancel_order_cancelling")];
                            [self sendActionWith:kJDISVOrderDetailOrderActionFloorLoadDataActionBlock value:orderId]; // 主控制器请求数据
                            [self sendActionWith:kJDISVOrderDetailOrderActionFloorCancelOrderActionBlock value:orderId]; // 触发主控制器外部回调
                        } else {
                            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_button_cancel_request_failed")];
                        }
                    }];
                }
            }];
            KAFloatLayerPresentationController *floatVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:vc presentingViewController:controller];
            floatVC.contentHeight = 0.68 * [[UIScreen mainScreen] bounds].size.height;
            floatVC.defultHeader.titleLabel.text = OrderDetailL(@"ka_order_cancel_order_title");
            vc.transitioningDelegate = floatVC;
            [controller presentViewController:vc animated:YES completion:nil];
            
            // 取消
//            @weakify(self)
//            [KAAlert alert].config
//                .renderW3a(OrderDetailL(@"ka_order_dialog_cancel_order_title"), NSTextAlignmentCenter)
//                .ka_addAction(OrderDetailL(@"ka_order_dialog_btn_cancel"), ^(UIButton * _Nonnull button) {
//                [button renderB5];
//            }, ^{
//                // 取消
//            })
//            .ka_addAction(OrderDetailL(@"isv_order_comfirm_btn_text"), ^(UIButton * _Nonnull button) {
//                [button renderB3];
//            }, ^{
//               
//                @strongify(self)
//        //        [KAToast alert].config
//        //        .renderW6(OrderDetailL(@"ka_order_cancel_order_cancelling"))
//        //        .alertShow();
//                [self cancelOrderInfoWithOrderId:orderId block:^(BOOL result, NSString *showMessage) {
//                    if (result) {//取消成功
//                        @strongify(self)
//                        [JDCDAlert closeWithCompletionBlock:^{
//                            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:showMessage ? : OrderDetailL(@"ka_order_cancel_order_cancelling")];
//                            [self sendActionWith:kJDISVOrderDetailOrderActionFloorLoadDataActionBlock value:orderId]; // 主控制器请求数据
//                            [self sendActionWith:kJDISVOrderDetailOrderActionFloorCancelOrderActionBlock value:orderId]; // 触发主控器外部回调
//                        }];
//                    } else {//取消失败
//        //                @strongify(self)
//                        [JDCDAlert closeWithCompletionBlock:^{
//                            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:showMessage ? : OrderDetailL(@"ka_order_button_cancel_request_failed")];
//                        }];
//                    }
//                }];
//                
//            }).jdcd_show();
        }
    }];
}

- (void)requestCancelReasonsComplete:(void (^)(ORDERDetailEnumsModel *model, NSError *error))completeBlock {
    [[OOPAdvancedNetworkManager sharedAdvancedManager] GET:@"order/enums?apiCode=b2c.cbff.order.enums" parameters:@{} headers:nil useCache:YES completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            completeBlock(nil, error);
        } else {
            ORDERDetailEnumsModel *model = [ORDERDetailEnumsModel yy_modelWithDictionary:responseObject[@"data"]];
            completeBlock(model, nil);
        }
    }];
}

- (void)buyAgainAction:(NSArray <ORDERListItemSkuDetailModel *> *)productModels controller:(UIViewController *)controller {
//    @weakify(self)
    [PlatformService showLoadingInView:controller.view];
    NSString *shoppingCart = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)];
    // 反选
    NSString *unselect = [NSString stringWithFormat:@"router://%@/unSelectAll", shoppingCart];
    [JDRouter openURL:unselect arg:nil error:nil completion:nil];
    // 加车
    NSString *addCart = [NSString stringWithFormat:@"router://%@/addCart", shoppingCart];
    NSMutableArray *items = [NSMutableArray array];
    for (ORDERListItemSkuDetailModel *item in productModels) {
        if (!item.skuId) {
            continue;
        }
        NSInteger count = [item.num integerValue];
        if (count < 1) {
            count = 1;
        }
        NSMutableDictionary *temp = [NSMutableDictionary dictionary];
        [temp setObject:item.skuId forKey:@"skuId"];
        [temp setObject:@(count) forKey:@"num"];
        [temp setObject:@(1) forKey:@"itemType"];
        [items addObject:[temp copy]];
    }
//    for (JDISVOrderDetailOrderActionFloorStoreModelProductModel *item in productModels) {
//        if (!item.productId) {
//            continue;
//        }
//        NSInteger count = [item.buyCount integerValue];
//        if (count < 1) {
//            count = 1;
//        }
//        NSMutableDictionary *temp = [NSMutableDictionary dictionary];
//        [temp setObject:item.productId forKey:@"skuId"];
//        [temp setObject:@(count) forKey:@"num"];
//        [temp setObject:@(1) forKey:@"itemType"];
//        [items addObject:[temp copy]];
//    }
    if (items.count == 0) {
        return;
    }
    [JDRouter openURL:addCart arg:@{@"operations": @{@"products": [items copy]}} error:nil completion:^(NSDictionary * _Nullable object) {
//        @strongify(self)
        [PlatformService dismissInView:controller.view];
        NSString *code = [object objectForKey:@"code"];
        if ([code isEqual:@"0"]) {
            // 跳转购物车
            NSString *show = [NSString stringWithFormat:@"router://%@/shoppingCartController", shoppingCart];
            id vc = [JDRouter openURL:show arg:nil error:nil completion:nil];
            [controller.navigationController pushViewController:vc animated:YES];
        } else {
            NSString *msg = [object objectForKey:@"message"];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:msg ?: OrderDetailL(@"ka_order_button_buy_again_failed")];
        }
    }];
}


- (void)evaluateActionWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller {
    // 订单评价
    NSString *orderIdStr = orderIdParam;
    if (!orderIdStr) {
        return;
    }
    NSNumber *orderId = @(orderIdStr.integerValue);
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeCommentCenter)];

    if(self.viewModel.productModels.count>1){
        NSString *router = [NSString stringWithFormat:@"router://%@/getReviewTransitionViewController", moduleName];
        [JDRouter openURL:router arg:@{@"orderId": orderId} error:nil completion:^(NSDictionary *object) {
            UIViewController *evaluteController = [object objectForKey:@"viewController"];
            if (evaluteController) {
                [controller.navigationController pushViewController:evaluteController animated:YES];
            }
            NSDictionary *info = [object objectForKey:@"info"];
            if (info) {
                NSNumber *saved = [info objectForKey:@"saveComment"];
                if ([saved boolValue]) {
                    
                    [self sendActionWith:kJDISVOrderDetailOrderActionFloorLoadDataActionBlock value:orderId]; // 主控制器请求数据
                    [self sendActionWith:kJDISVOrderDetailOrderActionFloorEvaluateActionBlock value:orderId]; // 触发主控器外部回调
                }
            }
            
        }];
    }else{//直接跳转评价页面
        NSString *router = [NSString stringWithFormat:@"router://%@/commentDetailViewController", moduleName];
//        JDISVOrderDetailOrderActionFloorStoreModelProductModel * productModel =  self.viewModel.productModels.firstObject;
        ORDERListItemSkuDetailModel *sku = self.viewModel.productModels.firstObject;
        [JDRouter openURL:router arg:@{@"orderId": orderIdStr,@"skuId" :sku.skuId} error:nil completion:^(NSDictionary *object) {
            UIViewController *vc = [object objectForKey:@"viewController"];
            if (vc) {
                [controller.navigationController pushViewController:vc animated:YES];
            }
            NSDictionary *info = [object objectForKey:@"info"];
            if (info) {
                NSNumber *saved = [info objectForKey:@"success"];
                if ([saved boolValue]) {
                    
                    [self sendActionWith:kJDISVOrderDetailOrderActionFloorLoadDataActionBlock value:orderId]; // 主控制器请求数据
                    [self sendActionWith:kJDISVOrderDetailOrderActionFloorEvaluateActionBlock value:orderId]; // 触发主控器外部回调
                }
            }
        }];
    }
    
}

- (void)applyAfterSaleActionWithController:(UIViewController *)controller {
    // 申请售后
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/getAfterSaleApplyListViewController",[[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeAfterSales]] arg:nil error:nil completion:^(UIViewController * viewController) {
        if(viewController){
            viewController.hidesBottomBarWhenPushed = true;
            [controller.navigationController pushViewController:viewController animated:YES];
        }
    }];
}

- (void)inviteFriendsActioWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller{
    //邀请好友
    NSString* shareModel = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:JDISVModuleTypeShare];
    NSString* router = [NSString stringWithFormat:@"router://%@/showShareController",shareModel];
    [JDRouter openURL:router arg:[self.viewModel.sharedParams copy] error:nil completion:nil];
}

- (void)payFirstMoneyActioWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller{
    //支付定金
    NSString *orderId = orderIdParam;
    if (![orderId jdcd_validateString]) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    @weakify(self)
    [PlatformService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"marketing_presale_earnest_pay" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self)
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            NSNumber *success = [responseObject objectForKey:@"success"];
            NSString *message = [responseObject objectForKey:@"message"];
            if (success.boolValue) {
                NSDictionary *dataDic = (NSDictionary *)[responseObject objectForKey:@"data"] ? : @{};
                NSString *jsonstr = [dataDic objectForKey:@"returnUrl"];
                NSDictionary *jsonDic = [JDISVOrderDetailOrderActionFloor dictionaryWithJsonString:jsonstr];
                NSString *payUrl = [jsonDic objectForKey:@"httpUrl"];
                if ([payUrl jdcd_validateString]) {
                    NSDictionary* param = @{@"text_url":payUrl ? : @""};
                    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
                    NSString* routerStr = [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
                    [JDRouter openURL:routerStr arg:param error:nil completion:^(id obj){
                        [controller.navigationController pushViewController:obj animated:YES];
                    }];
                }else{
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:[message jdcd_validateString] ? message : OrderDetailL(@"ka_order_submit_error")];
                }
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:[message jdcd_validateString] ? message : OrderDetailL(@"ka_order_submit_error")];
            }
        }
    }];
}


- (void)payLastMoneyActioWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller{
    //支付尾款
    NSString *orderId = orderIdParam;
    if (![orderId jdcd_validateString]) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    @weakify(self)
    [PlatformService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"marketing_presale_balance_result" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self)
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            NSNumber *success = [responseObject objectForKey:@"success"];
            NSString *message = [responseObject objectForKey:@"message"];
            if (success.boolValue) {
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"data"];
                NSNumber *payStatus = resultInfo[@"payStatus"];//1、已支付， 2、未支付
                if (payStatus && payStatus.integerValue == 1) {
                    NSString *jsonstr = [resultInfo objectForKey:@"returnUrl"];
                    NSDictionary *jsonDic = [JDISVOrderDetailOrderActionFloor dictionaryWithJsonString:jsonstr];
                    NSString *payUrl = [jsonDic objectForKey:@"httpUrl"];
                    NSDictionary* param = @{@"text_url":payUrl ? : @""};
                    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
                    NSString* routerStr = [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
                    [JDRouter openURL:routerStr arg:param error:nil completion:^(id obj){
                        [controller.navigationController pushViewController:obj animated:YES];
                    }];
                } else if (payStatus.integerValue == 2){
                    //未支付，进尾款结算提单
                    [self openCheckoutPageWithOrderId:orderId controller:controller];
                }
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:[message jdcd_validateString] ? message : OrderDetailL(@"ka_order_submit_error")];
            }
        }
    }];
}

- (void)openCheckoutPageWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller{
    //预售付尾款打开结算
    NSDictionary *arg = @{@"orderId":orderIdParam ? : @"",@"presaleEndPayment":@(1)};
    [PlatformService showLoadingInView:controller.view];
    @weakify(self)
    [JDRouter openURL:@"router://JDISVSettlementModule/settlementController" arg:arg error:nil completion:^(id  _Nullable object) {
        @strongify(self)
        [PlatformService dismissInView:controller.view];
        NSNumber *code = [object objectForKey:@"code"];
        if ([code integerValue] == 0) {
            // 成功
            UIViewController *vc = [object objectForKey:@"result"];
            if (vc) {
                [controller.navigationController pushViewController:vc animated:YES];
            }
        } else {
            // 失败
            NSString *message = [object objectForKey:@"result"];
            if ([message jdcd_validateString]) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:message];
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"ka_order_submit_error")];
            }
        }
    }];
}

- (void)delayReceiveActioWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller{
    //延迟收货
    NSString *orderId = orderIdParam;
    if (![orderId jdcd_validateString]) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    [param setObject:@(0) forKey:@"operatingPlatform"];//端口标识（0：C端，1：B端）
    @weakify(self)
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"" function:@"record_query_num_color_2.0" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self)
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            NSNumber *success = [responseObject objectForKey:@"success"];
            NSString *message = [responseObject objectForKey:@"message"];
            if (success.boolValue) {
                NSDictionary *dataDic = [responseObject objectForKey:@"data"];
                NSNumber *residueDegree = [dataDic objectForKey:@"residueDegree"];//剩余延迟次数
                NSNumber *numberOfDays = [dataDic objectForKey:@"numberOfDays"];//本次延迟天数
                if (residueDegree.intValue > 0) {
                    [KAAlert alert].config.renderW4(OrderDetailL(@"ka_order_dialog_delay_title"),[NSString stringWithFormat:OrderDetailL(@"ka_order_dialog_delay_tips"),residueDegree,numberOfDays?:@""])
                        .ka_addAction(OrderDetailL(@"ka_order_dialog_btn_cancel"), ^(UIButton * _Nonnull button) {
                            [button renderB5];
                        }, ^{
                            // 取消
                        })
                        .ka_addAction(OrderDetailL(@"isv_order_comfirm_btn_text"), ^(UIButton * _Nonnull button) {
                            [button renderB3];
                        }, ^{
                            [self delayReceiveWithOrderId:orderId];
                        }).jdcd_show();
                }else{
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_dialog_delay_no_num")];
                }
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:[message jdcd_validateString] ? message : OrderDetailL(@"ka_order_submit_error")];
            }
        }
    }];
}

- (void)applyInvoiceActioWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller{
    //申请开票
    NSDictionary* arg = @{@"orderId":orderIdParam ? : @""};
    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeInvoice];
    NSString* routerStr = [NSString stringWithFormat:@"router://%@/openApplyInvoiceController",WebModule];
    [JDRouter openURL:routerStr arg:arg error:nil completion:^(UIViewController * invoiceVC) {
        [controller.navigationController pushViewController:invoiceVC animated:YES];
    }];
}


- (void)checkInvoiceActioWithOrderId:(NSString *)applyNo controller:(UIViewController *)controller{
    //查看发票
    NSDictionary *arg  = @{@"applyNo":applyNo ? : @""};
    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeInvoice];
    NSString* routerStr = [NSString stringWithFormat:@"router://%@/openApplyDetailController",WebModule];
    [JDRouter openURL:routerStr arg:arg error:nil completion:^(UIViewController * invoiceVC) {
        [controller.navigationController pushViewController:invoiceVC animated:YES];
    }];
}

- (void)modifyOrderActioWithOrderId:(NSString *)orderIdParam controller:(UIViewController *)controller{
    //修改订单
    /// 打开订单修改页
    NSDictionary* param = @{@"orderId":orderIdParam ? : @""};
    JDISVOrderDetailFixController *vc = [JDRouter openURL:@"router://JDISVOrderDetailSDKModule/getFixOrderDetailController" arg:param error:nil completion:nil];
    vc.venderId = self.viewModel.venderId;
    vc.storehouseId = self.viewModel.storehouseId;
    vc.orderStatus = self.viewModel.orderStatus;
    vc.modiySuccess = ^{
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVOrderDetailOrderActionFloorLoadDataActionBlock"];
        [self isv_sendAction:action];
    };
    [controller.navigationController pushViewController:vc animated:YES];
}


#pragma mark - Request
// 删除订单
- (void)removeOrderInfoWithOrderId:(NSString *)orderId block:(void (^)(BOOL))block {
    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
        if (block) {
            block(NO);
        }
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"delHistoryOrder" function:@"" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            if (block) {
                block(NO);
            }
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            if ([code isEqual:@0]) {
                NSNumber *rt = [responseObject objectForKey:@"success"];
                if (block) {
                    block([rt boolValue]);
                }
            } else {
                if (block) {
                    block(NO);
                }
            }
        }
    }];
}

// 确认收货
- (void)comfirmOrderInfoWithOrderId:(NSString *)orderId block:(void (^)(BOOL))block {
    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
        if (block) {
            block(NO);
        }
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    
//    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"confirmReceipt" function:@"" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//        if (error) {
//            if (block) {
//                block(NO);
//            }
//        } else {
//            NSNumber *code = [responseObject objectForKey:@"code"];
//            if ([code isEqual:@0]) {
//                NSDictionary *dict = [responseObject objectForKey:@"value"];
//                NSNumber *rt = [dict objectForKey:@"result"];
//                if (block) {
//                    block([rt boolValue]);
//                }
//            } else {
//                if (block) {
//                    block(NO);
//                }
//            }
//        }
//    }];
    [[OOPNetworkManager sharedManager] POST:@"order/confirm?apiCode=b2c.cbff.order.confirm" parameters:param headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            JDT_BLOCK_IF_EXISTS(block, NO);
        } else {
            BOOL success = [responseObject[@"data"] boolValue];
            JDT_BLOCK_IF_EXISTS(block, success);
        }
    }];
}

- (void)cancelOrderNew:(NSString *)orderId cancelReasonId:(NSString *)cancelReasonId complete:(void (^)(BOOL))completeBlock {
    NSDictionary *params = @{
        @"orderId": orderId,
        @"cancelReasonId": @(cancelReasonId.integerValue)
    };
    [[OOPNetworkManager sharedManager] POST:@"order/cancel?apiCode=b2c.cbff.order.cancel" parameters:params headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            JDT_BLOCK_IF_EXISTS(completeBlock, NO);
        } else {
            BOOL success = [responseObject[@"data"] boolValue];
            JDT_BLOCK_IF_EXISTS(completeBlock, success);
        }
    }];
}

// 取消订单
- (void)cancelOrderInfoWithOrderId:(NSString *)orderId block:(void (^)(BOOL,NSString *))block {
    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
        if (block) {
            block(NO,nil);
        }
        return;
    }
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId forKey:@"orderId"];
    
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"cancelOrder" function:@"" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            if (block) {
                block(NO,nil);
            }
        } else {
            NSNumber *rt = [responseObject objectForKey:@"success"];
            if (rt.boolValue) {
                NSDictionary *value = [responseObject objectForKey:@"value"];
                NSString *cancelResult = [value objectForKey:@"cancelResult"];//“1”成功 “0”失败
                //confirmCode 5: 可以取消，需要回传confirmCode，并提示relatedOrderIds
                //confirmCode 6: 不可取消，需要根据extResultMap中的canNotCancelReasons进行不可取消提示
                NSString *confirmCode = [value objectForKey:@"confirmCode"];//取消时候关联的订单号
                if ([cancelResult isEqualToString:@"1"]) {
                    NSDictionary *extResultMap = [value objectForKey:@"extResultMap"];
                    NSString *showTip = [extResultMap objectForKey:@"showTip"];
                    if (block) {
                        block(YES,showTip);
                    }
                } else if ([confirmCode isEqualToString:@"5"]) {
                    NSArray *relatedOrderIds = [value objectForKey:@"relatedOrderIds"];//取消时候关联的订单号
                    [KAAlert alert].config.renderW4Alignment(OrderDetailL(@"ka_order_dialog_cancel_order_title"), [NSString stringWithFormat:OrderDetailL(@"isv_order_detail_cancel_tip"),[relatedOrderIds componentsJoinedByString:@","]], NSTextAlignmentCenter)
                        .ka_addAction(OrderDetailL(@"ka_order_dialog_btn_cancel"), ^(UIButton * _Nonnull button) {
                        [button renderB5];
                    }, ^{
                        
                    })
                    .ka_addAction(OrderDetailL(@"isv_order_comfirm_btn_text"), ^(UIButton * _Nonnull button) {
                        [button renderB3];
                    }, ^{
                        NSMutableDictionary *params = [NSMutableDictionary dictionary];
                        [params setObject:orderId forKey:@"orderId"];
                        [params setObject:confirmCode ? : @"" forKey:@"confirmCode"];
                        [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"cancelOrder" function:@"" version:@"1.0" parameters:[params copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
                            if (error) {
                                if (block) {
                                    block(NO,nil);
                                }
                            } else {
                                NSNumber *success = [responseObject objectForKey:@"success"];
                                if (success.boolValue) {
                                    if (block) {
                                        block(YES,nil);
                                    }
                                }else{
                                    if (block) {
                                        block(NO,nil);
                                    }
                                }
                            }
                        }];
                    }).jdcd_show();
                } else if ([confirmCode isEqualToString:@"6"]) {
                    NSDictionary *extResultMap = [value objectForKey:@"extResultMap"];
                    NSArray *canNotCancelReasons = [extResultMap objectForKey:@"canNotCancelReasons"];
                    NSString *reasons = [canNotCancelReasons componentsJoinedByString:@"\n"];
                    [KAAlert alert].config.renderW3a(reasons,NSTextAlignmentCenter)
                        .ka_addAction(OrderDetailL(@"isv_order_i_known"), ^(UIButton * _Nonnull button) {
                            [button renderB3];
                        }, ^{
                            
                        }).jdcd_show();
                } else {
                    //0
                    NSDictionary *extResultMap = [value objectForKey:@"extResultMap"];
                    NSString *showTip = [extResultMap objectForKey:@"showTip"];
                    if (block) {
                        block(NO,showTip);
                    }
                }
            } else {
                if (block) {
                    block(NO,nil);
                }
            }
        }
    }];
}

//确认延迟收货
- (void)delayReceiveWithOrderId:(NSString *)orderId{
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId?:@"" forKey:@"orderId"];
    [param setObject:@(0) forKey:@"operatingPlatform"];//端口标识（0：C端，1：B端）
    @weakify(self)
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"" function:@"record_save_record_color_3.0" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self)
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            NSNumber *success = [responseObject objectForKey:@"success"];
            NSString *message = [responseObject objectForKey:@"message"];
            if (success.boolValue) {
                NSString *dataStr = [responseObject objectForKey:@"data"];
                if ([dataStr jdcd_validateString]) {
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:dataStr];
                    // 主控制器请求数据
                    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVOrderDetailOrderActionFloorDelayReceiveAction"];
                    [self isv_sendAction:action];
                }else{
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderDetailL(@"ka_order_submit_error")];
                }
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:[message jdcd_validateString] ? message : OrderDetailL(@"ka_order_button_pay_request_failed")];
            }
        }
    }];
}
#pragma mark - Tool Func
+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString
{
    if (jsonString == nil) {
        return nil;
    }

    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err)
    {
        NSLog(@"json err：%@",err);
        return nil;
    }
    return dic;
}

#pragma mark - hitTest
- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    UIView *view = [super hitTest:point withEvent:event];
    if (view == nil) {
        for (UIView *subView in self.subviews) {
            CGPoint p = [subView convertPoint:point fromView:self];
            if (CGRectContainsPoint(subView.bounds, p)) {
                if ([subView isKindOfClass:[KAOrderDetailBottomBarMoreView class]]) {
                    view = [subView hitTest:p withEvent:event];
                    break;
                }
                
            }
        }
    }
    return view;
}

@end
