//
//  ORDERDetailCancelReasonController.m
//  JDTOrderDetailModule
//
//  Created by lvchenzhu.1 on 2025/6/23.
//

#import "ORDERDetailCancelReasonController.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "ORDERDetailCancelReasonCell.h"
#import <JDISVKAUIKitModule/UIButton+KAButton.h>

@import JDISVCategoryModule;

@interface ORDERDetailCancelReasonController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *tipLabel;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIButton *confirmBtn;
@property (nonatomic, assign) NSInteger selectedIndex;
//@property (weak, nonatomic) IBOutlet UIButton *sureButton;

@end

@implementation ORDERDetailCancelReasonController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupData];
    [self setupUI];
}

- (void)setupData {
    self.selectedIndex = -1;
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];

    // header
    [self.view addSubview:self.tipLabel];
    [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);
        make.leading.equalTo(self.view).offset(18);
        make.trailing.equalTo(self.view);
        make.height.mas_equalTo(20);
    }];
    
    // 标题
    [self.view addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(30);
        make.left.equalTo(self.view).offset(18);
        make.right.equalTo(self.view).offset(-18);
    }];
    
    // 确定按钮
    [self.view addSubview:self.confirmBtn];
    [self.confirmBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view).offset(-20);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-20);
        make.height.mas_equalTo(40);
    }];

    // 表格视图
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(20);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.confirmBtn.mas_top).offset(-20);
    }];
    [self.tableView registerClass:[ORDERDetailCancelReasonCell class] forCellReuseIdentifier:@"ORDERDetailCancelReasonCell"];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.reasonArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ORDERDetailCancelReasonCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ORDERDetailCancelReasonCell" forIndexPath:indexPath];
    cell.model = self.reasonArr[indexPath.row];
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 42;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    // 取消之前的选择
    if (self.selectedIndex >= 0 && self.selectedIndex < self.reasonArr.count) {
        self.reasonArr[self.selectedIndex].selected = NO;
    }

    // 设置新的选择
    self.selectedIndex = indexPath.row;
    self.reasonArr[self.selectedIndex].selected = YES;

    // 刷新表格
    [self.tableView reloadData];

    // 更新确定按钮状态
    self.confirmBtn.enabled = YES;
    
    [self.confirmBtn renderB1];
}

#pragma mark - Actions

- (void)confirmButtonClicked:(UIButton *)sender {
    if (self.selectedIndex >= 0 && self.selectedIndex < self.reasonArr.count) {
        ORDERDetailEnumItemModel *selectedReason = self.reasonArr[self.selectedIndex];

        // 执行回调
        if (self.reasonSelectedCallback) {
            self.reasonSelectedCallback(selectedReason);
        }

        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

#pragma mark - Getters

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text =  OrderDetailL(@"ka_order_cancel_reason_select_tip");
        _titleLabel.font = [UIFont boldSystemFontOfSize:16];
        _titleLabel.textColor = [UIColor blackColor];
        _titleLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _titleLabel;
}

- (UILabel *)tipLabel {
    if (!_tipLabel) {
        _tipLabel = [[UILabel alloc] init];
        _tipLabel.text = OrderDetailL(@"ka_order_cancel_reason_warn");
        _tipLabel.font = [UIFont boldSystemFontOfSize:12];
        _tipLabel.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"#FCF1ED" alpha:1];
        _tipLabel.textColor = [UIColor jdcd_colorWithHexColorString:@"#D75D23" alpha:1];
        
        _tipLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _tipLabel;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
    }
    return _tableView;
}

- (UIButton *)confirmBtn {
    if (!_confirmBtn) { 
        _confirmBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_confirmBtn renderB1];
        [_confirmBtn setTitle:OrderDetailL(@"isv_order_comfirm_btn_text") forState:UIControlStateNormal];
        [_confirmBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _confirmBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium];
        _confirmBtn.enabled = NO;
        [_confirmBtn addTarget:self action:@selector(confirmButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _confirmBtn;
}

@end
