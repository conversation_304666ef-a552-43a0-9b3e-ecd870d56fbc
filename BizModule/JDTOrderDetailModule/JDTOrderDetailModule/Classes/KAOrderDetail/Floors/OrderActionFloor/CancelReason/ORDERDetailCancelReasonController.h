//
//  ORDERDetailCancelReasonController.h
//  JDTOrderDetailModule
//
//  Created by lvchenzhu.1 on 2025/6/23.
//

#import <UIKit/UIKit.h>
@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

typedef void(^ORDERCancelReasonSelectedBlock)(ORDERDetailEnumItemModel *reason);

@interface ORDERDetailCancelReasonController : UIViewController

@property (nonatomic, copy) NSArray <ORDERDetailEnumItemModel *> *reasonArr;

/// 取消原因选择回调
@property (nonatomic, copy) ORDERCancelReasonSelectedBlock reasonSelectedCallback;

@end

NS_ASSUME_NONNULL_END
