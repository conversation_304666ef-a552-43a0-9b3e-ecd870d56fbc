/* 
  OrderDetail.strings
  JDISVOrderDetailSDKModule

  Created by ext.chenhongyu12 on 2023/4/20.
  Copyright © 2023 张令浩. All rights reserved.
*/
"isv_order_pre_pay_last" = "متبقي";
"isv_address_detail_exit_dialog_save" = "احفظ";
"isv_order_order_id" = "رقم الطلب";
"isv_order_detail_title_order_actual_pay_money" = "الدفع الفعلي";
"isv_order_edit_address_tip" = "قد يؤثر تغيير العنوان على الكفاءة اللوجستية، يرجى الرجوع إلى الشحن الفعلي";
"isv_order_qrcode_timeout_tip" = "استرداد تلقائي عند انتهاء الصلاحية";
"isv_order_pre_end_pay_cancel_tip" = "لم يدفع الدفعة النهائية لمنتج ما قبل البيع في الوقت المحدد، وتم إلغاء الطلبات";
"isv_address_detail_specific_label" = "العنوان التفصيلي";
"isv_order_tab_title_all_order" = "جميع";
"isv_order_list_pre_progress_end_time_tip2" = "العد التنازلي للدفع:";
"isv_order_service_chat" = "اتصل بخدمة العملاء";
"isv_order_pre_progress_end_time_tip" = "وقت دفع الرصيد";
"isv_order_tab_title_wait_to_pay" = "على أن تدفع";
"isv_address_cannot_exit_tip" = "لا يمكن تغيير العنوان";
"isv_order_auto_receive" = "تأكيد تلقائي لـ%@ المتبقية";
"isv_order_pre_progress_end" = "الرصيد غير مدفوع";
"isv_order_pre_progress_end_price" = "تم خصم الدفعة النهائية البالغة %@ (%@)";
"isv_order_pre_pay_state_pending" = "في انتظار الدفع";
"isv_address_detail_location_label" = "العنوان";
"isv_order_pre_start_price_text" = "الوديعة:";
"isv_order_edit_tip" = "يمكن تعديل الطلبات مرة واحدة فقط، يرجى فهم";
"isv_order_list_pre_progress_end_time_tip" = "وقت الدفع النهائي:";
"isv_order_edit_receive" = "تعديل الترتيب";
"isv_address_detail_region_hint" = "المقاطعات والمدن والمناطق والمحافظات، ناحيات وبلدات وإلخ";
"isv_order_tab_title_finished" = "مكتمل";
"isv_order_detail_title_order_need_end" = "الرصيد المستحق الدفع: ";
"isv_order_pre_progress_send" = "إرسال";
"isv_order_un_team_tip_part3" = "ناجح، يبقى";
"isv_order_un_team_tip_part1" = "ليس الآن";
"isv_order_un_team_tip_part2" = "%d شخص";
"isv_order_auto_close" = "إغلاق تلقائي بعد %@";
"isv_order_pre_pay_state_end" = "مدفوع";
"isv_order_detail_title_order_pay_way" = "طريق الدفع";
"isv_order_pre_end_price_text" = "الدفعة النهائية:";
"isv_order_edit_confirm" = "تأكيد التعديل";
"isv_order_receive_title" = "تلقي المعلومات";
"isv_order_team_ing" = "الشراء المشترك";
"isv_order_pre_progress_start" = "العربون مدفوع";
"isv_order_pickup_period_time_title" = "مدة الصلاحية: %@-%@";
"isv_order_pickup_code_not_settle" = "في انتظار إرسال الرمز";
"isv_address_detail_phone_hint" = "يرجى ملء رقم الهاتف المحمول للمستلم";
"isv_order_pre_progress_end2" = "الدفعة النهائية مدفوعة";
"isv_order_detail_title_order_need_pay_money" = "المبلغ المستحق:";
"isv_address_detail_phone_label" = "رقم الجوال";
"isv_order_detail_title_order_id" = "رقم الطلب";
"isv_order_list_un_team_tip" = "مطلوب %@ أشخاص إضافيين، ينتهي في: %@%@:%@:%@";
"isv_address_detail_phone_illegal" = "السماح بإدخال رقم 11 منازل فقط";
"isv_order_pre_progress_end_no_time" = "انتهت صلاحية الدفعة النهائية";
"isv_address_detail_complete_hint" = "أكمل العنوان التفصيلي";
"isv_order_detail_title_buyer_message" = "رسالة";
"isv_address_detail_exit_dialog_discard" = "لا تحفظ";
"isv_order_detail_title_order_discount" = "إجمالي مبلغ الخصم";
"isv_order_detail_title_order_need_bargin" = "الوديعة المستحقة:";
"isv_order_data_format10" = "%@ أيام %@ ساعة %@ دقيقة %@ ثانية";
"isv_order_detail_title_order_product_total_price" = "إجمالي كمية البضائع";
"isv_address_detail_specific_hint" = "شارع ورقم عمارة والخ";
"isv_order_order_logistics_title" = "تتبع الطلب";
"isv_order_data_format11" = "%@ ساعة %@ دقيقة %@ ثانية";
"isv_order_team_fail" = "فاشل";
"isv_order_team_text" = "تجمع";
"isv_address_detail_name_illegal" = "الرجاء إدخال ما لا يزيد عن 20 الصينية والإنجليزية وأرقام في المستلم";
"isv_order_data_format7" = "%02d : %02d : %02d";
"isv_order_data_format6" = "%d أيام %02d: %02d : %02d";
"isv_order_data_format5" = "%02d:%02d:%02d";
"isv_order_data_format4" = "%d أيام %02d:%02d:%02d";
"isv_order_detail_title_order_time" = "وقت الطلب";
//"isv_order_data_format3" = "%@ %@ ساعة %@ دقيقة %@ ثانية";
"isv_order_data_format3" = "%@ %@:%@:%@";
"isv_order_data_format3_unit" = "أيام";
"isv_order_data_format2" = "%@-%@ %@:%@";
"isv_order_data_format1" = "MM-dd HH:mm";
"isv_order_copy_success" = "نجاح النسخة";
"isv_order_title_order_number" = "رقم الطلب: %@";
"isv_address_detail_exit_dialog_title" = "ما إذا كنت تريد الخروج من التحرير";
"isv_order_data_format9" = "%@ ساعة %@ دقيقة %@ ثانية";
"isv_order_data_format8" = "%d أيام %@ ساعة %@ دقيقة";
"isv_address_detail_specific_illegal" = "السماح بإدخال فقط أحرف 6~%@ منازل للعنوان التفصيلي";
"isv_order_team_suc" = "ناجح";
"isv_order_pre_progress_end_price_text" = "الرصيد المستحق الدفع";
"isv_address_cannot_edit_level" = "لا يمكن تعديل العنوان المتتالي بعد الآن";
"isv_address_detail_name_hint" = "يرجى ملء اسم المستلم";
"isv_order_text_copy" = "نسخ";
"isv_address_detail_name_label" = "الاسم بالكامل";
"isv_order_title_logistics_name" = "الناقل: ";
"isv_order_detail_title_order_freight" = " تكاليف الشحن";
"isv_address_detail_phone_empty" = "الرجاء إدخال رقم الهاتف";
"isv_order_detail_title_order_delivery_way" = "طريق التوصيل";
"isv_order_pickup_store_info" = "ساعات العمل: %@\nالهاتف: %@";
"isv_order_used_store" = "المتاجر المتاحة";
"isv_order_edit_user_info" = "تعديل معلومات المستلم";
"isv_order_detail_title" = "تفاصيل الطلب";
"isv_address_detail_region_illegal" = "الرجاء تحديد منطقتك";
"isv_order_detail_btn_more_operation" = "أكثر";
"isv_order_title_logistics_number" = "رقم التتبع: %@";
"ka_order_button_delete_request_failed" = "فشل حذف الطلبات، يرجى إعادة المحاولة لاحقا";
"ka_order_customer_pickup_code_title" = "رمز الاستلام الذاتي: ";
"ka_order_dialog_confirm_receipt_title" = "هل أنت متأكد من أنك تلقيت المنتج؟";
"ka_order_tab_title_finished" = "مكتمل";
"ka_order_no_map_installed" = "قد لا يحتوي جهازك على تطبيق خريطة مثبت، يرجى التحقق";
"ka_order_submit_error" = "فشل الطلب، يرجى إعادة المحاولة لاحقا";
"ka_order_cancel_order_cancelling" = "جاري إلغاء طلباتك، يرجى الانتظار";
"ka_order_pickup_business_time_title" = "ساعات العمل: %@";
"ka_order_title_order_type" = "نوع الطلبات";
"ka_order_dialog_btn_cancel" = "إلغاء";
"ka_order_tab_title_cancelled" = "تم الإلغاء";
"ka_order_pickup_code_not_settle" = "جاري تحضير المنتجات";
"ka_order_submit_edit_success" = "تم التعديل بنجاح، سيتم تحديث عنوان الطلبات لاحقا";
"ka_order_list_title" = "طلباتي";
"ka_order_dialog_btn_confirm" = "تأكد";
"ka_order_list_empty_tip" = "ليس لديك طلبات ذات صلة حتى الآن";
"ka_order_submit_success" = "'قدمت بنجاح'";
"ka_order_dialog_delay_no_num" = "يوجد حاليا 0 عمليات استلام متأخرة متبقية";
"ka_order_button_ensure_receive_request_failed" = "فشل تأكيد الاستلام، يرجى إعادة المحاولة لاحقا";
"ka_order_customer_pick_up_place" = "نقطة الاستلام الذاتي:";
"ka_order_dialog_delay_title" = "تأكيد عملية الاستلام المتأخر؟";
"ka_order_button_cancel_request_success" = "جاري إلغاء طلباتك، يرجى الانتظار";
"ka_order_submit_edit_error" = "فشل تعديل الطلب";
"ka_order_dialog_cancel_order_title" = "هل أنت متأكد من إلغاء الطلب؟";
"ka_order_dialog_delete_order_content" = "لا يمكن استرداد الطلب بعد حذفه";
"ka_order_type_life" = "الحياة المحلية";
"ka_order_select_reset" = "اعادة تعيين";
"ka_order_comfirm" = "تأكد";
"ka_order_tab_title_wait_to_pay" = "على أن تدفع";
"ka_order_pickup_code_period_title" = "الموعد النهائي: %@";
"ka_order_dialog_delay_tips" = "يوجد حاليا %@ عمليات تأخير الاستلام المتبقية، بعد اجراء عمليات تأخير الاستلام، سيتم تمديد وقت إكمال الطلبات لمدة %@ يوما";
"ka_order_button_pay_request_failed" = "فشل الدفع، يرجى إعادة المحاولة لاحقا";
"ka_order_title_aftersale" = "أمر ما بعد البيع";
"ka_order_button_cancel_request_failed" = "فشل الإلغاء، يرجى إعادة المحاولة لاحقا";
"ka_order_button_delete_request_success" = "تم حذف الطلبات";
"ka_order_dialog_delete_order_title" = "هل أنت متأكد من حذف الطلب؟";
"ka_order_button_buy_again_failed" = "فشل إضافة المنتج إلى عربة التسوق!";
"ka_order_select_submit" = "تقديم";
"ka_order_tab_title_all_order" = "جميع";
"ka_order_title_order" = "طلبات";
"isv_order_pay_balance_payment" = "دفع الدفعة النهائية";
"isv_order_pay_now" = "الدفع الفوري";
"isv_order_screen" = "اختيار تصفية";
"isv_order_load_failed" = "فشل تحميل الصفحة";
"isv_order_reply" = "حاول مرة أخرى";
"isv_order_empty" = "لا توجد معلومات الطلبات حاليا";
"isv_order_invited" = "ادعو أصدقاء";
"isv_order_confirm_receipt" = "تأكيد استلام";
"isv_order_comfirm_receipt_des" = "هل أكدت استلام السلع؟";
"isv_order_comfirm_receipt_end" = "تم تأكيد الاستلام";
"isv_order_buy_again" = "اشتر مرة أخرى";
"isv_order_comment_title" = "يقيم";
"isv_order_delete_order" = "طلب حذف";
"isv_order_delete_comfirm" = "حذف";
"isv_order_delayed_receipt" = "الاستلام المتأخر";
"isv_order_comfirm_btn_text" = "تم";
"isv_order_pay_deposit" = " دفع الوديعة ";
"isv_order_waiting_for_self_pickup" = "في انتظار الاستلام الذاتي";
"isv_order_to_be_shipped" = "جاري الشحن";
"isv_order_processing" = "قيد المعالجة";
"isv_order_evaluate_sun_exposure" = "قيم التعرض لأشعة الشمس";
"isv_order_apply_for_invoicing" = "التقدم بطلب للحصول على الفواتير";
"isv_order_failed_interface_exception" = "فشل، الواجهة غير طبيعية";
"isv_order_account_balance" = "رصيد حسابك غير كاف. هل ترغب في إعادة شحنه؟";
"isv_order_pay_done_refresh" = "تم دفع طلباتك، يرجى تحديث الصفحة";
"isv_order_total_amount_limit" = "تجاوز مبلغ طلباتك الحد الإجمالي لطلب واحد. يرجى المحاولة مرة أخرى بعد التعديل";
"isv_order_network_error" = "الشبكة غير طبيعية ، يرجى المحاولة مرة أخرى لاحقا.";
"isv_order_joingroupBuy_info" = "تفاصيل شراء التعاون";
"isv_order_joinGroupBuy_outTime" = "مهلة المشتريات الجماعية";
"isv_order_day_unit" = "يوم";
"isv_order_tab_title_cancelled" = "Canceled_ar";
"isv_order_written_off" = "Used_ar";
"isv_order_expired" = "Expired_ar";
"isv_order_locked" = "Locked_ar";
"isv_order_pre_progress_price" = "الوديعة: ريال%.2f";
"isv_order_scale_zero" = "خصم 0.00";
"isv_order_pre_end_price_text_1" = "الدفعة النهائية: ريال%.2f%@";
"isv_order_pre_end_price_text_2" = "الدفعة النهائية: ريال%.2f";
"isv_order_send_after" = "الشحن قبل %@";
"isv_order_detail_pre_progress_end_time" = "العد التنازلي المتبقي للدفع";
"isv_order_time_out_order_cancel" = "إذا لم يتم استلام المنتج في الموعد النهائي، إلغاء الطلبات تلقائيا. يرجى الذهاب إلى نقطة الاستلام الذاتي في أقرب وقت ممكن";
"isv_order_i_known" = "عرفت ";
"isv_order_stop_doing_business" = " مغلق ";
"isv_order_stop_outdoing_business" = " عاطل عن العمل ";
"isv_order_address_title" = "عنوان";
"isv_order_phone_tip" = "يسمح بإدخال 9 رقما فقط";
"isv_order_address_placeholder" = "الرجاء إدخال عنوان مفصل";
"isv_order_comfirm_failed" = "فشل التقديم!";
"isv_order_track" = "تتبع الطلبات";
"isv_order_store_address" = "عنوان المتجر: %@";
"isv_order_list_pre_progress_end_time_tip" = "وقت دفع الدفعة النهائية:  دقيقة %@ ساعة %@ اليوم %@ شهر %@";
"isv_order_list_pre_progress_end_time_tip" = "وقت دفع الدفعة النهائية:  دقيقة %@ ساعة %@ اليوم %@ شهر %@";
"ka_order_dialog_delay_tips" = "يوجد حاليا  %@عمليات تأخير الاستلام المتبقية، بعد اجراء عمليات تأخير الاستلام، سيتم تمديد وقت إكمال الطلبات لمدة  %@يوما";
"isv_order_detail_gift" = "هدية";
"isv_order_detail_modify_tip" = "يمكن تعديل الطلبات مرة واحدة فقط، يرجى فهم";
"isv_order_detail_modify_recipient" = "متلقي";
"isv_order_detail_modify_tel" = "رقم التليفون";
"isv_order_detail_modify_are" = "منطقة";
"isv_order_detail_modify_detail_address" = "عنوان تفصيلي";
"isv_order_detail_modify_tip_bottom" = "قد يؤثر تغيير العنوان على الكفاءة اللوجستية، يرجى الرجوع إلى الشحن الفعلي";
"isv_order_detail_cancel_tip" = "ستقوم الطلبات [رقم الطلبات:%@] مع الخصومات المشتركة أيضا بتنفيذ هذه العملية، هل أنت متأكد من متابعة إلغاء الطلب";


//KSA 630
"isv_order_detail_title_view_invoice" = "فاتورة";
"isv_order_detail_view_invoice" = "عرض الفاتورة";
"isv_order_detail_send_to_email" = "إرسال إلى البريد الإلكتروني";
"isv_order_detail_OrderSummary" = "ملخص الطلب";
"isv_order_detail_no_invoice" = "لا الفاتورة";
"isv_order_detail_email_placeholder" = "يرجى إدخال عنوان البريد الإلكتروني للمستلم";
"isv_order_detail_email_tip" = "الرجاء إدخال عنوان البريد الإلكتروني الصحيح";
"isv_order_detail_email_title" = "البريد الإلكتروني";
"isv_order_detail_email_success" = "تم إرسال الفاتورة إلى البريد الإلكتروني المحدد، يرجى التحقق منها";
"isv_order_detail_email_failed" = "فشل إرسال البريد الإلكتروني ، يرجى المحاولة مرة أخرى في وقت لاحق";
"isv_order_share_sharebuy_detail" = "تفاصيل الشراء المشارك";

"isv_order_order_detail_product_count" = "%@ عنصر/عناصر";
"isv_order_order_detail_multi_package_status" = "التفاصيل اللوجستية";
"isv_order_order_detail_multi_package_num" = "تم تقسيم طلبك إلى X طرود، والتي تم شحن Y منها";
"isv_order_order_detail_multi_package_text" = "التغليف%d";
"isv_order_order_detail_modify_address_title" = "تعديل العنوان";
"isv_order_order_detail_modify_address_new_title" = "معلومات العنوان الجديد";
"ka_order_dialog_cancel_modify_title" = "هل أنت متأكد من إلغاء الطلب؟";
"ka_order_dialog_cancel_modify_cancel_success" = "نجح الإلغاء";
"ka_order_dialog_cancel_modify_cancel_fail" = "فشل الإلغاء";
"isv_order_order_detail_modify_address_applying" = "يجب الموافقة على طلب تعديل معلومات المستلم. يرجى الانتظار بصبر";
"isv_order_order_detail_modify_address_reject" = "تم رفض طلب تعديل معلومات المستلم";

"isv_order_order_detail_address_name_error" = "أدخل 50 حرفا كحد أقصى تتكون من أحرف إنجليزية أو عربية أو رموز خاصة مثل المسافات والشرطات السفلية والشرطة المائلة العكسية والواصلات";
"isv_order_detail_invoice_check_later" = "الفاتورة الخاصة بك قيد المعالجة حالياً, يرجى التحقق مرة أخرى لاحقا.";
"isv_order_detail_invoice_no_invoice" = "البائع غير قادر حاليا على تقديم فاتورة!";

"address_detail_name_hint" = "إدخال الاسم الكامل";
"address_detail_phone_hint" = "أدخل رقم الجوال";
"address_detail_region_hint" = "الولاية / المدينة / المنطقة";
"address_detail_specific_hint" = "إدخال العنوان التفصيلي";
"ka_order_cancel_order_title" = "取消订单";
"ka_order_cancel_reason_select_tip" = "请选择取消订单原因";
"ka_order_cancel_reason_warn" = "订单取消后将无法恢复，好货不等人哦";

"ka_order_productTotalAmount" = "商品总额";
"ka_order_freightAmount" = "运费";
"ka_order_promotAmount" = "促销";
"ka_order_voucherAmount" = "优惠券";
"ka_order_couponCodeAmount" = "优惠码";
"ka_order_pointDiscountAmount" = "积分";
"ka_order_consumptionVoucherAmount" = "消费券";
"ka_order_payAmount" = "实付款";
"ka_order_freightDiscountAmount" = "运费抵扣";

