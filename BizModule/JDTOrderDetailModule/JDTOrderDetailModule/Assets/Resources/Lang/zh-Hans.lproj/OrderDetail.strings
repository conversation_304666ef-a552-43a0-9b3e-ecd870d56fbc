/* 
  OrderDetail.strings
  JDISVOrderDetailSDKModule

  Created by ext.chenhongyu12 on 2023/4/20.
  Copyright © 2023 张令浩. All rights reserved.
*/

"isv_order_failed_interface_exception" = "失败，接口异常";
"isv_order_account_balance​" = "您的账户余额不足，是否去充值？";
"isv_order_pay_done_refresh" = "您的订单已完成支付，请刷新页面";
"isv_order_total_amount_limit" = "您的订单金额已超出单笔订单总额限制，请修改后再试";
"isv_order_network_error" = "网络异常，请稍后再试";
"isv_order_load_failed" = "页面加载失败";
"isv_order_reply" = "重试";
"isv_order_detail_title" = "订单详情";
"isv_order_pay_balance_payment" = "支付尾款";
"isv_order_invited" = "邀请好友";
"isv_order_buy_again" = "再次购买";
"isv_order_pay_deposit" = "支付定金";
"ka_order_button_pay_request_failed" = "支付失败，请稍后再试";
"ka_order_dialog_delete_order_title" = "确定要删除此订单？";
"ka_order_dialog_delete_order_content" = "订单删除后不可恢复";
"ka_order_dialog_btn_cancel" = "取消";
"isv_order_delete_comfirm" = "删除";
"ka_order_button_delete_request_success" = "订单已删除";
"ka_order_button_delete_request_failed" = "订单删除失败，请稍后重试";
"isv_order_comfirm_receipt_des" = "确认收到货了吗？";
"isv_order_confirm_receipt" = "确认收货";
"isv_order_comfirm_receipt_end" = "已确认收货";
"ka_order_button_ensure_receive_request_failed" = "确认收货失败，请稍后再试";
"ka_order_dialog_cancel_order_title" = "确定要取消订单吗？";
"isv_order_comfirm_btn_text" = "确认";
"ka_order_cancel_order_cancelling" = "正在为您取消订单，请稍后";
"ka_order_button_cancel_request_failed" = "取消订单失败，请稍后再试";
"ka_order_button_buy_again_failed" = "商品加入购物车失败！";
"ka_order_submit_error" = "请求失败，请稍后重试";
"ka_order_dialog_delay_title" = "确认延迟收货操作?";
"ka_order_dialog_delay_tips" = "当前可延迟收货操作次数剩余 %@ 次，操作延迟收货后订单完成时间将会延长 %@ 天";
"ka_order_dialog_delay_no_num" = "当前可延迟收货操作次数剩余 0 次";
"isv_order_copy_success" = "复制成功";
"isv_order_order_id" = "订单编号";
"isv_order_detail_title_order_time" = "下单时间";
"isv_order_detail_title_order_pay_way" = "支付方式";
"isv_order_detail_title_order_delivery_way" = "配送方式";
"isv_order_pre_start_price_text" = "定金";
"isv_order_pre_end_price_text" = "尾款";
"isv_order_detail_title_order_need_bargin" = "应付定金: ";
"isv_order_detail_title_order_need_end" = "应付尾款: ";
"isv_order_data_format1" = "MM月dd日 HH:mm";
"isv_order_service_chat" = "联系客服";
"isv_order_team_ing" = "拼团中";
"isv_order_team_suc" = "拼团成功";
"isv_order_team_fail" = "拼团失败";
"isv_order_joinGroupBuy_outTime" = "拼团超时";
"isv_order_day_unit" = "天";
"isv_order_list_un_team_tip" = "还差%@人成团，剩余 %@%@:%@:%@";
"isv_order_receive_title" = "接收信息";
"isv_order_qrcode_timeout_tip" = "过期自动退";
"isv_order_tab_title_cancelled" = "Canceled_zh";
"isv_order_written_off" = "Used_zh";
"isv_order_expired" = "Expired_zh";
"isv_order_locked" = "Locked_zh";
"isv_order_pickup_period_time_title" = "有效期：%@-%@";
"isv_order_used_store" = "可用门店";
"isv_order_pickup_store_info" = "营业时间: %@ 电话: %@";
"isv_order_detail_title_buyer_message" = "留言";
"isv_order_pre_progress_price" = "定金：¥%.2f";
"isv_order_scale_zero" = "优惠0.00";
"isv_order_pre_end_price_text_1" = "尾款：¥%.2f%@";
"isv_order_pre_end_price_text_2" = "尾款：¥%.2f";
"isv_order_pre_pay_state_pending" = "待付款";
"isv_order_pre_pay_state_end" = "已付款";
"isv_order_team_text" = "拼团";
"isv_order_send_after" = "%@前发货";
"isv_order_pre_end_pay_cancel_tip" = "预售商品，未按时支付尾款，订单已取消";
"isv_order_pre_progress_end_no_time" = "尾款已超时";
//"isv_order_list_pre_progress_end_time_tip4" = "支付倒计时    剩余";
"isv_order_detail_pre_progress_end_time" = "支付倒计时   剩余";
"isv_order_data_format3" = "%@ %@时%@分%@秒";
"isv_order_data_format3_unit" = "天";
"isv_order_pre_progress_end_time_tip" = "尾款支付时间";
"isv_order_data_format2" = "%@-%@ %@:%@";
"isv_order_pre_progress_start" = "定金已付";
"isv_order_pre_progress_end" = "待付尾款";
"isv_order_pre_progress_send" = "发货";
"isv_order_pre_progress_end2" = "尾款已付";
"isv_order_pre_progress_end_price_text" = "应付尾款";
"isv_order_auto_close" = "%@ 后自动关闭";
"isv_order_auto_receive" = "剩余%@自动确认";
"isv_order_time_out_order_cancel" = "超过截止日期未自提商品，订单自动取消，请尽快前往自提点";
"isv_order_i_known" = "我知道了";
"ka_order_customer_pickup_code_title" = "自提码: ";
"ka_order_pickup_code_period_title" = "截止日期：%@";
"isv_order_stop_doing_business" = "停业 ";
"isv_order_stop_outdoing_business" = "歇业 ";
"ka_order_customer_pick_up_place" = "自提点：";
"ka_order_pickup_code_not_settle" = "门店备货中";
"ka_order_pickup_business_time_title" = "营业时间: %@";
"isv_order_edit_receive" = "修改订单";
"isv_address_detail_name_hint" = "请输入收货人姓名";
"isv_address_detail_phone_illegal" = "仅允许输入20个字符的数字及字母";
"isv_address_detail_phone_empty" = "请输入电话号码";
"isv_order_phone_tip" = "电话号码仅允许输入11位数字";
"isv_order_address_placeholder" = "请输入详细地址";
"isv_address_detail_specific_illegal" = "详细地址仅允许输入6~120位字符";
"ka_order_submit_edit_error" = "订单修改失败";
"ka_order_submit_edit_success" = "修改成功，订单地址稍后更新";
"ka_order_dialog_btn_confirm" = "确定";
"isv_order_comfirm_failed" = "提交失败";
"isv_order_address_title" = "地址";
"isv_order_track" = "订单追踪";
"isv_order_text_copy" = "复制";
"isv_order_title_order_number" = "订单号：%@";
"isv_order_title_logistics_name" = "承运商: ";
"isv_order_title_logistics_number" = "运单号：%@";
"isv_order_store_address" = "门店地址：%@";
"isv_order_detail_gift" = "赠品";
"isv_order_detail_modify_tip" = "订单仅支持修改一次，敬请谅解";
"isv_order_detail_modify_recipient" = "收货人";
"isv_order_detail_modify_tel" = "手机号码";
"isv_order_detail_modify_are" = "所在地区";
"isv_order_detail_modify_detail_address" = "详细地址";
"isv_order_detail_modify_tip_bottom" = "修改地址可能会影响物流时效，请以实际配送为准";
"isv_order_detail_cancel_tip" = "共享优惠的[订单号:%@]也会执行该操作，确定要继续取消订单吗？";


//KSA 630
"isv_order_detail_title_view_invoice" = "发票";
"isv_order_detail_view_invoice" = "在线预览";
"isv_order_detail_send_to_email" = "发送到邮箱";
"isv_order_detail_email_placeholder" = "请输入收票人邮箱";
"isv_order_detail_email_tip" = "请输入正确的邮箱";
"isv_order_detail_email_title" = "邮箱";
"isv_order_detail_email_success" = "发票已发送至指定邮箱，请注意查收";
"isv_order_detail_email_failed" = "发送邮箱失败，请稍后重试";
"isv_order_share_sharebuy_detail" = "拼团详情";

"isv_order_order_detail_product_count" = "共%@件";
"isv_order_order_detail_multi_package_status" = "包裹物流详情";
"isv_order_order_detail_multi_package_num" = "您的订单被拆分为%@个包裹，%@个已发出";
"isv_order_order_detail_multi_package_text" = "包裹%d";
"isv_order_order_detail_modify_address_title" = "修改地址";
"isv_order_order_detail_modify_address_new_title" = "新的地址信息";
"ka_order_dialog_cancel_modify_title" = "确认撤销申请吗？";
"ka_order_dialog_cancel_modify_cancel_success" = "撤销成功";
"ka_order_dialog_cancel_modify_cancel_fail" = "撤销失败";

"isv_order_order_detail_modify_address_applying" = "收货人信息修改申请正在审核中，请耐心等待";
"isv_order_order_detail_modify_address_reject" = "收货人信息修改申请被驳回";

"isv_order_order_detail_address_name_error" = "仅支持输入50个字符的英文及阿语以及特殊字符空格_\-";

"isv_order_detail_invoice_no_invoice" = "卖家暂时无法提供发票！";
"isv_order_detail_invoice_check_later" = "卖家正在开发票，请稍后查看！";

"address_detail_name_hint" = "请输入收货人姓名";
"address_detail_phone_hint" = "请填写收货人手机号码";
"address_detail_region_hint" = "省市区县、乡镇等";
"address_detail_specific_hint" = "街道、楼牌号等";
"ka_order_cancel_order_title" = "取消订单";
"ka_order_cancel_reason_select_tip" = "请选择取消订单原因";
"ka_order_cancel_reason_warn" = "订单取消后将无法恢复，好货不等人哦";

"ka_order_productTotalAmount" = "商品总额";
"ka_order_freightAmount" = "运费";
"ka_order_promotAmount" = "促销";
"ka_order_voucherAmount" = "优惠券";
"ka_order_couponCodeAmount" = "优惠码";
"ka_order_pointDiscountAmount" = "积分";
"ka_order_consumptionVoucherAmount" = "消费券";
"ka_order_payAmount" = "实付款";
"ka_order_freightDiscountAmount" = "运费抵扣";

/*
 发票
 在线预览
 发送到邮箱
 请输入收票人邮箱
 请输入正确的邮箱
 邮箱
 */
