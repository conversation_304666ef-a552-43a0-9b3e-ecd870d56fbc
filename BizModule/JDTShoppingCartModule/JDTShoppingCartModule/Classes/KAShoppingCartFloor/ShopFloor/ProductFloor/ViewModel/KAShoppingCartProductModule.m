
//  KAShoppingCartProductModule.m
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/23.
//  Copyright © 2021 罗静. All rights reserved.
//

#import "KAShoppingCartProductModule.h"

#import <JDISVYYModelModule/YYModel.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVThemeModule/JDISVThemeColor.h>

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import "KAShoppingCartProductModel.h"
#import "JDISVShoppingCartMainModel.h"
#import "KAShoppingCartProductFloor.h"
#import "JDISVShoppingCarPub.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>

JDCDISVActionType const KAShoppingCartProductCollectionAction = @"KAShoppingCartProductCollectionAction";
JDCDISVActionType const KAShoppingCartProductDeleteAction = @"KAShoppingCartProductDeleteAction";


JDISVRegisterFloorModule(kProductFloor, KAShoppingCartProductModule)

@interface KAShoppingCartProductModule ()
@property (weak,nonatomic) JDISVFloorCommonModel* commonModel;

@property (assign,nonatomic) CGFloat limitInfoHeight;
@end

@implementation KAShoppingCartProductModule

- (Class)tableViewFloorClass {
    return KAShoppingCartProductFloor.class;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (CGFloat)floorHeight {
    return MAX(self.cellHeight+2,114);
}

- (BOOL)sideSlipEnable {
    return YES;
}

- (NSArray<JDCDISVAction *> *)sideSlipActions {
    //左滑效果
    JDCDISVAction *deleteAction = [JDCDISVAction actionWithType:KAShoppingCartProductDeleteAction];
    UIColor *deleteColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"] ? : [UIColor whiteColor];
    deleteAction.value = @{@"title":ShopingCartL(@"ka_cart_product_delete"),@"color":deleteColor};

    JDCDISVAction *collectionAction = [JDCDISVAction actionWithType:KAShoppingCartProductCollectionAction];
    UIColor *collectionColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10"] ? : [UIColor whiteColor];
    collectionAction.value = @{@"title":ShopingCartL(@"ka_cart_product_favorite"),@"color":collectionColor};
    
    NSArray *tempActions = @[deleteAction,collectionAction];
    return [tempActions copy];
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    
    @weakify(self)
    [commonModel commonDataDidChange:^(NSDictionary * _Nonnull commonData) {
        @strongify(self);
        BOOL edit = [commonData[@"edit"] boolValue];
        self.edit = edit;
        if(edit){
            [self dealEditModel];
            [self.cell floorDidLoad:self];
        }else{
//            [self finishEditmodel];
//            [self.cell floorDidLoad:self];
        }
    }];
    self.commonModel = commonModel;
    
//    NSDictionary *floorData = data[@"data"];
//    self.parentItem = data[@"parentItem"];
//    KAShoppingCartProductResultModel *resultModel = [KAShoppingCartProductResultModel yy_modelWithDictionary:floorData];
    
    CARTMainSkuItemModel *skuItem = data[@"data"];
    
//    KAShoppingCartProductModel *model = resultModel.info;
//    self.model = model;
//    self.udid = resultModel.uuid;
    
    //解析砍价数据
//    {
//        NSMutableArray *slashModels = [NSMutableArray array];
//        for (KAShoppingCartProductPartModel *item in resultModel.partList) {
//            NSString *nameSpace = @"jd_main-DM#cartView.product-P#canSelectPricePart";
//            if ([item.type.nameSpace isEqualToString:nameSpace]) {
//                KAShoppingCartProductSlashModel *slashModel = [KAShoppingCartProductSlashModel yy_modelWithDictionary:item.info];
//                [slashModels addObject:slashModel];
//            }
//        }
//        
//        for (KAShoppingCartProductSlashModel *slashModel in slashModels) {
//            if (slashModel.slashCore.activeCheck == 1 && slashModel.slashCore.priceIcon && [slashModel.slashCore.priceIcon isEqualToString:@"tab_1027"]) {
//                model.slashModel = slashModel;
//            }
//        }
//    }
    //解析满减
//    {
//        NSMutableArray *promosions = [NSMutableArray array];
//        for (KAShoppingCartProductPartModel *item in resultModel.partList) {
//            NSString *nameSpace = @"jd_main-DM#cartView.product-P#canSelectPromotionPart";
//            if ([item.type.nameSpace isEqualToString:nameSpace]) {
//                KAShoppingCartPromosion *prom = [KAShoppingCartPromosion yy_modelWithDictionary:item.info[@"HJM-P#canSelectPromotionPart&canSelectPromotion"]];
//                if(prom.title.length && prom.promotionId.length){
//                    [promosions addObject:prom];
//                }
//            }
//        }
//        model.promosions = [promosions copy];
//    }
    
    //解析砍价数据完成
//    NSInteger specialId = [model.cartProductExtInfo.specialId integerValue];
    //1左移28位就是预售
//    self.preSale = specialId & (1<<28);
//    self.maxCount = model.cartProductExtInfo.maxBuyNum.integerValue;
    self.maxCount = skuItem.maxBuyNum.integerValue;
    if(self.maxCount > 200){
        self.maxCount = 200;
    }
//    self.minCount = model.cartProductExtInfo. minBuyNum.integerValue;
    self.minCount = skuItem.minBuyNum.integerValue;
    if(self.minCount<=0){
        self.minCount = 1;
    }
    
//    self.skuId = model.productItemCore.skuId;
    self.skuId = skuItem.skuId;
    
//    self.itemType = model.cartProductExtInfo.itemType;
    
//    if ([model.cartProductExtInfo.enableCheck boolValue]) {
//        if ([model.productItemBasic.activeCheck boolValue]) {
    if (skuItem.enableCheck.boolValue) {
        if (skuItem.activeCheck.boolValue) {
            self.selectedStatus = KAShoppingCartProductSelectedStatusSelected;
            self.selected = YES;
            self.selectedEnable = YES;
        }else {
            self.selectedStatus = KAShoppingCartProductSelectedStatusUnSelected;
            self.selected = NO;
            self.selectedEnable = YES;
        }
    }else {
        self.selectedStatus = KAShoppingCartProductSelectedStatusDisenable;
        self.selected = YES;
        self.selectedEnable = NO;
    }
    
//    if (model.cartProductExtInfo.imgDomain) {
//        self.coverImageUrl = [NSString stringWithFormat:@"%@/%@",model.cartProductExtInfo.imgDomain,model.productItemBasic.imgUrl];
//    }else {
//        self.coverImageUrl = [NSString stringWithFormat:@"%@",model.productItemBasic.imgUrl];
//    }
    self.coverImageUrl = skuItem.imgUrl;
    
//    self.stockNum = [NSString stringWithFormat:ShopingCartL(@"ka_cart_only_number"),model.cartProductStockInfo.remainNum ? model.cartProductStockInfo.remainNum : @(0)];
    self.stockNum = [NSString stringWithFormat:ShopingCartL(@"ka_cart_only_number"), skuItem.remainNum ? skuItem.remainNum : @(0)];
    self.stockCount = skuItem.remainNum.integerValue < 1 ? skuItem.maxBuyNum.integerValue : skuItem.remainNum.integerValue;
    if (skuItem.remainNum.integerValue <= 10 && skuItem.remainNum.integerValue > 0) {
        self.showStockNum = YES;
    }else {
        self.showStockNum = NO;
    }
   
//    self.productName = model.productItemBasic.skuName;
    self.productName = skuItem.skuName;
    
//    if (model.cartProductExtInfo.propertyTagMap.count) {
//        self.showProperty = YES;
//        NSMutableArray *tempArray = [NSMutableArray array];
//        for (NSString *key in model.cartProductExtInfo.propertyTagMap) {
//            [tempArray addObject:[NSString stringWithFormat:@"%@",model.cartProductExtInfo.propertyTagMap[key]]];
//        }
//        self.property = [tempArray componentsJoinedByString:@";"];
//        self.showMoreProperty = YES;
//        self.tagImageWidth = 12;
//        self.tagImageRight = -6;
//    }else {
//        self.showProperty = NO;
//    }
    
    if (skuItem.productSpecs.count) {
        self.showProperty = YES;
        NSMutableArray *specMArr = [NSMutableArray array];
        for (CARTMainProductSpecModel *spec in skuItem.productSpecs) {
            [specMArr addObject:spec.value];
        }
        self.property = [specMArr componentsJoinedByString:@";"];
        self.showMoreProperty = YES;
        self.tagImageWidth = 12;
        self.tagImageRight = -6;
    } else {
        self.showProperty = NO;
    }
    
    //0：有货 1：无货 2：预定
//    if (model.cartProductStockInfo.stockCode.integerValue == 1) {
//        self.inStock = NO;
//        
//        self.selectedStatus = KAShoppingCartProductSelectedStatusDisenable;
//        self.selected = YES;
//        self.selectedEnable = NO;
//    }else {
//        self.inStock = YES;
//    }
    // 1 有货 0 无货
    if (skuItem.stockStatus.integerValue == 1) {
        self.inStock = YES;
    } else {
        self.inStock = NO;
        
        self.selectedStatus = KAShoppingCartProductSelectedStatusDisenable;
        self.selected = YES;
        self.selectedEnable = NO;
    }
    
//    self.price = model.cartProductExtInfo.priceShow;
    self.price = skuItem.salePrice.stringValue;
    self.originalPrice = skuItem.originalPrice.stringValue;
    
//    self.count = model.productItemBasic.num.integerValue;
    self.count = skuItem.num.integerValue;
    
    if (self.maxCount < self.minCount) {
        self.maxCount = self.minCount;
    }
    
    //数据下发是否时砍价商品
//    BOOL hasSlash = model.slashModel.slashCore.priceIcon && [model.slashModel.slashCore.priceIcon isEqualToString:@"tab_1027"];
//    if(!hasSlash){
//        NSDictionary* infoDic = floorData[@"info"];
//        NSDictionary* exInfoDic = infoDic[@"HJM-M#cartProduct&extInfo"];
//        NSArray* markSet = exInfoDic[@"productMarkSet"];
//        //砍价2种判断方式
//        if([markSet isKindOfClass:NSArray.class] && [markSet containsObject:@(35)]){
//            hasSlash = YES;
//        }
//    }
    //兼容KSA环境
//    NSDictionary* infoDic = [floorData jdcd_getDicElementForKey:@"info"];
//    NSDictionary* exInfoDic = [infoDic jdcd_getDicElementForKey:@"RCB2C-D#productDecorator&extInfo"];
//    NSArray* markSet = [exInfoDic jdcd_getArrayElementForKey:@"productMarkSet"];
    NSArray* markSet = skuItem.productMarkSet;
//    if(!hasSlash){
        //砍价2种判断方式
//        if([markSet containsObject:@(35)]){
//            hasSlash = YES;
//        }
//    }

    if([markSet containsObject:@(36)]){
        self.isShip = YES;
    }
    
    //配置开关是否显示砍价，优先级更高
//    NSDictionary *configData = [data jdcd_getDicElementForKey:@"ext"];
    
    //砍价价格
//    BOOL slashFeature = [configData[@"slashFeature"] boolValue];
//    if (hasSlash && slashFeature) {
//        //tab_1027表示砍价商品，显示砍价标
//        self.priceColor = @"#C9";
//        self.slashPrice = self.price;
//    }else {
        //非砍价
        self.priceColor = @"#C9";
        
        self.slashPrice = nil;
//    }
    //砍价标
//    if (hasSlash && slashFeature) {
//        self.showSlash = YES;
//    }else {
//        self.showSlash = NO;
//    }
//    if(!self.preSale && model.promosions.count > 1){
//        self.showPromotion = YES;
//    }else{
//        self.showPromotion = NO;
//    }
    
//    if(resultModel.info.secKillModel.seckillTag){
//        self.secKill = resultModel.info.secKillModel.secKillEndRemainTime/1000.0;
//    }
    for (CARTMainPromotionModel *promotion in skuItem.promotionList) {
        if ([promotion.activityType isEqualToString:@"SEC_KILL"]) {
            self.secKill = promotion.remainTime.integerValue / 1000.0;
        }
    }

//    if(self.preSale){
//        self.showSlash = NO;
//        self.secKill = 0;
//    }
    self.orignalSelectedStatus = self.selectedStatus;
    [self dealEditModel];
    
    self.limitInfoText = @"";
    if(self.maxCount==200 && self.minCount == 1){
        self.limitInfoText = @"";
    }else if(self.maxCount == self.minCount) { //最大=最小，只展示最大 田华
        self.limitInfoText = [NSString stringWithFormat:ShopingCartL(@"ka_cart_max_limit_des"),@(self.maxCount)];
    } else if(self.minCount >1 && self.maxCount < 200){
        NSString* lang = [NSString getKAUseLang];
        if([lang isEqualToString:@"en"]){
            self.limitInfoText = [NSString stringWithFormat:ShopingCartL(@"ka_cart_min_max_limit_des"),@(self.minCount),@(self.maxCount)];
        }else{
            self.limitInfoText = [NSString stringWithFormat:ShopingCartL(@"ka_cart_min_max_limit_des"),@(self.maxCount),@(self.minCount)];
        }
    }else if(self.minCount > 1){
        self.limitInfoText = [NSString stringWithFormat:ShopingCartL(@"ka_cart_min_limit_des"),@(self.minCount)];
    }else if(self.maxCount > 1){
        self.limitInfoText = [NSString stringWithFormat:ShopingCartL(@"ka_cart_max_limit_des"),@(self.maxCount)];
    }
    
    self.limitInfoHeight = 0;
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    CGFloat screenWidth =  UIScreen.mainScreen.bounds.size.width;
    CGFloat textWidth = screenWidth - 2*w3-50-90-2*12;
    
    if(self.limitInfoText.length){
        UIFont* font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T10"];
        CGSize size = [self.limitInfoText jdcd_getStringSize:font constraintsSize:CGSizeMake(textWidth, 300)];
        self.limitInfoHeight = size.height;
    }
    
    
    self.secKilltopMargin = (self.showProperty)?30:6;
    if(self.secKill && (self.showProperty)){ //显示2行
        self.priceTopMargin = 88+self.limitInfoHeight;
    }else {
        if(self.limitInfoHeight > 16){
            self.priceTopMargin = 76+14;
        }else{
            self.priceTopMargin = 76;
        }
    }
    
//    if(self.showSlash){
//        self.cellHeight = self.priceTopMargin+18+18+8;
//    }else{
        self.cellHeight = self.priceTopMargin+18+8;
//    }
    

}

-(void)finishEditmodel{
    if(self.orignalSelectedStatus == KAShoppingCartProductSelectedStatusDisenable){
        self.selectedStatus = KAShoppingCartProductSelectedStatusDisenable;
        self.selected = NO;
        self.selectedEnable = NO;
    }
}

-(void)dealEditModel{
    //只处理编辑模式
    if (![self.commonModel.commonData[@"edit"] boolValue]){
        return;
    }
    //指出来原来是不可选的状态
    if(self.orignalSelectedStatus != KAShoppingCartProductSelectedStatusDisenable)
        return;
    self.selectedEnable = YES;
    NSMutableArray* editSkus = self.commonModel.commonData[@"editModeSkus"];
    if([editSkus containsObject:self.skuId]){
        self.selectedStatus = KAShoppingCartProductSelectedStatusSelected;
        self.selected = YES;
    }else{
        self.selectedStatus = KAShoppingCartProductSelectedStatusUnSelected;
        self.selected = NO;
    }
}
- (void)editSaveToStore:(BOOL)save{
    NSMutableArray* store = self.commonModel.commonData[@"editModeSkus"];
    if(!store){
        store = [NSMutableArray array];
    }
    if(save){
        [store addObject:self.skuId];
    }else{
        [store removeObject:self.skuId];
    }
    self.commonModel.commonData[@"editModeSkus"] = store;
    [self.commonModel commonDataChange];
}

#pragma mark - gatewayParam
//-(NSDictionary*)promotionID:(NSString*)pid
//                        num:(NSNumber*)num{
//    if([self.parentItem.type.nameSpace containsString:@"#cartView.promotion-M#d"]){
//        NSMutableDictionary* result = [NSMutableDictionary dictionary];
//        result[@"id"] = NSUUID.UUID.UUIDString;
//        result[@"promotionId"]  = self.parentItem.info[@"C-M#promotionItem&core"][@"promotionId"]?:@"";
//        result[@"itemType"] = self.parentItem.info[@"C-M#promotionItem&core"][@"promotionType"]?:@"";
//        result[@"num"] = num;
//        result[@"childList"] = @[pid];
//        return result;
//    }
//    return nil;
//}

#pragma mark - action
- (JDCDISVAction *)selectAction {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    if (self.skuId) {
        [params setObject:self.skuId forKey:@"skuId"];
    }
//    if (self.itemType) {
//        [params setObject:self.itemType forKey:@"itemType"];
//    }
    [params setObject:@(self.count) forKey:@"num"];
    
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    NSString* uuid = NSUUID.UUID.UUIDString;
    [params setObject:uuid forKey:@"id"];
//    NSDictionary* promotionDic = [self promotionID:uuid num:@(self.count)];
//    if(promotionDic){
//        [params setObject:promotionDic[@"id"] forKey:@"parentId"];
//        [result setObject:@{@"products":@[params],@"promotions":@[promotionDic]} forKey:@"operations"];
//    }else{
        [result setObject:@{@"products":@[params]} forKey:@"operations"];
//    }

    NSString *selectTag = @"select";
    if (self.selectedStatus == KAShoppingCartProductSelectedStatusSelected) {
        selectTag = @"unselect";
    }else if(self.selectedStatus == KAShoppingCartProductSelectedStatusUnSelected){
        selectTag = @"select";
    }
    [result setObject:selectTag forKey:@"userActionId"];
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartSelectProductAction"];
    action.value = result;
    return action;
}

- (JDCDISVAction *)deleteAction {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (self.skuId) {
        [params setObject:self.skuId forKey:@"skuId"];
    }
//    if (self.itemType) {
//        [params setObject:self.itemType forKey:@"itemType"];
//    }
    [params setObject:@(self.count) forKey:@"num"];
    
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    NSString* uuid = NSUUID.UUID.UUIDString;
    [params setObject:uuid forKey:@"id"];
//    NSDictionary* promotionDic = [self promotionID:uuid num:@(self.count)];
//    if(promotionDic){
//        [params setObject:promotionDic[@"id"] forKey:@"parentId"];
//        [result setObject:@{@"products":@[params],@"promotions":@[promotionDic]} forKey:@"operations"];
//    }else{
        [result setObject:@{@"products":@[params]} forKey:@"operations"];
//    }
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartDeleteAction"];
    action.value = result;
    return action;
}

- (JDCDISVAction *)collectionAction {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (self.skuId) {
        [params setObject:self.skuId forKey:@"skuId"];
    }
//    if (self.itemType) {
//        [params setObject:self.itemType forKey:@"itemType"];
//    }
    [params setObject:@(self.count) forKey:@"num"];
    
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    NSString* uuid = NSUUID.UUID.UUIDString;
    [params setObject:uuid forKey:@"id"];
//    NSDictionary* promotionDic = [self promotionID:uuid num:@(self.count)];
//    if(promotionDic){
//        [params setObject:promotionDic[@"id"] forKey:@"parentId"];
//        [result setObject:@{@"products":@[params],@"promotions":@[promotionDic]} forKey:@"operations"];
//    }else{
        [result setObject:@{@"products":@[params]} forKey:@"operations"];
//    }
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartCollectionProudctAction"];
    action.value = result;
    return action;
}

- (JDCDISVAction *)changeProductCountActionWithCount:(NSInteger)count {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (self.skuId) {
        [params setObject:self.skuId forKey:@"skuId"];
    }
//    if (self.itemType) {
//        [params setObject:self.itemType forKey:@"itemType"];
//    }
    [params setObject:@(count) forKey:@"num"];
    
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    NSString* uuid = NSUUID.UUID.UUIDString;
    [params setObject:uuid forKey:@"id"];
//    NSDictionary* promotionDic = [self promotionID:uuid num:@(self.count)];
//    if(promotionDic){
//        [params setObject:promotionDic[@"id"] forKey:@"parentId"];
//        [result setObject:@{@"products":@[params],@"promotions":@[promotionDic]} forKey:@"operations"];
//    }else{
        [result setObject:@{@"products":@[params]} forKey:@"operations"];
//    }
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartChangeProductNumberAction"];
    action.value = result;
    return action;
}

- (JDCDISVAction *)changeProductActionWithSkuId:(NSString *)skuId {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    if (self.skuId) {
        [params setObject:self.skuId forKey:@"skuId"];
    }
//    if (self.itemType) {
//        [params setObject:self.itemType forKey:@"itemType"];
//    }
    if (self.count) {
        [params setObject:@(self.count) forKey:@"num"];
    }
    if (skuId) {
        [params setObject:skuId forKey:@"targetSkuId"];
    }
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartChangeProductAction"];
    action.value = @{@"operations":@{@"products":@[params]}};
    return action;
}

//- (JDCDISVAction *)changePromotionActionWithNewId:(NSString *)newId
//                                      newItemType:(NSString*)newType
//                                            oldId:(NSString*)oldId
//                                      oldItemType:(NSString*)oldType{
//    int targetPromotionType = 2;
//    
//    NSMutableDictionary* product = [NSMutableDictionary dictionary];
//    product[@"id"] = NSUUID.UUID.UUIDString;
//    NSMutableDictionary* promotion = [NSMutableDictionary dictionary];
//    promotion[@"id"] = NSUUID.UUID.UUIDString;
//    
//    
//    product[@"itemType"] = self.model.cartProductExtInfo.itemType?:@(1);
//    product[@"num"] = @(self.count);
//    product[@"skuId"] = self.skuId?:@"";
//    product[@"targetPromotionId"] = newId?:@"";
//    product[@"targetPromotionType"] = @(targetPromotionType);
//    product[@"parentId"] = promotion[@"id"];
//    
//    promotion[@"promotionId"]  = oldId?:@"";
//    promotion[@"itemType"] = @(3);
//    promotion[@"num"] = @(1);
//    promotion[@"childList"]=@[product[@"id"]];
//    
//    NSDictionary* result = @{@"operations":@{@"products":@[product],
//                                             @"promotions":@[promotion]}};
//    if([oldId isEqualToString:@"-100"] ||[oldId isEqualToString:@"-200"] ||[oldId isEqualToString:@"-300"]){
//        product[@"targetPromotionType"] = @(10);
//        [product removeObjectForKey:@"parentId"];
//        result = @{@"operations":@{@"products":@[product]}};
//    }
//    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartChangePromotionAction"];
//    action.value = result;
//    
//    return action;
//}
@end
