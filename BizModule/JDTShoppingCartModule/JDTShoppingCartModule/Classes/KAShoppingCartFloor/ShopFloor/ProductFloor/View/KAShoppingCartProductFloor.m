//
//  KAShoppingCartProductFloor.m
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/23.
//  Copyright © 2021 罗静. All rights reserved.
//

#import "KAShoppingCartProductFloor.h"

#import <JDISVMasonryModule/Masonry.h>

#import <JDISVKAUIKitModule/KAStepper.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAUIKitModule/UIButton+KALabelRender.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVKAIconFontModule/UIImageView+KATheme.h>
#import <JDISVKAUIKitModule/KAFloatLayerPresentationController.h>
#import <JDISVKAUIKitModule/NSMutableAttributedString+KAPirce.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDBRouterModule/JDRouter.h>
#import <JDISVTimeServiceModule/JDISVTimeServiceModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import "KAShoppingCartProductModule.h"
#import "JDISVShoppingCartMainModel.h"
#import "JDISVShoppingCartPropertyController.h"
#import "JDISVShoppingPromotionController.h"
#import "JDISVShoppingCartDiscountDetailContrller.h"
#import "KAShoppingSecKillView.h"
#import "JDISVShoppingCartTool.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "CARTSelectSpecController.h"

JDCDISVActionType const KAShoppingCartProductPropertyClickedAction = @"KAShoppingCartProductPropertyClickedAction";

JDCDISVActionType const KAShoppingCartProductPromosionClickedAction = @"KAShoppingCartProductPromosionClickedAction";

@interface KAShoppingCartProductFloor ()<KAStepperDelegate>

@property (nonatomic, strong) KAShoppingCartProductModule *viewModel;

@property (nonatomic, strong) UIButton *selectedButton;

@property (nonatomic, strong) UIButton *selectedMaskButton;

@property (nonatomic, strong) UIView *productView;

@property (nonatomic, strong) UIImageView *productImageView;

@property (nonatomic, strong) UIView *maskView;

@property (nonatomic, strong) UIButton *storeButton;

@property (nonatomic, strong) UILabel *storeLabel;

@property (nonatomic, strong) UILabel *productNameLabel;

@property (nonatomic, strong) UIView *propertyView;

@property (nonatomic, strong) UILabel *propertyLabel;

@property (nonatomic, strong) UIImageView *tagImageView;

@property (nonatomic, strong) UIView *promosionView;

@property (nonatomic, strong) UILabel *promosionLabel;

@property (nonatomic, strong) UIImageView *tagpromosionImageView;

@property (nonatomic, strong) KAShoppingSecKillView* secKillView;

@property (nonatomic, strong) UILabel *priceUtilLabel;

@property (nonatomic, strong) KAPriceLabel *priceLabel;

@property (nonatomic, strong) KAStepper *stepper;

@property (nonatomic, strong) UIView *slashView;

@property (nonatomic, strong) UIImageView *slashTagImageView;

@property (nonatomic, strong) UILabel *slashNameLabel;

@property (nonatomic, strong) UIButton *leftStepperButton;

@property (nonatomic, strong) UIButton *rightStepperButton;

@property (nonatomic, strong) UILabel * preSaleLabel;

@property (nonatomic, strong) UILabel * limitInfoLabel;

@property (nonatomic,strong) UIView * promotionAndNormalLine;

@property (nonatomic,assign) CGFloat  KGPromotionWidth;

@property (nonatomic, strong) UIImageView* shipbackground;

@property (nonatomic, strong) UIImageView* shipImg;

@property (nonatomic, strong) UIButton *deleteButton;

@end



@implementation KAShoppingCartProductFloor

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUpUI];
        NSString* lang = [NSString getKAUseLang];
        if([lang isEqual:@"en"]){
            self.KGPromotionWidth = 85;
        }else if([lang isEqualToString:@"ar"]){
            self.KGPromotionWidth = 88;
        }else{
            self.KGPromotionWidth = 63;
        }
    }
    return self;
}

- (void)setUpUI {
    self.contentView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [self.contentView addSubview:self.selectedButton];
    [self.selectedButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(18);
        make.width.height.mas_equalTo(20);
        make.top.mas_equalTo(51);
    }];
    
    [self.contentView addSubview:self.selectedMaskButton];
    [self.selectedMaskButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.bottom.mas_equalTo(0);
        make.width.mas_equalTo(20 + 36);
    }];
    
    [self.contentView addSubview:self.productView];
    [self.productView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self).mas_offset(50);
        make.width.height.mas_equalTo(90);
        make.top.mas_equalTo(16);
    }];
    
    [self.productView addSubview:self.productImageView];
    [self.productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.productView);
    }];
    
    [self.productView addSubview:self.storeLabel];
    [self.storeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.leading.trailing.mas_equalTo(0);
        make.height.mas_equalTo(20);
    }];
    
    [self.productView addSubview:self.storeButton];
    [self.storeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.productView).insets(UIEdgeInsetsMake(15, 15, 15, 15));
    }];
//    [self.contentView addSubview: self.deleteButton];
//    [self.deleteButton mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.trailing.mas_equalTo(self.mas_trailing).mas_offset(-18);
//        make.top.mas_equalTo(self.productView.mas_top).mas_offset(-4);
//        make.height.width.mas_equalTo(22);
//    }];
    [self.contentView addSubview:self.productNameLabel];
    [self.productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        make.trailing.mas_equalTo(self.contentView.mas_trailing).mas_offset(-12);
        make.top.mas_equalTo(self.productView.mas_top);
        make.height.mas_equalTo(18);
    }];
    {//property
        [self.contentView addSubview:self.propertyView];
        //        [self.propertyView mas_makeConstraints:^(MASConstraintMaker *make) {
        //            make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        //            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
        //            make.height.mas_equalTo(18);
        //            make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-18);
        //        }];
        
        [self.propertyView addSubview:self.tagImageView];
        [self.tagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.propertyView.mas_trailing).mas_offset(-6);
            make.height.width.mas_equalTo(12);
            make.top.mas_equalTo(3);
        }];
        
        [self.propertyView addSubview:self.propertyLabel];
        [self.propertyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(6);
            make.centerY.mas_equalTo(self.propertyView.mas_centerY);
            make.trailing.mas_equalTo(self.tagImageView.mas_leading).mas_offset(-6);
        }];
        self.propertyView.hidden = YES;
    }
    {//promotion
        [self.contentView addSubview:self.promosionView];
        [self.promosionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.propertyView.mas_trailing).mas_offset(6);
            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
            make.height.mas_equalTo(18);
            make.width.mas_equalTo(self.KGPromotionWidth);
            make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-6);
        }];
        [self.promosionView addSubview:self.tagpromosionImageView];
        [self.tagpromosionImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.promosionView.mas_trailing).mas_offset(-6);
            make.height.width.mas_equalTo(12);
            make.top.mas_equalTo(3);
        }];
        [self.promosionView addSubview:self.promosionLabel];
        [self.promosionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(6);
            make.centerY.mas_equalTo(self.promosionView.mas_centerY);
            make.trailing.mas_equalTo(self.tagpromosionImageView.mas_leading);
        }];
        self.promosionView.hidden = YES;
    }
    
    [self.contentView addSubview:self.secKillView];
    [self.secKillView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productNameLabel.mas_leading);
        make.trailing.mas_equalTo(self.mas_trailing).offset(-15);
        make.height.mas_equalTo(20);
        make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
    }];
    
    [self.contentView addSubview:self.priceUtilLabel];
    [self.priceUtilLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        make.bottom.mas_equalTo(self.productView.mas_bottom).mas_offset(- 3);
        make.height.mas_equalTo(12);
        make.width.mas_equalTo(0);
    }];
    
    [self.contentView addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.priceUtilLabel.mas_trailing);
        make.top.mas_equalTo(self.productNameLabel.mas_top).mas_offset(75);
        make.height.mas_equalTo(18);
    }];
    [self.contentView addSubview:self.preSaleLabel];
    self.preSaleLabel.numberOfLines = 0;
    [self.preSaleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.priceUtilLabel.mas_leading);
        make.trailing.mas_equalTo(self).mas_offset(-12);
        make.top.mas_equalTo(self.productNameLabel.mas_top).mas_offset(54);
    }];
    
    if(PlatformService.isRTL){
        [self.contentView addSubview:self.stepper];
        [self.stepper mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.mas_trailing).mas_offset(-18);
            make.width.mas_equalTo(78);
            make.height.mas_equalTo(20);
            make.top.mas_equalTo(self.priceLabel.mas_top);
        }];
        [self.contentView addSubview:self.leftStepperButton];
        [self.leftStepperButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.stepper.mas_trailing).mas_offset(-20);
            make.height.mas_equalTo(30);
            make.width.mas_equalTo(40);
            make.centerY.mas_equalTo(self.stepper.mas_centerY);
        }];
        
        [self.contentView addSubview:self.rightStepperButton];
        [self.rightStepperButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.stepper.mas_leading).mas_offset(20);
            make.height.width.mas_equalTo(30);
            make.width.mas_equalTo(40);
            make.centerY.mas_equalTo(self.stepper.mas_centerY);
            
        }];
    }else{
        [self.contentView addSubview:self.stepper];
        [self.stepper mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.mas_trailing).mas_offset(-18);
            make.width.mas_equalTo(78);
            make.height.mas_equalTo(20);
            make.top.mas_equalTo(self.priceLabel.mas_top);
        }];
        [self.contentView addSubview:self.leftStepperButton];
        [self.leftStepperButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.stepper.mas_leading).mas_offset(20);
            make.height.width.mas_equalTo(30);
            make.width.mas_equalTo(40);
            make.centerY.mas_equalTo(self.stepper.mas_centerY);
        }];
        
        [self.contentView addSubview:self.rightStepperButton];
        [self.rightStepperButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.stepper.mas_trailing).mas_offset(-20);
            make.height.mas_equalTo(30);
            make.width.mas_equalTo(40);
            make.centerY.mas_equalTo(self.stepper.mas_centerY);
        }];
    }
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(self.leftStepperButton.mas_leading).mas_offset(0);
    }];
    [self.contentView addSubview:self.slashView];
    CGSize size = [self.slashNameLabel.text jdcd_getStringSize:self.slashNameLabel.font constraintsSize:CGSizeMake(100, 18)];
    [self.slashView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        make.top.mas_equalTo(self.priceLabel.mas_bottom).mas_offset(2.5);
        make.width.mas_equalTo(size.width+18+4);
        make.height.mas_equalTo(18);
    }];
    
    [self.slashView addSubview:self.slashTagImageView];
    [self.slashTagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(4);
        make.centerY.mas_equalTo(self.slashView.mas_centerY);
        make.width.height.mas_equalTo(12);
    }];
    
    [self.slashView addSubview:self.slashNameLabel];
    [self.slashNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.slashTagImageView.mas_trailing).mas_offset(2);
        make.trailing.mas_equalTo(0);
        make.centerY.mas_equalTo(self.slashView.mas_centerY);
    }];
    
    [self.productView addSubview:self.maskView];
    [self.maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.productView);
    }];
    
    self.promotionAndNormalLine = [[UIView alloc] initWithFrame:CGRectZero];
    [self.contentView addSubview:self.promotionAndNormalLine];
    [self.promotionAndNormalLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentView).mas_offset(18);
        make.trailing.mas_equalTo(self.contentView).mas_offset(-18);
        make.height.mas_equalTo(1);
    }];
    self.promotionAndNormalLine.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    [self.contentView addSubview:self.limitInfoLabel];
    
    [self.limitInfoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productNameLabel.mas_leading);
        make.trailing.mas_equalTo(self).mas_lessThanOrEqualTo(-12);
        make.bottom.mas_equalTo(self.priceLabel.mas_top).mas_offset(-1);
    }];
    
    self.shipbackground = [[UIImageView alloc] initWithFrame:CGRectZero];
    self.shipImg = [[UIImageView alloc] initWithFrame:CGRectZero];
    self.shipbackground.hidden = YES;
    
    [self.productView addSubview:self.shipbackground];
    [self.shipbackground mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productView);
        make.trailing.mas_equalTo(self.productView.mas_trailing);
        make.width.height.mas_equalTo(12);
    }];
    [self.shipbackground addSubview:self.shipImg];
    [self.shipImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.shipbackground);
        make.width.height.mas_equalTo(11);
    }];
    UIImage*shipBack = [UIImage isv_shoppingCart_imageWithName:@"isv_shopping_cart_shopback"];
    if([PlatformService isRTL]){
        shipBack = [shipBack RTL];
    }
    self.shipbackground.image = shipBack;
    UIImage* shipImg =
    [UIImage ka_iconWithName:JDIF_ICON_KUAJING imageSize:CGSizeMake(11,11) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C43"]];
    self.shipImg.image = shipImg;
    
    
    if(KSAAPP){
        [self.stepper showBorder];
    }
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    if(self.viewModel){
        self.viewModel.cell = nil;
        self.viewModel  = nil;
    }
    
    
    self.viewModel = floorModel;
    self.viewModel.cell = self;
    
    self.shipbackground.hidden = !self.viewModel.isShip;
    
    if (self.viewModel.inStock) {
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.productNameLabel.textColor = color;
    }else {
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        self.productNameLabel.textColor = color;
    }
    self.stepper.delegate = self;
    self.productNameLabel.text = self.viewModel.productName;
    
    
    if(self.viewModel.secKill){
        [self.secKillView updateLeaftSec:self.viewModel.secKill];
        __weak typeof(self) wSelf = self;
        [[JDCDReverceTimer reverceTimer]
         addTarget:self startTime:self.viewModel.secKill timeInterval:1 complete:^(NSInteger running) {
            if(running == 0){
                JDCDISVAction *action = [JDCDISVAction actionWithType:kJDCDISVFloorReloadDataAction];
                [wSelf isv_sendAction:action];
                return;
            }
            [wSelf.secKillView updateLeaftSec:running];
        }];
        self.secKillView.hidden = NO;
        
        [self.secKillView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(self.viewModel.secKilltopMargin);
        }];
    }else{
        self.secKillView.hidden = YES;
    }
    
    [self.priceLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(self.viewModel.priceTopMargin);
    }];
    [self.tagImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(self.viewModel.tagImageWidth);
        make.trailing.mas_equalTo(self.propertyView.mas_trailing).mas_offset(self.viewModel.tagImageRight);
    }];
    
    self.priceUtilLabel.text = @"";
    
    self.viewModel.price = [self.viewModel.price stringByReplacingOccurrencesOfString:@"¥" withString:@""];
//    [_priceLabel configTextWithPrice:self.viewModel.price.doubleValue middleLinePrice:self.viewModel.model.productItemBasic.price.doubleValue];
    [self.priceLabel configTextWithPrice:self.viewModel.price.doubleValue middleLinePrice:self.viewModel.originalPrice.doubleValue];
    
    //限制不住最大，所以最大只是提示，端上不做最大数量显示
    self.stepper.maxLimit = 200;
    
    self.stepper.minLimit = self.viewModel.minCount;
//    self.stepper.stockLimit = 200;
    self.stepper.stockLimit = self.viewModel.stockCount;
    NSInteger count = self.viewModel.count;
    [self.stepper reloadWithCount:count];
    UIImage *placeHolder = [PlatformService getDefaultPlaceholderImage:JDISVWebImagePlaceHolderTypeSmall];
    
    NSString *imageUrl = [PlatformService getCompleteImageUrl:self.viewModel.coverImageUrl moduleType:JDISVModuleTypeShoppingCart];
    imageUrl = [imageUrl stringByReplacingOccurrencesOfString:@".sa/da/" withString:@".sa/da/s560x560_"];
    [self.productImageView jdcd_setImage:imageUrl placeHolder:placeHolder contentMode:UIViewContentModeScaleAspectFit];
    
    self.selectedButton.jdisv_selected_B7 = self.viewModel.selected;
    self.selectedButton.enabled = self.viewModel.selectedEnable;
    self.selectedMaskButton.enabled = self.viewModel.selectedEnable;
    if(!self.viewModel.selectedEnable){
        self.selectedMaskButton.userInteractionEnabled = NO;
    }
//    if(self.viewModel.preSale){
//        self.preSaleLabel.hidden = NO;
//        self.priceLabel.hidden = YES;
//        self.priceUtilLabel.hidden = YES;
//        self.stepper.hidden = YES;
//        self.leftStepperButton.hidden = YES;
//        self.rightStepperButton.hidden = YES;
//        self.storeLabel.text = ShopingCartL(@"ka_cart_pre_sale");
//        self.storeLabel.hidden = NO;
//        self.storeButton.hidden = YES;
//        _storeLabel.jdisv_backgroundColorPicker = JDISVColorPickerWithKeyAndAlpha(@"#C53", 0.8);
//    }else{
        self.preSaleLabel.hidden = YES;
        self.priceLabel.hidden = NO;
        self.priceUtilLabel.hidden = NO;
        self.stepper.hidden = !self.viewModel.inStock;
        self.leftStepperButton.hidden = NO;
        self.rightStepperButton.hidden = NO;
        self.storeLabel.text = self.viewModel.stockNum;
        self.storeLabel.hidden = !self.viewModel.showStockNum;
        self.storeButton.hidden = self.viewModel.inStock;
        _storeLabel.jdisv_backgroundColorPicker = JDISVColorPickerWithKeyAndAlpha(@"#C8", 0.65);
//    }
    
//    self.slashView.hidden = !self.viewModel.showSlash;
    self.slashView.hidden = YES;
    [self showNormalLine];
    
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.alignment = NSTextAlignmentNatural;
    
    NSMutableAttributedString *limitAttributeString = [[NSMutableAttributedString alloc] initWithString:self.viewModel.limitInfoText ?: @"" attributes:@{NSParagraphStyleAttributeName: paragraphStyle}];
    NSRange range = NSMakeRange(0, limitAttributeString.length);
    UIFont *font = self.limitInfoLabel.font;
    
    [limitAttributeString addAttribute:NSFontAttributeName value:font range:range];
    // 设置字体颜色
    UIColor *tColor  = self.limitInfoLabel.textColor;
    
    [limitAttributeString addAttribute:NSForegroundColorAttributeName value:tColor range:range];
    
    self.limitInfoLabel.attributedText = limitAttributeString;
    self.limitInfoLabel.text =  self.viewModel.limitInfoText;
    self.propertyLabel.text = self.viewModel.property;
    self.propertyView.hidden = !self.viewModel.showProperty;
    self.tagImageView.hidden = !self.viewModel.showMoreProperty;
//    self.promosionView.hidden = !self.viewModel.showPromotion;
    self.promosionView.hidden = YES;
    [self rebuildMas];
}

//-(JDISVShoppingCartItemModel*)findUpModel{
//    JDISVShoppingCartItemModel* upOne = nil;
//    for(JDISVShoppingCartItemModel* model in self.viewModel.parentItem.subList){
//        if([model.uuid isEqualToString:self.viewModel.udid]){
//            return upOne;
//        }
//        upOne = model;
//    }
//    return nil;
//}

-(void)showNormalLine{
//    JDISVShoppingCartItemModel* upOne = [self findUpModel];
//    if(upOne){
//        if([upOne.type.nameSpace containsString:@"#cartView.promotion-M#"]){
//            NSDictionary* vs = upOne.info[@"HJM-D#virtualSuit&virtualSuit"];
//            if(vs){
//                self.promotionAndNormalLine.hidden = YES;
//            }else{
//                self.promotionAndNormalLine.hidden = NO;
//            }
//        }else{
//            self.promotionAndNormalLine.hidden = YES;
//        }
//    }else{
        self.promotionAndNormalLine.hidden = YES;
//    }
}

-(void)rebuildMas{
//    if(self.viewModel.showProperty && self.viewModel.showPromotion){
//        CGSize size =  [self.propertyLabel.text jdcd_getStringSize:self.propertyLabel.font constraintsSize:CGSizeMake(600, 18)];
//        CGFloat width;
//        if(self.viewModel.showMoreProperty){
//            width = ceil(size.width)+ 12+18;
//        }else{
//            width = ceil(size.width) + 13;
//        }
//        [self.propertyView mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
//            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
//            make.height.mas_equalTo(18);
//            make.width.mas_equalTo(width).priority(800);
//            make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-15-self.KGPromotionWidth).priority(1000);
//        }];
//        [self.promosionView mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.leading.mas_equalTo(self.propertyView.mas_trailing).mas_offset(6);
//            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
//            make.height.mas_equalTo(18);
//            make.width.mas_equalTo(self.KGPromotionWidth);
//            make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-6);
//        }];
//        return;
//    }
//    else if(self.viewModel.showPromotion){
//        [self.promosionView mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
//            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
//            make.height.mas_equalTo(18);
//            make.width.mas_equalTo(self.KGPromotionWidth);
//            make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-6);
//        }];
//        return;
//    }else if(self.viewModel.showProperty){
        CGSize size =  [self.propertyLabel.text jdcd_getStringSize:self.propertyLabel.font constraintsSize:CGSizeMake(600, 18)];
        CGFloat width;
        if(self.viewModel.showMoreProperty){
            width = ceil(size.width)+ 12+18;
        }else{
            width = ceil(size.width) + 13;
        }
        NSLog(@"!!!Width:%@",@(width));
        [self.propertyView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
            make.height.mas_equalTo(18);
            make.width.mas_equalTo(width).priority(800);
            make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-6).priority(1000);
        }];
//    }
}

- (void)selectedButtonClicked {
    //edit模式处理
    if(self.viewModel.orignalSelectedStatus == KAShoppingCartProductSelectedStatusDisenable){
        self.viewModel.selected = !self.viewModel.selected;
        self.selectedButton.jdisv_selected_B7 = self.viewModel.selected;
        [self.viewModel editSaveToStore:self.viewModel.selected];
    }
    else {
        JDCDISVAction *action = [self.viewModel selectAction];
        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
            if (error) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_tips_action_failed")];
            }
        };
        [self isv_sendAction:action];
    }
}

- (void)propertyClicked {
    JDCDISVAction *action = [JDCDISVAction actionWithType:KAShoppingCartProductPropertyClickedAction];
    [self isv_sendAction:action];
}

- (void)promosionClicked{
    JDCDISVAction *action = [JDCDISVAction actionWithType:KAShoppingCartProductPromosionClickedAction];
    [self isv_sendAction:action];
}

#pragma mark action
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    __weak typeof(self) wSelf = self;
    if ([action.actionType isEqualToString:KAShoppingCartProductCollectionAction]) {
        JDCDISVAction *action = [self.viewModel collectionAction];
        
        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
            
            if (error) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_tips_favorite_failed")];
            }
        };
        [wSelf isv_sendAction:action];
        
        return YES;
    }else if([action.actionType isEqualToString:KAShoppingCartProductDeleteAction]){
        [wSelf deleteButtonClick];
        return YES;
    }else if ([action.actionType isEqualToString:KAShoppingCartProductPropertyClickedAction]){
        [self showPropertyVC:controller];
        return YES;
    }else if ([action.actionType isEqualToString:KAShoppingCartProductPromosionClickedAction]){
//        [self showPromosionVC:controller];
        return YES;
    }else if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]){
        if (self.viewModel.isEdit) {
            //编辑态不能进商详
            return YES;
        }
        NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeProductDetail];
        NSString *router = [NSString stringWithFormat:@"router://%@/productDetailController",moduleName];
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        if (self.viewModel.skuId) {
            [params setObject:self.viewModel.skuId forKey:@"skuId"];
        }
        UIViewController *productDetailVC = [JDRouter openURL:router arg:params error:nil completion:nil];
        if (productDetailVC) {
            productDetailVC.hidesBottomBarWhenPushed = YES;
            [controller.navigationController pushViewController:productDetailVC animated:YES];
        }
        return YES;
    }
    return NO;
}
- (void)showPropertyVC:(UIViewController*)controller {
//    JDISVShoppingCartPropertyController *propertyController = [[JDISVShoppingCartPropertyController alloc] init];
    CARTSelectSpecController *propertyController = [[CARTSelectSpecController alloc] init];
    propertyController.skuId = self.viewModel.skuId;
    
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:propertyController presentingViewController:controller];
    presentationVC.type = KAFloatLayerTypeCustom;
    propertyController.transitioningDelegate = presentationVC;
    
    @weakify(self);
    __weak typeof(propertyController) weakPropertyController = propertyController;
    propertyController.didClickOKBtnBlock = ^(NSString *skuId){
        @strongify(self);
        [PlatformService showLoadingInView:weakPropertyController.view];
        JDCDISVAction *action = [self.viewModel changeProductActionWithSkuId:skuId];
        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
            [PlatformService dismissInView:weakPropertyController.view];
            if (error) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_failed_to_switch_products")];
            }else {
                [weakPropertyController dismissViewControllerAnimated:YES completion:nil];
                JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartReloadDataAction"];
                @strongify(self);
                [self isv_sendAction:action];
            }
        };
        [self isv_sendAction:action];
    };
    presentationVC.presentedView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [controller presentViewController:propertyController animated:YES completion:nil];
}

//-(void)showPromosionVC:(UIViewController*)controller{
//    JDISVShoppingCartDiscountDetailContrller* promosionController = [[JDISVShoppingCartDiscountDetailContrller alloc] init];
//    
//    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:promosionController presentingViewController:controller];
//    presentationVC.type = KAFloatLayerTypeCustom;
//    promosionController.transitioningDelegate = presentationVC;
//    promosionController.skuId = self.viewModel.skuId;
////    promosionController.promosions = self.viewModel.model.promosions;
//    promosionController.img = self.productImageView.image;
//    promosionController.Price = self.viewModel.price;
//    
//    @weakify(self);
//    __weak typeof(promosionController) weakPromosionController = promosionController;
//    promosionController.sureButtonCallBack = ^(NSString *newId,
//                                               NSString *newType,
//                                               NSString* oldId,
//                                               NSString* oldType){
//        
//        @strongify(self);
//        [PlatformService showLoadingInView:weakPromosionController.view];
//        JDCDISVAction *action =
//        [self.viewModel changePromotionActionWithNewId:newId
//                                           newItemType:newType
//                                                 oldId:oldId
//                                           oldItemType:oldType];
//        
//        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
//            [PlatformService dismissInView:weakPromosionController.view];
//            if (error) {
//                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_failed_to_switch_promotion")];
//            }else {
//                [weakPromosionController dismissViewControllerAnimated:YES completion:nil];
//                JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartReloadDataAction"];
//                @strongify(self);
//                [self isv_sendAction:action];
//            }
//        };
//        [self isv_sendAction:action];
//    };
//    presentationVC.presentedView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
//    [controller presentViewController:promosionController animated:YES completion:nil];
//}

#pragma mark - KAStepperDelegate
- (void)stepper:(KAStepper *)stepper changeOfCount:(NSInteger)count {
    if(stepper.tag != 0){
        if(self.viewModel.skuId.intValue != stepper.tag){
            return;
        }
    }
    JDCDISVAction *action = [self.viewModel changeProductCountActionWithCount:count];
    action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
        
        if (error) {
            [KAToast alert].config.renderW1(ShopingCartL(@"ka_cart_tips_action_failed")).jdcd_show();
        }
    };
    [self isv_sendAction:action];
}

- (void)stepper:(KAStepper *)stepper touchOffMaxLimit:(NSInteger)maxLimit {
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:[NSString stringWithFormat:ShopingCartL(@"ka_cart_purchase_up_to"),@(maxLimit)]];
}

- (void)stepper:(KAStepper *)stepper touchOffMinLimit:(NSInteger)minLimit {
    NSString* fmtStr = ShopingCartL(@"ka_cart_purchase_at_least");
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:[NSString stringWithFormat:fmtStr,@(minLimit)]];
}

- (void)stepper:(KAStepper *)stepper touchOffStockLimit:(NSInteger)stockLimit {
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:[NSString stringWithFormat:ShopingCartL(@"ka_cart_tips_stock_not_enough"),@(stockLimit)]];
}

- (void)leftStepperButtonClick {
    self.stepper.tag = self.viewModel.skuId.intValue;
    [self.stepper munisButtonClick:nil];
}

- (void)rightStepperButtonClick {
    self.stepper.tag = self.viewModel.skuId.intValue;
    [self.stepper plusButtonClick:nil];
}

- (void)deleteButtonClick {
    __weak typeof(self) wSelf = self;
    NSString* message = ShopingCartL(@"ka_cart_confirm_del_specific_item");
    NSString* cancel = ShopingCartL(@"ka_cart_keep_item");
    NSString* delete = ShopingCartL(@"ka_cart_remove_item");
    [KAAlert alert].config.renderW3a(message,NSTextAlignmentCenter).addLineAction(cancel, ^{
    }).addFillAction(delete, ^{
        JDCDISVAction *action = [wSelf.viewModel deleteAction];
        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
            if (error) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_remove_item_fail_message")];
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:ShopingCartL(@"ka_cart_remove_item_success_message")];
            }
        };
        [wSelf isv_sendAction:action];
    }).jdcd_show();
}

#pragma mark - getter

- (UIButton *)selectedButton {
    if (!_selectedButton) {
        _selectedButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectedButton addTarget:self action:@selector(selectedButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        
        [_selectedButton renderB7];
        
    }
    return _selectedButton;
}

- (UIView *)productView {
    if (!_productView) {
        _productView = [[UIView alloc] init];
        CGFloat r75 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R75"];
        _productView.layer.cornerRadius = r75;
        _productView.layer.masksToBounds = YES;
//        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        _productView.backgroundColor = UIColor.whiteColor;
    }
    return _productView;
}

- (UIImageView *)productImageView {
    if (!_productImageView) {
        _productImageView = [[UIImageView alloc] init];
        _productImageView.backgroundColor = UIColor.whiteColor;
    }
    return _productImageView;
}

- (UILabel *)storeLabel {
    if (!_storeLabel) {
        _storeLabel = [[UILabel alloc] init];
        _storeLabel.jdisv_backgroundColorPicker = JDISVColorPickerWithKeyAndAlpha(@"#C8", 0.65);
        _storeLabel.textColor = [UIColor whiteColor];
        _storeLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
        _storeLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _storeLabel;
}

-(KAShoppingSecKillView*)secKillView{
    if(!_secKillView){
        _secKillView = [[KAShoppingSecKillView  alloc] initWithFrame:CGRectZero];
        CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R60"];
        _secKillView.layer.cornerRadius = r60;
        _secKillView.layer.masksToBounds = YES;
        
    }
    return _secKillView;
}

- (UIButton *)storeButton {
    if (!_storeButton) {
        _storeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _storeButton.userInteractionEnabled = NO;
        
//        NSString* imgName = @"shopping_car_soldout_a";
//        NSString* lang = [NSString getKAUseLang];
//        if([lang isEqualToString:@"en"]){
//            imgName = @"shopping_car_soldout_e";
//        }else if([lang isEqualToString:@"ar"]){
//            imgName = @"shopping_car_soldout_a";
//        }else if([lang isEqualToString:@"zh-Hans"]){
//            imgName = @"shopping_car_soldout";
//        }
//        UIImage*img = [UIImage isv_shoppingCart_imageWithName:imgName];
//        [_storeButton setImage:img forState:UIControlStateNormal];
        [_storeButton setTitle:ShopingCartL(@"ka_cart_product_no_stock_short") forState:UIControlStateNormal];
        _storeButton.titleLabel.font = [UIFont systemFontOfSize:12];
        [_storeButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _storeButton.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
        _storeButton.titleLabel.numberOfLines = 0;
        _storeButton.titleLabel.textAlignment = NSTextAlignmentCenter;
        [_storeButton renderL8WithCornerRadius:30];
        [_storeButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        
    }
    return _storeButton;
}

- (UILabel *)productNameLabel {
    if (!_productNameLabel) {
        _productNameLabel = [[UILabel alloc] init];
        _productNameLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _productNameLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _productNameLabel.numberOfLines = 0;
        _productNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    }
    return _productNameLabel;
}

- (UIView *)propertyView {
    if (!_propertyView) {
        _propertyView = [[UIView alloc] init];
        _propertyView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C2");
        CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R60"];
        _propertyView.layer.cornerRadius = r60;
        _propertyView.layer.masksToBounds = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(propertyClicked)];
        [_propertyView addGestureRecognizer:tap];
    }
    return _propertyView;
}

- (UILabel *)propertyLabel {
    if (!_propertyLabel) {
        _propertyLabel = [[UILabel alloc] init];
        _propertyLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
        _propertyLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    }
    return _propertyLabel;
}

- (UIImageView *)tagImageView {
    if (!_tagImageView) {
        _tagImageView = [[UIImageView alloc] init];
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_SORT_ASCENDING imageSize:CGSizeMake(12, 12) color:color];
        _tagImageView.image = image;
    }
    return _tagImageView;
}

- (UIView *)promosionView {
    if (!_promosionView) {
        _promosionView = [[UIView alloc] init];
        _promosionView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C2");
        CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R60"];
        _promosionView.layer.cornerRadius = r60;
        _promosionView.layer.masksToBounds = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(promosionClicked)];
        [_promosionView addGestureRecognizer:tap];
    }
    return _promosionView;
}

- (UILabel *)promosionLabel {
    if (!_promosionLabel) {
        _promosionLabel = [[UILabel alloc] init];
        _promosionLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
        _promosionLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
        _promosionLabel.text = ShopingCartL(@"ka_cart_exchange_add_pay");
        _promosionLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _promosionLabel;
}

- (UIImageView *)tagpromosionImageView {
    if (!_tagpromosionImageView) {
        _tagpromosionImageView = [[UIImageView alloc] init];
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_SORT_ASCENDING imageSize:CGSizeMake(12, 12) color:color];
        _tagpromosionImageView.image = image;
    }
    return _tagpromosionImageView;
}

- (UILabel *)priceUtilLabel {
    if (!_priceUtilLabel) {
        _priceUtilLabel = [[UILabel alloc] init];
        _priceUtilLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C9");
        _priceUtilLabel.jdisv_fontPicker = JDISVJDFontPickerWithKey(@"#T9");
    }
    return _priceUtilLabel;
}

- (KAPriceLabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[KAPriceLabel alloc] init];
        _priceLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C9");
        _priceLabel.jdisv_fontPicker = JDISVJDFontPickerWithKey(@"#T5");
        _priceLabel.adjustsFontSizeToFitWidth = true;
        _priceLabel.minimumScaleFactor = 0.7;
        _priceLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _priceLabel;
}

- (KAStepper *)stepper {
    if (!_stepper) {
        _stepper = [[KAStepper alloc] init];
        _stepper.delegate = self;
        CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R60"];
        _stepper.cornerRedius = r60;
    }
    return _stepper;
}

- (UIView *)slashView {
    if (!_slashView) {
        _slashView = [[UIView alloc] init];
        _slashView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30" alpha:0.07];
        CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R60"];
        _slashView.layer.cornerRadius = r60;
        _slashView.layer.masksToBounds = YES;
    }
    return _slashView;
}

- (UIImageView *)slashTagImageView {
    if (!_slashTagImageView) {
        _slashTagImageView = [[UIImageView alloc] init];
        
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];
        _slashTagImageView.image = [UIImage ka_iconWithName:JDIF_ICON_BARGAIN imageSize:CGSizeMake(18, 18) color:color];
    }
    return _slashTagImageView;
}

- (UILabel *)slashNameLabel {
    if (!_slashNameLabel) {
        _slashNameLabel = [[UILabel alloc] init];
        _slashNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];//
        _slashNameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"];
        _slashNameLabel.text = ShopingCartL(@"ka_cart_can_price");
    }
    return _slashNameLabel;
}

- (UILabel *)preSaleLabel {
    if (!_preSaleLabel) {
        _preSaleLabel = [[UILabel alloc] init];
        _preSaleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        _preSaleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        _preSaleLabel.text = ShopingCartL(@"ka_cart_pre_pay_tip");
    }
    return _preSaleLabel;
}

- (UIButton *)leftStepperButton {
    if (!_leftStepperButton) {
        _leftStepperButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_leftStepperButton addTarget:self action:@selector(leftStepperButtonClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _leftStepperButton;
}

- (UIButton *)rightStepperButton {
    if (!_rightStepperButton) {
        _rightStepperButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_rightStepperButton addTarget:self action:@selector(rightStepperButtonClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _rightStepperButton;
}

- (UIButton *)deleteButton {
    if (!_deleteButton) {
        _deleteButton = [UIButton buttonWithType:UIButtonTypeSystem];
        UIImage *image = [UIImage systemImageNamed:@"trash"];
        [_deleteButton setImage:image forState:UIControlStateNormal];
        _deleteButton.tintColor = UIColor.grayColor;
        [_deleteButton addTarget:self action:@selector(deleteButtonClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _deleteButton;
}

- (UIView *)maskView {
    if (!_maskView) {
        _maskView = [[UIView alloc] init];
//        _maskView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.02];
        _maskView.backgroundColor = UIColor.clearColor;
    }
    return _maskView;
}

- (UIButton *)selectedMaskButton {
    if (!_selectedMaskButton) {
        _selectedMaskButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectedMaskButton addTarget:self action:@selector(selectedButtonClicked) forControlEvents:UIControlEventTouchUpInside];
    }
    return _selectedMaskButton;
}


- (UILabel *)limitInfoLabel {
    if (!_limitInfoLabel) {
        if([PlatformService isRTL]){
            _limitInfoLabel = [[UILabel alloc] init];
            _limitInfoLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
            _limitInfoLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T10"];
            _limitInfoLabel.text = ShopingCartL(@"ka_cart_pre_pay_tip");
            _limitInfoLabel.numberOfLines = 2;
            _limitInfoLabel.textAlignment = NSTextAlignmentRight;
        }else{
            _limitInfoLabel = [[UILabel alloc] init];
            _limitInfoLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
            _limitInfoLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T10"];
            _limitInfoLabel.text = ShopingCartL(@"ka_cart_pre_pay_tip");
            _limitInfoLabel.numberOfLines = 2;
            _limitInfoLabel.textAlignment = NSTextAlignmentLeft;
        }
    }
    return _limitInfoLabel;
}

@end


