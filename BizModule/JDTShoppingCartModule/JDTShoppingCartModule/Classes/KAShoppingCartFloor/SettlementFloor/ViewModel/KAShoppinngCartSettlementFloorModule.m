//
//  KAShoppinngCartSettlementFloorModule.m
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/23.
//  Copyright © 2021 罗静. All rights reserved.
//

#import "KAShoppinngCartSettlementFloorModule.h"

#import <JDISVYYModelModule/YYModel.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>

#import "JDISVShoppingCartTool.h"
#import "JDISVShoppingCartMainModel.h"

#import "KAShoppingCartSettlementModel.h"
#import "KAShoppingCartSettlementFloor.h"
#import "KAShoppingCartShopHeaderModel.h"
#import "JDISVShoppingCartMainModel.h"
#import "CARTMainModel.h"

JDISVRegisterFloorModule(KaCartToolBarFloor, KAShoppinngCartSettlementFloorModule)

@interface KAShoppinngCartSettlementFloorModule ()

@property (nonatomic, assign) BOOL noData;
@property (nonatomic,weak) JDISVFloorCommonModel* commonModel;
//@property (nonatomic,strong) NSDictionary* responseObject;
@property (nonatomic, strong) CARTMainModel *cartMainModel;
@end

@implementation KAShoppinngCartSettlementFloorModule

- (UIView *)floorView {
    KAShoppingCartSettlementFloor *settlement = [[KAShoppingCartSettlementFloor alloc] init];
    return settlement;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeBottomFixFloor;
}

- (CGFloat)floorHeight {

    if(self.edit){
        return 50;
    }
    return 100;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    
    @weakify(self)
    
    self.commonModel = commonModel;
    
    self.cartMainModel = [CARTMainModel yy_modelWithDictionary:data[@"data"]];
    
//    NSDictionary *responseObject = data[@"data"];
//    self.responseObject = responseObject;
//    NSNumber* jhGroupNum = data[@"ext"][@"jh_group"];
//    self.featureJHGroup = [jhGroupNum boolValue];
//    NSDictionary *dict = [self subtotalDataWithResponseObject:responseObject];
//    KAShoppingCartSettlementModel *model = [KAShoppingCartSettlementModel yy_modelWithDictionary:dict];
//    self.responseObject = responseObject;
//    if ([model.subtotalOther.enableCheck boolValue]) {
//        if ([model.subtotalOther.activeCheck boolValue]) {
    if (self.cartMainModel.subTotal.enableCheck.boolValue) {
        if (self.cartMainModel.subTotal.activeCheck.boolValue) {
            self.selectedStatus = KAShoppingCartSettlementBarSelectedStatusSelected;
        }else {
            self.selectedStatus = KAShoppingCartSettlementBarSelectedStatusUnSelected;
        }
    }else {
        self.selectedStatus = KAShoppingCartSettlementBarSelectedStatusDisenable;
    }
    
//    if (model.subtotalPrice.totalReprice) {
//        
//        self.price = model.subtotalPrice.priceShow;
//    }
//    
//    if (model.subtotalPrice.totalReprice) {
//        self.discount = model.subtotalPrice.totalReprice;
//    }
    
    self.price = self.cartMainModel.subTotal.totalRealAmount.stringValue;
    self.discount = self.cartMainModel.subTotal.totalDiscountAmount.stringValue;
    
    //操作参数
    [self selectedParamsWisthResponseObject:self.cartMainModel];

    BOOL inStock = [self stockWithResponseObject:self.cartMainModel];
    
    if (NO == inStock) {
        self.selectedStatus = KAShoppingCartSettlementBarSelectedStatusDisenable;
    }
    self.details = [self getCouponDetails:self.cartMainModel];
    
//    NSDictionary* numDic =  dict[@"HJM-D#subtotal&number"];
//    self.kindNum = [numDic[@"productKindNum"] intValue];
    self.kindNum = self.cartMainModel.subTotal.productKindNum.integerValue;
//    self.selectKindNum = [numDic[@"selectedProductKindNum"] intValue];
//    self.productNum = [numDic[@"productNum"] intValue];
    self.productNum = self.cartMainModel.subTotal.productNum.integerValue;
//    self.selectProductNum = [numDic[@"selectedProductNum"] intValue];
    self.selectProductNum = self.cartMainModel.subTotal.selectedProductNum.integerValue;
    
    [commonModel commonDataDidChange:^(NSDictionary * _Nonnull commonData) {
        @strongify(self);
        BOOL edit = [commonData[@"edit"] boolValue];
        if(!self.edit){
            self.edit = edit;
            [self editMode];
        }
        
    }];
}

-(BOOL)editModelProductSelected:(CARTMainSkuItemModel *)product
                     selectSkus:(NSArray*)selectSkus{
    BOOL isSelectAll = YES;
    
//    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:self.responseObject];
//    JDISVShoppingCartItemModel* superItem = [mainModel itemModelForKey:product.uuid].superItem;
//    NSDictionary* vs = superItem.info[@"HJM-D#virtualSuit&virtualSuit"];
//    if([superItem.type.nameSpace containsString:@"#cartView.promotion-M#"] &&
//       vs.count){
//        NSNumber* enableCheck = vs[@"enableCheck"];
//        if(enableCheck.boolValue){
//            NSNumber* acitveCheck = vs[@"activeCheck"];
//            if(!acitveCheck.boolValue){
//                isSelectAll = NO;
//            }
//        }else{
//            NSString* sku = vs[@"vSkuId"]?:@"";
//            if(![selectSkus containsObject:sku]){
//                isSelectAll =NO;
//            }
//        }
//    }else{
//        KAShoppingCartSettlementTempProductModel *productModel = [KAShoppingCartSettlementTempProductModel yy_modelWithDictionary:product.info];
        if([product.enableCheck boolValue]){
            if(![product.activeCheck boolValue]){
                isSelectAll = NO;
            }
        }else{
            if(![selectSkus containsObject:product.skuId]){
                isSelectAll = NO;
            }
        }
//    }
    return isSelectAll;
}

-(void)editMode{
    if(!self.isEdit)
        return;
//    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:self.responseObject];
    BOOL isSelectAll = YES;
    NSArray* groups = self.commonModel.commonData[@"editModeGroups"];
    NSArray* selectSkus = self.commonModel.commonData[@"editModeSkus"];
//    for (JDISVShoppingCartItemModel* subModel in mainModel.rootHierarchy.subList){
//        NSLog(@"%@ %@", subModel.uuid,subModel.type.nameSpace);
//        if([subModel.uuid isEqualToString:@"subtotal"]){
//            continue;
//        }else if([subModel.type.nameSpace containsString:@"#cartView.group-M#"]){
//            NSString* groupId = subModel.info[@"C-M#abstractGroupItem&core"][@"groupId"];
//            if(![groups containsObject:groupId]){
//                isSelectAll = NO;
//                break;
//            }
//        }else if(subModel.subList){
////            NSDictionary* suit =  subModel.info[@"HJM-D#virtualSuit&virtualSuit"];
////            if(suit){
////                NSNumber* enableCheck = subModel.info[@"HJM-D#virtualSuit&virtualSuit"][@"enableCheck"];
////                NSNumber* activeCheck = subModel.info[@"HJM-D#virtualSuit&virtualSuit"][@"activeCheck"];
////                NSString* sku = subModel.info[@"HJM-D#virtualSuit&virtualSuit"][@"vSkuId"]?:@"";
////                if(enableCheck.boolValue){
////                    if(!activeCheck.boolValue){
////                        isSelectAll = NO;
////                        break;
////                    }
////                }else{
////                    if(![selectSkus containsObject:sku]){
////                        isSelectAll = NO;
////                        break;
////                    }
////                }
////            }else{
//                if(![self editModelProductSelected:subModel
//                                        selectSkus:selectSkus]){
//                    isSelectAll = NO;
//                    break;
//                }
////            }
//        } else if([subModel.type.nameSpace containsString:@"#cartView.product-M#"]){
//            if(![self editModelProductSelected:subModel
//                                    selectSkus:selectSkus]){
//                isSelectAll = NO;
//                break;
//            }
//        }else {
//            NSLog(@"!!ignore type:%@",subModel.type.nameSpace);
//        }
//    }
    
    for (CARTMainVenderModel *vender in self.cartMainModel.venderList) {
        NSString *venderId = vender.vendorId.stringValue;
        if (![groups containsObject:venderId]) {
            isSelectAll = NO;
            break;
        }
        for (CARTMainSkuItemModel *skuItem in vender.skuItems) {
            if (![self editModelProductSelected:skuItem selectSkus:selectSkus]) {
                isSelectAll = NO;
                break;
            }
        }
    }
    
    if(isSelectAll){
        self.selectedStatus = KAShoppingCartSettlementBarSelectedStatusSelected;
    }else{
        self.selectedStatus = KAShoppingCartSettlementBarSelectedStatusUnSelected;
    }
    [self.cell floorDidLoad:self];
    
//    [self selectedParamsWisthResponseObject:self.responseObject];
    [self selectedParamsWisthResponseObject:self.cartMainModel];
}

//-(NSArray*)getCouponDetails:(NSDictionary*)responseObject{
//    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
//    NSMutableArray* details = [NSMutableArray array];
//    for(JDISVShoppingCartItemModel* model in mainModel.rootHierarchy.subList){
//        if([model.type.nameSpace containsString:@"cartView.subtotalDetail-M#subtotalDetailItem"]){
//            [details addObject:model];
//        }
//    }
//    [details sortUsingComparator:^NSComparisonResult(JDISVShoppingCartItemModel*  _Nonnull obj1, JDISVShoppingCartItemModel*  _Nonnull obj2) {
//        NSNumber* obj1seq = obj1.info[@"C-M#subtotalDetailItem&core"][@"seq"];
//        NSNumber* obj2seq = obj2.info[@"C-M#subtotalDetailItem&core"][@"seq"];
//        return [obj1seq compare:obj2seq];
//    }];
//    return [details copy];
//}

-(NSArray*)getCouponDetails:(CARTMainModel *)mainModel {
//    NSMutableArray* details = [NSMutableArray array];
//    for(JDISVShoppingCartItemModel* model in mainModel.rootHierarchy.subList){
//        if([model.type.nameSpace containsString:@"cartView.subtotalDetail-M#subtotalDetailItem"]){
//            [details addObject:model];
//        }
//    }
    NSMutableArray *details = [NSMutableArray arrayWithArray:mainModel.subTotalDetails];
    [details sortUsingComparator:^NSComparisonResult(CARTMainSubTotalDetailModel * _Nonnull obj1, CARTMainSubTotalDetailModel * _Nonnull obj2) {
        return [obj1.seq compare:obj2.seq];
    }];
//    [details sortUsingComparator:^NSComparisonResult(JDISVShoppingCartItemModel*  _Nonnull obj1, JDISVShoppingCartItemModel*  _Nonnull obj2) {
//        NSNumber* obj1seq = obj1.info[@"C-M#subtotalDetailItem&core"][@"seq"];
//        NSNumber* obj2seq = obj2.info[@"C-M#subtotalDetailItem&core"][@"seq"];
//        return [obj1seq compare:obj2seq];
//    }];
    return [details copy];
}

- (NSDictionary *)subtotalDataWithResponseObject:(NSDictionary *)responseObject {
    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
    NSString *nameSpace = @"core.trade-DM#cartView.subtotal-M#defaultSubtotalItem";
    JDISVShoppingCartItemModel *itemModel = [mainModel subItemsModelForNameSpace:nameSpace].firstObject;
    return itemModel ? itemModel.info : @{};
}

- (BOOL)stockWithResponseObject:(CARTMainModel *)mainModel {
//    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
    
//    NSString *nameSpace = @"core.trade-DM#cartView.product-M#productItem";
//    NSArray <JDISVShoppingCartItemModel *>*productItems = [mainModel subItemsModelForNameSpace:nameSpace];
    
//    for (JDISVShoppingCartItemModel *itemModel in productItems) {
//        
//        KAShoppingCartSettlementTempProductModel *productModel = [KAShoppingCartSettlementTempProductModel yy_modelWithDictionary:itemModel.info];
//        //只要一个商品有货就判定为有货
//        //0：有货 1：无货 2：预定
//        if (productModel.cartProductStockInfo.stockCode.integerValue != 1) {
//            return YES;
//        }
//    }
//    return NO;
    for (CARTMainVenderModel *vender in mainModel.venderList) {
        for (CARTMainSkuItemModel *skuItem in vender.skuItems) {
            if (skuItem.stockStatus.integerValue == 1) {
                return YES;
            }
        }
    }
    return NO;
}

-(void)selectedParamsWisthResponseObject:(CARTMainModel *)mainModel {
    
//    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
    self.cheapBuyNum = 0;
    NSMutableArray* prds = [NSMutableArray array];
    NSMutableArray* promotions = [NSMutableArray array];
    NSMutableArray* groupBySkus = [NSMutableArray array];
//    for (JDISVShoppingCartItemModel *obj in mainModel.rootHierarchy.subList) {
//        JDISVShoppingCartItemModel * promotionModel = obj;
//        if([promotionModel.type.nameSpace containsString:@"#cartView.subtotal-M#"]){
//            continue;
//        }
//        else if([promotionModel.type.nameSpace containsString:@"cartView.group-M#defaultGroupItem"]){
//            for(JDISVShoppingCartItemModel* p in promotionModel.subList){
//                [self dealPromotionAndProduct:p
//                                         prds:prds
//                                   promotions:promotions
//                                  groupBySkus:groupBySkus];
//            }
//        }else{
//            [self dealPromotionAndProduct:promotionModel
//                                     prds:prds
//                               promotions:promotions
//                              groupBySkus:groupBySkus];
//        }
//    }
    for (CARTMainVenderModel *vender in mainModel.venderList) {
        for (CARTMainSkuItemModel *skuItem in vender.skuItems) {
            [self dealPromotionAndProduct:skuItem prds:prds promotions:promotions groupBySkus:groupBySkus];
        }
    }
    self.productParams = prds;
    self.promotionParams = promotions;
    self.groupBySkus = [groupBySkus copy];
    NSLog(@"----product:%@",prds);
    NSLog(@"----promotion:%@",promotions);
    NSLog(@"----groupBySkus:%@",groupBySkus);
}

//-(void)dealVirtualSuitAndProduct:(JDISVShoppingCartItemModel*)promotionModel
//                            prds:(NSMutableArray*)prds
//                      promotions:(NSMutableArray*)promotions
//                     groupBySkus:(NSMutableArray*)groupBySkus{
//    NSNumber* enableCheck = promotionModel.info[@"HJM-D#virtualSuit&virtualSuit"][@"enableCheck"];
//    NSNumber* activeCheck = promotionModel.info[@"HJM-D#virtualSuit&virtualSuit"][@"activeCheck"];
//    BOOL select = enableCheck.boolValue && activeCheck.boolValue;
//    if(self.edit){ //编辑模式手动选择
//        NSArray* editModeSkus = self.commonModel.commonData[@"editModeSkus"];
//        NSString* sku = promotionModel.info[@"HJM-D#virtualSuit&virtualSuit"][@"vSkuId"]?:@"";
//        if([editModeSkus containsObject:sku]){
//            select = YES;
//        }
//    }
//    if(select){
//        NSString* promotionId = NSUUID.UUID.UUIDString;
//        NSMutableArray* promtionPrdis = [NSMutableArray array];
//        for(JDISVShoppingCartItemModel* prudoctModel in promotionModel.subList){
//            NSDictionary* prd =
//            [self shopProductParamsWithShopModel:prudoctModel
//                                      promotonId:promotionId
//                                  groupFirstItem:promotionModel.subList.firstObject
//                                            pids:promtionPrdis
//                                     groupBySkus:nil]; //虚拟组套子品不计入分组购买
//            if(prd){
//                [prds addObject:prd];
//            }
//        }
//        if(promtionPrdis.count){
//            NSDictionary* promotion  =
//            [self shopPromotionParamsWithShopModel:promotionModel
//                                        promotonId:promotionId
//                                              pids:promtionPrdis
//                                       groupBySkus:groupBySkus];
//            if(promotion){
//                [promotions addObject:promotion];
//            }
//        }
//    }
//}




-(void)dealPromotionAndProduct:(CARTMainSkuItemModel *)promotionModel
                          prds:(NSMutableArray*)prds
                    promotions:(NSMutableArray*)promotions
                   groupBySkus:(NSMutableArray*)groupBySkus{
//    if([promotionModel.type.nameSpace containsString:@"cartView.product-M#productItem"]){
        NSDictionary* prd = [self shopProductParamsWithShopModel:promotionModel promotonId:@"" groupFirstItem:nil pids:nil groupBySkus:groupBySkus];
        if(prd){
            [prds addObject:prd];
        }
//    }else{
//        NSDictionary* suit =  promotionModel.info[@"HJM-D#virtualSuit&virtualSuit"];
//        if(suit){
//            [self dealVirtualSuitAndProduct:promotionModel
//                                       prds:prds
//                                 promotions:promotions
//                                groupBySkus:groupBySkus];
//        }else{
//            NSString* promotionId = NSUUID.UUID.UUIDString;
//            NSMutableArray* promtionPrdis = [NSMutableArray array];
//            for(JDISVShoppingCartItemModel* prudoctModel in promotionModel.subList){
//                NSDictionary* prd = [self shopProductParamsWithShopModel:prudoctModel
//                                                              promotonId:promotionId
//                                                          groupFirstItem:promotionModel.subList.firstObject
//                                                                    pids:promtionPrdis
//                                                             groupBySkus:groupBySkus
//                ];
//                if(prd){
//                    [prds addObject:prd];
//                }
//            }
//            if(promtionPrdis.count){
//                NSDictionary* promotion  =
//                [self shopPromotionParamsWithShopModel:promotionModel
//                                            promotonId:promotionId
//                                                  pids:promtionPrdis
//                                           groupBySkus:groupBySkus];
//                if(promotion){
//                    [promotions addObject:promotion];
//                }
//            }
//        }
//    }
}

-(NSDictionary*)shopPromotionParamsWithShopModel:(JDISVShoppingCartItemModel*)promotionModel
                                      promotonId:(NSString*)proID
                                            pids:(NSArray*)prds
                                     groupBySkus:(NSMutableArray*)groupBySkus{
    
    if([promotionModel.type.nameSpace containsString:@"cartView.promotion-M"]){
        
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        NSDictionary* vsDic = [promotionModel.info jdcd_getDicElementForKey:@"HJM-D#virtualSuit&virtualSuit"];
        if(vsDic.count){
            params[@"id"] = proID;
            params[@"itemType"] = @(4);
            params[@"num"] = @(1);
            params[@"promotionId"] = vsDic[@"vSkuId"]?:@"";
            [groupBySkus addObject:vsDic[@"vSkuId"]?:@""];
            if(prds.count){
                params[@"childList"] = prds;
            }
        }else{
            params[@"id"] = proID;
            params[@"itemType"] = @(3);
            params[@"num"] = @(1);
            params[@"promotionId"] = promotionModel.info[@"C-M#promotionItem&core"][@"promotionId"]?:@"";
            if(prds.count){
                params[@"childList"] = prds;
            }
        }

        return params;
    }
    return nil;
}

- (NSDictionary *)shopProductParamsWithShopModel:(CARTMainSkuItemModel *)pModel
                                      promotonId:(NSString*)promotionId
                                  groupFirstItem:(CARTMainSkuItemModel *)firstItem
                                            pids:(NSMutableArray*)pids
                                     groupBySkus:(NSMutableArray*)groupBySkus{
    
//    if(![pModel.type.nameSpace containsString:@"cartView.product-M#productItem"]){
//        if([pModel.type.nameSpace containsString:@"cartView.appendant-M#promotionCheapBuyModel"]){
//            KAShoppingCartSettlementTempProductModel *productModel = [KAShoppingCartSettlementTempProductModel yy_modelWithDictionary:firstItem.info];
//            BOOL select = productModel.cartProductExtInfo.enableCheck.boolValue && productModel.productItemBasic.activeCheck.boolValue;
//            if(self.edit){ //编辑模式手动选择
//                NSArray* editModeSkus = self.commonModel.commonData[@"editModeSkus"];
//                NSString* sku = productModel.productItemCore.skuId?:@"";
//                if([editModeSkus containsObject:sku]){
//                    select = YES;
//                }
//            }
//            
//            if (select) {
//                self.cheapBuyNum ++;
//                NSDictionary* coreDic = pModel.info[@"C-M#abstractAppendantItem&core"];
//                               NSString* skuId = coreDic[@"id"];
//                if(skuId.length){
//                    [groupBySkus addObject:skuId];
//                }
//            }
//        }
//        return nil;
//    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
//    KAShoppingCartSettlementTempProductModel *productModel = [KAShoppingCartSettlementTempProductModel yy_modelWithDictionary:pModel.info];
//    BOOL select = productModel.cartProductExtInfo.enableCheck.boolValue && productModel.productItemBasic.activeCheck.boolValue;
    BOOL select = pModel.enableCheck.boolValue && pModel.activeCheck.boolValue;
    if(self.edit){ //编辑模式手动选择
        NSArray* selectSkus = self.commonModel.commonData[@"editModeSkus"];
        select = [self editModelProductSelected:pModel selectSkus:selectSkus];
    }
    if (select) {
        NSString* uuid = NSUUID.UUID.UUIDString;
        params[@"id"] = uuid;
        if(pids){
            [pids addObject:uuid];
        }
//        if(promotionId.length){
//            params[@"parentId"] = promotionId;
//        }
//        if (productModel.cartProductExtInfo.itemType) {
//            [params setObject:productModel.cartProductExtInfo.itemType forKey:@"itemType"];
//        }else{
//            [params setObject:@(1) forKey:@"itemType"];
//        }
        if (pModel.skuId) {
            [params setObject:pModel.skuId forKey:@"skuId"];
            [groupBySkus addObject:pModel.skuId];
        }
        if (pModel.num) {
            [params setObject:pModel.num forKey:@"num"];
        }
        return params;
    }
    return  nil;
}




-(RACSignal*)jhGroupCanBuy{
//    if(!self.featureJHGroup){
        return [[RACSignal return:@(YES)] deliverOnMainThread];
//    }

//    return [[RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        
//        NSArray* skus = self.groupBySkus;
//        NSDictionary* param = @{@"reqId":[NSUUID UUID].UUIDString,
//                                @"skuIds":skus,
//                                @"token":[PlatformService getUserA2]?:@""
//        };
//        
//        [PlatformService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"query_order_check" version:@"1.0" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            BOOL suc = [responseObject[@"success"] boolValue];
//            if(!suc){
//                [subscriber sendError:[NSError errorWithDomain:@"JNOS" code:-1 userInfo:@{NSLocalizedDescriptionKey:responseObject[@"message"]?:ShopingCartL(@"ka_cart_operation_failed")}]];
//            }else{
//                NSNumber* offValue = responseObject[@"data"][@"off"];
//                if(offValue.boolValue){
//                    [subscriber sendNext:@(YES)];
//                    [subscriber sendCompleted];
//                }else{
//                    [subscriber sendError:[NSError errorWithDomain:@"JNOS" code:-1 userInfo:@{NSLocalizedDescriptionKey:responseObject[@"data"][@"message"]?:ShopingCartL(@"ka_cart_operation_failed")}]];
//                }   
//            }
//        }];
//        return nil;
//    }] deliverOnMainThread];
}

- (void)userSelectAll:(NSString*)selected{
    self.commonModel.commonData[@"userSelecteAll"] = selected;
    [self.commonModel commonDataChange];
    self.commonModel.commonData[@"userSelecteAll"] = @"";
    [self editMode];
}

#pragma mark - action
- (JDCDISVAction *)selectAction {
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    
    NSString *selectTag = @"selectAll";
    if (self.selectedStatus == KAShoppingCartSettlementBarSelectedStatusSelected) {
        selectTag = @"unselectAll";
    }else if(self.selectedStatus == KAShoppingCartSettlementBarSelectedStatusUnSelected){
        selectTag = @"selectAll";
    }
    [result setObject:selectTag forKey:@"userActionId"];
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartSelectProductAction"];
    action.value = result;
    return action;
}

- (JDCDISVAction *)deleteAction {
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartDeleteAction"];
    if(self.promotionParams.count){
        action.value = @{@"operations":@{@"products":self.productParams?:@"",
                                         @"promotions":self.promotionParams
        }};
    }else{
        action.value = @{@"operations":@{@"products":self.productParams?:@""}};
    }
    return action;
}

- (JDCDISVAction *)collectionAction {
//    if(![self isContainVirtualSuit]){
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartCollectionProudctAction"];
        if(self.promotionParams.count){
            action.value = @{@"operations":@{@"products":self.productParams?:@"",
                                             @"promotions":self.promotionParams
            }};
        }else{
            action.value = @{@"operations":@{@"products":self.productParams?:@""}};
        }
        return action;
//    }
//    NSMutableArray* newPromotions = [self.promotionParams mutableCopy];
//    NSMutableArray* removeProductsUid= [NSMutableArray array];
//    NSMutableArray* removePromotions= [NSMutableArray array];
//    for(NSDictionary* dic in self.promotionParams){
//        NSNumber* itemType = dic[@"itemType"];
//        if(itemType.intValue == 4){
//            [removePromotions addObject:dic];
//            NSArray* child = dic[@"childList"];
//            if([child count]){
//                [removeProductsUid addObjectsFromArray:child];
//            }
//        }
//    }
//    
//    for(NSDictionary* promotion in removePromotions){
//        [newPromotions removeObject:promotion];
//    }
//    
//    NSMutableArray* newProducts = [NSMutableArray array];
//    
//    for(NSDictionary* pDic in self.productParams){
//        NSString* idStr = pDic[@"id"];
//        if(![removeProductsUid containsObject:idStr]){
//            [newProducts addObject:pDic];
//        }
//    }
//    
//    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartCollectionProudctAction"];
//    if(newPromotions.count){
//        action.value = @{@"operations":@{@"products":newProducts?:@"",
//                                         @"promotions":newPromotions
//        }};
//    }else{
//        action.value = @{@"operations":@{@"products":newProducts?:@""}};
//    }
//    return action;
}

//-(BOOL)isAllVirtualSuit{
//    if(self.productParams.count == 0)
//        return 0;
//    
//    BOOL isAllVS = YES;
//    NSInteger virturalProductNum = 0;
//    for (NSDictionary* dic in self.promotionParams){
//        NSNumber* itemType =   dic[@"itemType"];
//        if(itemType.intValue !=4 ){
//            isAllVS = NO;
//        }else{
//            NSArray* child = dic[@"childList"];
//            virturalProductNum += child.count;
//        }
//    }
//    if(isAllVS && virturalProductNum == self.productParams.count){
//        return YES;
//    }
//    return NO;
//}

//-(NSInteger)getVirtualSutiNum{
//    NSInteger num = 0;
//    for (NSDictionary* dic in self.promotionParams){
//        NSNumber* itemType =   dic[@"itemType"];
//        if(itemType.intValue == 4 ){
//            num++;
//        }
//    }
//    return num;
//}

//-(BOOL)isContainVirtualSuit{
//    for (NSDictionary* dic in self.promotionParams){
//        NSNumber* itemType =  dic[@"itemType"];
//        if(itemType.intValue ==4 ){
//            return YES;
//        }
//    }
//    return NO;
//}

//-(NSInteger)getShowNum{
//    return self.selectKindNum;
//    
//    NSInteger virturalProductNum = 0;
//    for (NSDictionary* dic in self.promotionParams){
//        NSNumber* itemType =   dic[@"itemType"];
//        if(itemType.intValue == 4 ){
//            NSArray* child = dic[@"childList"];
//            virturalProductNum += child.count;
//        }
//    }
//    NSInteger pNum = self.productParams.count;
////    NSInteger VSNum = [self getVirtualSutiNum];
//    NSInteger cheapBuyNum = self.cheapBuyNum;
//    NSInteger result = pNum - virturalProductNum + cheapBuyNum;
//    return result;
//    
//}
@end
