//
//  JDISVAddressTagView.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/14.
//

#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>

#import "JDISVAddressTagView.h"

#import "JDISVAddressModulUtil.h"

@interface JDISVAddressTagView ()<UITextFieldDelegate>
@property (weak, nonatomic) IBOutlet UIView *tagContainerView;
@property (weak, nonatomic) IBOutlet UILabel *tagLabel;

@property (weak, nonatomic) IBOutlet UIButton *homeTagButton;

@property (weak, nonatomic) IBOutlet UIButton *companyTagButton;


@property (weak, nonatomic) IBOutlet UIButton *schoolTagButton;

@property (weak, nonatomic) IBOutlet UIButton *addTagButton;

@property (weak, nonatomic) IBOutlet UIButton *sureButton;
@property (weak, nonatomic) IBOutlet UITextField *tagTextField;
@property (weak, nonatomic) IBOutlet UIButton *editButton;

@property (weak, nonatomic) IBOutlet UISwitch *adddressSwitch;

@property (weak, nonatomic) IBOutlet UIButton *customTagButton;
@property (weak, nonatomic) IBOutlet UIView *customTagView;
@property (weak, nonatomic) IBOutlet UIView *customTagEditView;
@property (weak, nonatomic) IBOutlet UIView *bottomContainerView;
@property (weak, nonatomic) IBOutlet UILabel *bottomDefaultAddressLabel;
@property (weak, nonatomic) IBOutlet UILabel *addressTiplabel;

@property (nonatomic, strong) UIView *editButtonLine;
@property (nonatomic, strong) UIButton *switchButton;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *customTagButtonWidth;

@end

@implementation JDISVAddressTagView

- (void)awakeFromNib{
    [super awakeFromNib];
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.bottomContainerView.backgroundColor = [UIColor clearColor];
    self.tagContainerView.backgroundColor = [UIColor clearColor];
    self.tagLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.tagLabel.text = AddressL(@"address_detail_tag_label");
    self.addressTiplabel.text = AddressL(@"address_detail_default_hint");
    self.isDefault = NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(textFieldDidChanged:) name:UITextFieldTextDidChangeNotification object:nil];
    NSArray<UIView *> *cornerViews = @[self.homeTagButton,
                                       self.companyTagButton,
                                       self.schoolTagButton,
                                       self.addTagButton,
                                       self.sureButton,
                                       self.customTagView,
                                       self.customTagEditView,
    ];
    [cornerViews enumerateObjectsUsingBlock:^(UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R61"];
        obj.clipsToBounds = YES;
    }];
    self.tagTextField.delegate = self;
    self.customTagView.hidden = YES;
    self.customTagEditView.hidden = YES;
    
    self.customTagEditView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    [self.editButton setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateNormal];
    [self.editButton setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"]];
    [self.editButton setTitle:AddressL(@"address_detail_tag_edit") forState:UIControlStateNormal];
    
    self.tagTextField.tintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    self.tagTextField.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    NSDictionary *attrs = @{NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightRegular],
                            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
                            NSParagraphStyleAttributeName:[[NSMutableParagraphStyle alloc] init]
    };
    self.tagTextField.attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:AddressL(@"address_detail_tag_hint") attributes:attrs];
    
    UIView *leftPaddingView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, self.tagTextField.frame.size.height)];
    self.tagTextField.leftView = leftPaddingView;
    self.tagTextField.leftViewMode = UITextFieldViewModeAlways;

    UIView *rightPaddingView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, self.tagTextField.frame.size.height)];
    self.tagTextField.rightView = rightPaddingView;
    self.tagTextField.rightViewMode = UITextFieldViewModeAlways;
    
    
    [self.addTagButton setImage:[UIImage ka_iconWithName:JDIF_ICON_PLUS imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]] forState:UIControlStateNormal];
    self.addTagButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    [self.sureButton setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateNormal];
    [self.sureButton setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"]];
    [self.sureButton setTitle:AddressL(@"address_comfir_button_title") forState:UIControlStateNormal];
    
    [self.companyTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.companyTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
    [self.companyTagButton setTitle:AddressL(@"address_company_tag") forState:UIControlStateNormal];
    [self.customTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.customTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
    [self.schoolTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.schoolTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
    [self.schoolTagButton setTitle:AddressL(@"address_school_tag") forState:UIControlStateNormal];
    [self.homeTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.homeTagButton setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
    [self.homeTagButton setTitle:AddressL(@"address_home_tag") forState:UIControlStateNormal];
    
    [self selectedTag:nil];
    
    self.bottomDefaultAddressLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.bottomDefaultAddressLabel.text = AddressL(@"address_detail_default_label");
    _editButtonLine = [UIView new];
    _editButtonLine.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    _editButtonLine.hidden = YES;
    [self addSubview:_editButtonLine];
    [_editButtonLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(1);
        make.top.leading.bottom.equalTo(self.editButton);
    }];
    
    self.adddressSwitch.hidden = YES;
    self.switchButton = [UIButton new];
    [self.switchButton renderB9];
    self.switchButton.jdisv_autoChangeUI_B9 = YES;
    [self.bottomContainerView addSubview:self.switchButton];
    [self.switchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(49);
        make.height.mas_equalTo(31);
        make.bottom.mas_equalTo(self.bottomContainerView).offset(-11);
        //make.centerY.equalTo(self.bottomContainerView);
        make.trailing.mas_equalTo(-18);
    }];
}

- (void)selectedTag:(UIButton *)sender {
    sender.selected = !sender.selected;
    NSArray<UIButton *> *buttons = @[self.homeTagButton,
                                     self.companyTagButton,
                                     self.schoolTagButton,
                                     self.customTagButton];
    
    [buttons enumerateObjectsUsingBlock:^(UIButton * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        //[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        [obj setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateNormal];
        [obj setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateSelected];
        obj.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
        
        if (obj != sender) {
            obj.selected = NO;
        }
        
        if (obj.selected) {
            obj.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
            if ([obj isEqual:self.customTagButton]) {
                self.editButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
                //self.editButtonLine.hidden = NO;
                [self.editButton setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateNormal];
            }
            [obj setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateSelected];
        }else{
            obj.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
            if ([obj isEqual:self.customTagButton]) {
                self.editButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
                //self.editButtonLine.hidden = YES;
                [self.editButton setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateNormal];
            }
        }
    }];
    
}

- (IBAction)homeTagButtonClick:(UIButton *)sender {
    [self selectedTag:sender];
}

- (IBAction)companyTagButtonClick:(UIButton *)sender {
    [self selectedTag:sender];
    
}

- (IBAction)schoolTagButtonClick:(UIButton *)sender {
    [self selectedTag:sender];
}

- (IBAction)addTagButtonClick:(UIButton *)sender {
    self.addTagButton.hidden = YES;
    self.customTagEditView.hidden = NO;
}

- (IBAction)sureButtonClick:(UIButton *)sender {
    self.customTagEditView.hidden = YES;
    self.customTagView.hidden = NO;
    self.editButtonLine.hidden = NO;
    [self.customTagButton setTitle:[NSString stringWithFormat:@"%@",self.tagTextField.text] forState:UIControlStateNormal];
    self.customTagButtonWidth.constant = [self.customTagButton.titleLabel.text boundingRectWithSize:CGSizeMake(400, 30) options:0 attributes:@{NSFontAttributeName:self.customTagButton.titleLabel.font} context:nil].size.width  + 30.f;
    [self.tagTextField resignFirstResponder];
    self.customTagButton.selected = NO;
    [self selectedTag:self.customTagButton];
}

- (IBAction)editButtonClick:(UIButton *)sender {
    self.customTagEditView.hidden = NO;
    self.customTagView.hidden = YES;
    self.editButtonLine.hidden = YES;
    [self.tagTextField becomeFirstResponder];
}

- (IBAction)customTagButtonClick:(UIButton *)sender {
    [self selectedTag:sender];
    [self endEditing:YES];
}
- (IBAction)switchtValueChanged:(UISwitch *)sender {
    
}



- (void)textFieldDidChanged:(NSNotification *)notification {
    
    UITextField *textField = (UITextField *)notification.object;
    if (textField != self.tagTextField) return;
    [self changeSureButtonState];
    // 需要限制的长度
    NSUInteger maxLength = 5;
    NSString *contentText = textField.text;
    UITextRange *selectedRange = [textField markedTextRange];
    
    NSInteger markedTextLength = [textField offsetFromPosition:selectedRange.start toPosition:selectedRange.end];
    
    if (markedTextLength == 0) {
        
        if (contentText.length > maxLength) {
            
            NSRange rangeRange = [contentText rangeOfComposedCharacterSequencesForRange:NSMakeRange(0, maxLength)];
            textField.text = [contentText substringWithRange:rangeRange];
        }
    }
    
}

/// MARK: - UITextFieldDelegate


- (void)textFieldDidBeginEditing:(UITextField *)textField{
    [self changeSureButtonState];
}

- (void)changeSureButtonState{
    NSString *colorKey = self.tagTextField.text.length>0 ? @"#C9" : @"#C4";
    [self.sureButton setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:colorKey]];
    self.sureButton.enabled = self.tagTextField.text.length>0;
}

/// MARK: - Accessor

- (NSNumber *)tagId {
    if (self.homeTagButton.isSelected) {
        return @1;
    } else if (self.schoolTagButton.isSelected) {
        return @2;
    } else if (self.companyTagButton.isSelected) {
        return @3;
    } else {
        return nil;
    }
}

- (void)setTagId:(NSNumber *)tagId {
    if ([tagId isEqual:@1]) {
        [self selectedTag:self.homeTagButton];
    } else if ([tagId isEqual:@2]) {
        [self selectedTag:self.schoolTagButton];
    } else if ([tagId isEqual:@3]) {
        [self selectedTag:self.companyTagButton];
    }
}

- (void)selectInnerTag:(NSString *)tag {
    // TODO:Juice 这里注意接口返回的多语言
    if ([tag isEqualToString:AddressL(@"address_home_tag")]) {
        [self selectedTag:self.homeTagButton];
    } else if ([tag isEqualToString:AddressL(@"address_school_tag")]) {
        [self selectedTag:self.schoolTagButton];
    } else if ([tag isEqualToString:AddressL(@"address_company_tag")]) {
        [self selectedTag:self.companyTagButton];
    }
}

- (void)selectCustomTag {
    [self selectedTag:self.customTagButton];
}

-  (BOOL)isCustom {
    return self.customTagButton.isSelected && self.tagTextField.text.length > 0;
}

- (NSString *)tagText {
    NSArray<UIButton *> *buttons = @[self.homeTagButton,
                                     self.companyTagButton,
                                     self.schoolTagButton,
                                     self.customTagButton];
    
    __block UIButton * selectButton;
    [buttons enumerateObjectsUsingBlock:^(UIButton * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop){
        if (obj.selected){
            selectButton = obj;
            *stop = true;
        }
    }];
     
    
    if(selectButton){
        return selectButton.currentTitle;
    } else {
        return nil;
    }
}

- (void)setTagText:(NSString *)tagText {
    self.tagTextField.text = tagText;
    if (tagText.length > 0) {
        [self addTagButtonClick:self.addTagButton];
        [self sureButtonClick:self.sureButton];
        [self customTagButtonClick:self.customTagButton];
    }
}

- (BOOL)isDefault {
//    return self.adddressSwitch.on;
    return self.switchButton.jdisv_selected_B9;
}

- (void)setIsDefault:(BOOL)isDefault {
//    _adddressSwitch.on = isDefault;
    self.switchButton.jdisv_selected_B9 = isDefault;
}
@end
