//
//  JDISVAddressCitiesViewController.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/22.
//

#import "JDISVAddressCitiesViewController.h"

#import "JDISVAddressModulUtil.h"
#import "JDISVAddressCitiesCell.h"

@import QMapKit;

@interface JDISVAddressCitiesViewController () <KANavigationBarSearchDelegate, QMSSearchDelegate, UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) QMSSearcher *searcher;

@property (nonatomic, assign) BOOL isLoading;
@property (nonatomic, assign) BOOL isLoaded;
@property (nonatomic, assign) BOOL isError;

@property (nonatomic, assign) CGFloat navigationBarHeight;

@property (nonatomic, copy) NSArray *displayCities;
@property (nonatomic, copy) NSString *searchName;

@property (nonatomic, strong) UILabel *currentCityLabel;

@end

@implementation JDISVAddressCitiesViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    //自定义导航
    self.navigationBarHeight = [UIWindow ka_uikit_navigationHeight];
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, self.navigationBarHeight)];
    [self.view addSubview:navigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    navigationBar
    .decorator
    .backgroundColor() //背景色
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.navigationController popViewControllerAnimated:YES];
    }) //返回按钮
    .searchTitle(AddressL(@"address_search_district_hint"), self)
    .render();
    
    // table view
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.rowHeight = UITableViewAutomaticDimension;
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVAddressCitiesCell" bundle:[NSBundle isv_address_bundle]] forCellReuseIdentifier:@"cell"];
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.navigationBarHeight);
        make.leading.trailing.bottom.equalTo(self.view);
    }];
    self.tableView.tableHeaderView = [self headerView];
    self.tableView.tableHeaderView = [self headerView];
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.currentCityLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.currentCityLabel.text = self.selectedCity;
    
    // search
    self.searcher = [QMSSearcher new];
    self.searcher.delegate = self;
    
    // 获取行政区列表
    if (!self.cityArray) {
        QMSDistrictListSearchOption *listOpt = [QMSDistrictListSearchOption new];
        [self.searcher searchWithDistrictListSearchOption:listOpt];
    }
    
    // 获取当前城市
    if ((!self.selectedCity ||[self.selectedCity isKindOfClass:[NSNull class]]) && self.userLocation) {
        [self fetchCurrentLocation];
    }
}

#pragma mark - action

// 将指定的坐标 转换成坐标所在地的位置文字描述
- (void)fetchCurrentLocation {
    if (self.userLocation == nil) {
        return;
    }
    QMSReverseGeoCodeSearchOption *option = [QMSReverseGeoCodeSearchOption new];
    [option setLocationWithCenterCoordinate:self.userLocation.coordinate];
    [self.searcher searchWithReverseGeoCodeSearchOption:option];
}

- (void)selectCurrentCity {
    if ([self.currentCityLabel.text isEqual:@"-"]) {
        return;
    }
    
    // 若用户点击重新定位后，currentCityLabel 和 selectedCity 可能不一致
    if (![self.currentCityLabel.text isEqual:(self.selectedCity)]) {
        if (self.displayCities && self.seletedCallback){
            QMSDistrictData *data;
            for (NSInteger i = 0; i < self.displayCities.count; i++) {
                data =  self.displayCities[i];
                if ([data.fullname isEqual:(self.currentCityLabel.text) ]) {
                    self.seletedCallback(data);
                    break;
                }
            }
        }
    }
    
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - UITableViewDelegate, UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.displayCities.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVAddressCitiesCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell"];
    QMSDistrictData *data = self.displayCities[indexPath.row];
    BOOL selected = [self.selectedCity isEqual:data.fullname];
    [cell configureWithCityName:data.name selected:selected];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    QMSDistrictData *data = self.displayCities[indexPath.row];
    if (self.seletedCallback) {
        self.seletedCallback(data);
    }
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - KANavigationBarSearchDelegate

- (void)searchView:(KANavigationBarSearchView *)searchView beginEditing:(UITextField *)textField {
    
}

- (void)searchView:(KANavigationBarSearchView *)searchView didChange:(UITextField *)textField {
    if (textField.markedTextRange == nil) {
        self.searchName = textField.text;
        if (self.searchName && self.searchName.length > 0) {
            self.displayCities = [self.cityArray jdcd_filter:^BOOL(QMSDistrictData * _Nonnull obj) {
                return [obj.name containsString:self.searchName];
            }];
        } else {
            self.displayCities = self.cityArray;
        }
        [self.tableView reloadData];
    }
}

- (void)searchView:(KANavigationBarSearchView *)searchView endEditing:(UITextField *)textField {
    
}

- (void)searchView:(KANavigationBarSearchView *)searchView clearClick:(UITextField *)textField {
    self.searchName = nil;
    self.displayCities = self.cityArray;
    [self.tableView reloadData];
}

#pragma mark - QMSSearchDelegate

- (void)searchWithSearchOption:(QMSSearchOption *)searchOption didFailWithError:(NSError *)error {
    if ([searchOption isKindOfClass:QMSDistrictBaseSearchOption.class]) {
        self.isError = YES;
        self.isLoading = NO;
        self.cityArray = nil;
        [self.tableView reloadData];
    }
}

- (void)searchWithDistrictSearchOption:(QMSDistrictBaseSearchOption *)districtSearchOption didRecevieResult:(QMSDistrictSearchResult *)districtSearchResult {
    self.isLoaded = YES;
    self.isLoading = NO;
    
    NSMutableArray *temp = [NSMutableArray array];
    
    if (districtSearchResult.result.count >= 1) {
        for (id addr in districtSearchResult.result.firstObject) {
            if ([addr isKindOfClass:QMSDistrictData.class]) {
                [temp addObject:addr];
            }
        }
    }
    
    if (districtSearchResult.result.count >= 2) {
        for (id addr in districtSearchResult.result[1]) {
            if ([addr isKindOfClass:QMSDistrictData.class]) {
                [temp addObject:addr];
            }
        }
    }
    
    self.cityArray = [temp copy];
    self.displayCities = self.cityArray;
    [self.tableView reloadData];
}

- (void)searchWithReverseGeoCodeSearchOption:(QMSReverseGeoCodeSearchOption *)reverseGeoCodeSearchOption didReceiveResult:(QMSReverseGeoCodeSearchResult *)reverseGeoCodeSearchResult {
    if(reverseGeoCodeSearchResult && reverseGeoCodeSearchResult.address_component){
        QMSAddressComponent *component = reverseGeoCodeSearchResult.address_component;
        self.currentCityLabel.text = component.city ?: @"-";
    }
}

#pragma mark - getter

- (UIView *)headerView {
    UIView *container = [UIView new];
    //title
    UILabel *title = [UILabel new];
    title.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    title.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    title.text = AddressL(@"address_map_current_location");
    [container addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(23);
        make.leading.mas_equalTo(18);
    }];
    //addr
    UILabel *addr = [UILabel new];
    addr.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
    addr.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    addr.text = @"-";
    [container addSubview:addr];
    [addr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(18);
        make.bottom.mas_equalTo(-18);
    }];
    self.currentCityLabel = addr;
    //btn
    UIButton *loctionBtn = [UIButton buttonWithType:(UIButtonTypeCustom)];
    loctionBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    [loctionBtn setTitle:AddressL(@"address_map_relocate") forState:(UIControlStateNormal)];
    [loctionBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C14"] forState:UIControlStateNormal];
    [container addSubview:loctionBtn];
    [loctionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(-18);
        make.centerY.equalTo(addr);
        make.height.mas_equalTo(20);
    }];
    [loctionBtn addTarget:self action:@selector(fetchCurrentLocation) forControlEvents:(UIControlEventTouchUpInside)];
    
    //current city btn
    UIButton *cityBtn = [UIButton buttonWithType:(UIButtonTypeCustom)];
    [cityBtn addTarget:self  action:@selector(selectCurrentCity) forControlEvents:(UIControlEventTouchUpInside)];
    [container addSubview:cityBtn];
    [cityBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(addr).insets(UIEdgeInsetsMake(-4, -4, -4, -4));
    }];
    [cityBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:UIControlStateNormal];
    container.bounds = CGRectMake(0, 0, [JDISVAddressModulUtil screenWidth], 96);
    return container;
}

@end
