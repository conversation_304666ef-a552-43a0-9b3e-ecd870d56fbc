//
//  JDISVAddressCitiesViewController.h
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/22.
//

#import <UIKit/UIKit.h>

#import <CoreLocation/CLLocation.h>
@import QMapKit;

NS_ASSUME_NONNULL_BEGIN

@interface JDISVAddressCitiesViewController : UIViewController

@property (nonatomic, strong) CLLocation *userLocation;
@property (nonatomic, copy) NSString *selectedCity;
@property (nonatomic, copy, nullable) NSArray *cityArray;

@property (nonatomic, copy) void(^seletedCallback)(QMSDistrictData *data);

@end

NS_ASSUME_NONNULL_END
