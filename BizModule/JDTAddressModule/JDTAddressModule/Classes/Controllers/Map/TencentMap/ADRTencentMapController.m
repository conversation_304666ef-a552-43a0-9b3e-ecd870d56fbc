//
//  ADRTencentMapController.m
//  JDTAddressModule
//
//  Created by lvchenzhu.1 on 2025/7/4.
//

#import "ADRTencentMapController.h"
#import "JDISVAddressLocationTitleView.h"
#import "JDISVAddressSearchCell.h"
#import "JDISVAddressModulUtil.h"
#import "JDISVAdressPub.h"
#import "JDISVAddressCitiesViewController.h"
#import "JDISVAddressSearchViewController.h"

@import QMapKit;

@interface ADRTencentMapController () <QMapViewDelegate, QMSSearchDelegate, UITableViewDataSource, UITableViewDelegate>

// UI组件
@property (nonatomic, strong) JDISVAddressLocationTitleView *titleView;
@property (nonatomic, strong) QMapView *mapView;
@property (nonatomic, strong) UIImageView *pinImageView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UILabel *tipLabel;

// 数据和状态
@property (nonatomic, strong) QMSSearcher *searcher;
@property (nonatomic, strong) NSArray<QMSReGeoCodePoi *> *addressArray;

@property (nonatomic, assign) BOOL isLoading;
@property (nonatomic, assign) BOOL isMapInitialized;

@end

@implementation ADRTencentMapController

#pragma mark - Lifecycle
- (void)viewDidLoad {
    [super viewDidLoad];

    [self setupUI];
    [self setupData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];

    // 隐藏导航栏
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

#pragma mark - Setup Methods

- (void)setupUI {
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

    [self setupTitleView];
    [self setupMapView];
    [self setupTableView];
    [self setupConstraints];
}

- (void)setupData {
    // 初始化搜索器
    self.searcher = [[QMSSearcher alloc] init];
    self.searcher.delegate = self;

    // 初始化数据
    self.addressArray = @[];
    self.isLoading = NO;
    self.isMapInitialized = NO;
}

- (void)setupTitleView {
    @weakify(self);
    self.titleView = [[JDISVAddressLocationTitleView alloc] init];
    self.titleView.cityName.text = self.cityName;
    
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    navigationBar.decorator.backgroundColor().backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        @strongify(self);
        [self.navigationController popViewControllerAnimated:YES];
    }).render();
    navigationBar.navigationItem.titleViewLayoutType = KANavigationBarTitleViewLayoutTypeFull;
    navigationBar.navigationItem.titleView = self.titleView;
    
    self.titleView.tapCityCallback = ^{
        @strongify(self);
        [self showCitySelection];
    };

    self.titleView.tapSearchCallback = ^{
        @strongify(self);
        [self showAddressSearch];
    };
}

- (void)setupMapView {
    // 创建地图视图
    self.mapView = [[QMapView alloc] init];
    self.mapView.delegate = self;
    self.mapView.showsUserLocation = YES;
    self.mapView.userTrackingMode = QUserTrackingModeNone;
    self.mapView.showsScale = NO;
    [self.mapView setLogoMargin:CGPointMake(6, 3) anchor:QMapLogoAnchorLeftBottom];
    [self.view addSubview:self.mapView];

    // 创建中心标记图标
    self.pinImageView = [[UIImageView alloc] init];
    self.pinImageView.image = [UIImage isv_address_imageNamed:@"jdisv_address_pin"];
    self.pinImageView.contentMode = UIViewContentModeScaleAspectFit;
    if (!self.pinImageView.image) {
        // 如果图片不存在，使用系统图标
        self.pinImageView.image = [UIImage systemImageNamed:@"mappin.circle.fill"];
        self.pinImageView.tintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10"];
    }
    [self.view addSubview:self.pinImageView];
}

- (void)setupTableView {
    // 创建表格视图
    self.tableView = [[UITableView alloc] init];
    self.tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.showsVerticalScrollIndicator = NO;
    [self.view addSubview:self.tableView];

    // 注册cell
    UINib *cellNib = [UINib nibWithNibName:@"JDISVAddressSearchCell" bundle:[NSBundle isv_address_bundle]];
    [self.tableView registerNib:cellNib forCellReuseIdentifier:@"JDISVAddressSearchCell"];

    // 创建提示标签
    self.tipLabel = [[UILabel alloc] init];
    self.tipLabel.text = AddressL(@"address_map_location_empty");
    self.tipLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.tipLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.tipLabel.textAlignment = NSTextAlignmentCenter;
    self.tipLabel.hidden = YES;
    [self.view addSubview:self.tipLabel];
}

- (void)setupConstraints {
    // 标题视图约束
    [self.titleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(8);
        make.left.equalTo(self.view).offset(48);
        make.right.equalTo(self.view).offset(-16);
        make.height.mas_equalTo(32);
    }];

    // 地图视图约束 - 占据上半部分
    [self.mapView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleView.mas_bottom).offset(8);
        make.left.right.equalTo(self.view);
        make.height.equalTo(self.view.mas_height).multipliedBy(0.4); // 占40%高度
    }];

    // 地图中心标记约束
    [self.pinImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.mapView);
        make.width.height.mas_equalTo(32);
    }];

    // 表格视图约束 - 占据下半部分
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mapView.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];

    // 提示标签约束
    [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.tableView);
        make.left.equalTo(self.tableView).offset(20);
        make.right.equalTo(self.tableView).offset(-20);
    }];
}

#pragma mark - Action Methods

- (void)showCitySelection {
    JDISVAddressCitiesViewController *citiesVC = [[JDISVAddressCitiesViewController alloc] init];
    citiesVC.selectedCity = self.cityName;
    citiesVC.userLocation = [[CLLocation alloc] initWithLatitude:self.mapView.centerCoordinate.latitude
                                                       longitude:self.mapView.centerCoordinate.longitude];

    @weakify(self);
    citiesVC.seletedCallback = ^(QMSDistrictData *data) {
        @strongify(self);
        self.cityName = data.fullname;
        self.titleView.cityName.text = data.name;
        // 城市切换后重新搜索当前位置的地址
        [self refreshAddressWithLocation: data.location getPoi:YES];
    };

    [self.navigationController pushViewController:citiesVC animated:YES];
}

- (void)showAddressSearch {
    JDISVAddressSearchViewController *searchVC = [[JDISVAddressSearchViewController alloc] init];
    searchVC.cityName = self.cityName;
    
    searchVC.centerLocation = [[CLLocation alloc] initWithLatitude:self.mapView.centerCoordinate.latitude
                                                         longitude:self.mapView.centerCoordinate.longitude];
    
    @weakify(self);
//    searchVC.selectionCallback = ^(QMSPoiData *data) {
//        @strongify(self);
//        // 搜索选择地址后，移动地图到该位置
//        [self refreshAddressWithLocation:data.location];
//        self.isAutoBack = YES;
//        //[self.mapView setCenterCoordinate:data.location animated:YES];
//        //[self.navigationController popViewControllerAnimated:YES];
//    };
    
    searchVC.addressSelectionCallback = ^(QMSReverseGeoCodeSearchResult * _Nonnull selectedAddress) {
        @strongify(self);
        if(self.addressSelectionCallback) {
            self.addressSelectionCallback(selectedAddress);
            [self.navigationController popViewControllerAnimated:YES];
        }
    };

    [self.navigationController pushViewController:searchVC animated:YES];
}

- (void)refreshAddressWithLocation:(CLLocationCoordinate2D)location getPoi: (BOOL) getPoi {
    if (!CLLocationCoordinate2DIsValid(location)) {
        return;
    }

    // 创建逆地理编码搜索选项
    QMSReverseGeoCodeSearchOption *option = [[QMSReverseGeoCodeSearchOption alloc] init];
    [option setLocationWithCenterCoordinate:location];
    option.poi_options = @"address_format=short;radius=5000;page_size=20;page_index=1;policy=5";
    option.get_poi = getPoi;

    self.isLoading = YES;
    [self updateTableViewState];

    [self.searcher searchWithReverseGeoCodeSearchOption:option];
}

- (void)updateTableViewState {
    if (self.isLoading) {
        self.tipLabel.text = AddressL(@"address_select_city_locate_loading");
        self.tipLabel.hidden = NO;
    } else if (self.addressArray.count == 0) {
        self.tipLabel.text = AddressL(@"address_map_location_empty");
        self.tipLabel.hidden = NO;
    } else {
        self.tipLabel.hidden = YES;
    }

    [self.tableView reloadData];
}

#pragma mark - QMapViewDelegate

- (void)mapViewInitComplete:(QMapView *)mapView {
    self.isMapInitialized = YES;

    // 地图初始化完成后，开始搜索当前位置的地址
    [self refreshAddressWithLocation:mapView.centerCoordinate getPoi:YES];
}

- (void)mapViewDidFailLoadingMap:(QMapView *)mapView withError:(NSError *)error {
    NSLog(@"地图加载失败: %@", error.localizedDescription);
    self.tipLabel.text = AddressL(@"address_load_failed");
    self.tipLabel.hidden = NO;
}

- (void)mapView:(QMapView *)mapView regionDidChangeAnimated:(BOOL)animated gesture:(BOOL)bGesture {
    // 只有用户手势操作才触发地址搜索
    if (bGesture && self.isMapInitialized) {
        [self refreshAddressWithLocation:mapView.centerCoordinate getPoi:YES];
    }
}

- (void)mapView:(QMapView *)mapView didUpdateUserLocation:(QUserLocation *)userLocation fromHeading:(BOOL)fromHeading {
    // 用户位置更新时的处理
    if (userLocation.location && !self.isMapInitialized) {
        // 首次获取用户位置时，移动地图到用户位置
        [mapView setCenterCoordinate:userLocation.location.coordinate animated:YES];
    }
}

#pragma mark - QMSSearchDelegate

- (void)searchWithSearchOption:(QMSSearchOption *)searchOption didFailWithError:(NSError *)error {
    NSLog(@"地址搜索失败: %@", error.localizedDescription);

    self.isLoading = NO;
    self.addressArray = @[];
    [self updateTableViewState];
}

- (void)searchWithReverseGeoCodeSearchOption:(QMSReverseGeoCodeSearchOption *)reverseGeoCodeSearchOption
                              didReceiveResult:(QMSReverseGeoCodeSearchResult *)reverseGeoCodeSearchResult {
    self.isLoading = NO;

    if (reverseGeoCodeSearchResult && reverseGeoCodeSearchOption && reverseGeoCodeSearchOption.get_poi == NO){
        // 执行选择回调
        if (self.addressSelectionCallback) {
            self.addressSelectionCallback(reverseGeoCodeSearchResult);
        }
        
        [self.navigationController popViewControllerAnimated:YES];
        return;
    }
        
    
    if (reverseGeoCodeSearchResult && reverseGeoCodeSearchResult.poisArray.count > 0) {
        self.addressArray = reverseGeoCodeSearchResult.poisArray;
    } else {
        self.addressArray = @[];
    }

    if (reverseGeoCodeSearchResult.address_component && self.cityName.length == 0) {
        self.cityName = reverseGeoCodeSearchResult.address_component.city;
        self.titleView.cityName.text = reverseGeoCodeSearchResult.address_component.city;
    }

    [self updateTableViewState];
}

- (void)searchWithDistrictSearchOption:(QMSDistrictBaseSearchOption *)districtSearchOption didRecevieResult:(QMSDistrictSearchResult *)districtSearchResult {
    NSLog(@"查询所有省市");
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.addressArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVAddressSearchCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVAddressSearchCell" forIndexPath:indexPath];

    if (indexPath.row < self.addressArray.count) {
        QMSReGeoCodePoi *poi = self.addressArray[indexPath.row];

        // 设置地址标题
        cell.addrTitle.text = poi.title ?: @"";

        // 设置地址详情（包含距离信息）
        NSString *distance = @"";
        if (poi._distance > 0) {
            if (poi._distance < 1000) {
                distance = [NSString stringWithFormat:@"%.0fm", poi._distance];
            } else {
                distance = [NSString stringWithFormat:@"%.1fkm", poi._distance / 1000.0];
            }
        }

        NSString *address = poi.address ?: @"";
        if (distance.length > 0 && address.length > 0) {
            cell.addrDetail.text = [NSString stringWithFormat:@"%@ | %@", distance, address];
        } else if (address.length > 0) {
            cell.addrDetail.text = address;
        } else {
            cell.addrDetail.text = distance;
        }

        // 设置分隔线
        [cell showSeparator:(indexPath.row < self.addressArray.count - 1)];
    }

    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 80.0; // 与设计图中的地址卡片高度一致
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    if (indexPath.row < self.addressArray.count) {
        QMSReGeoCodePoi *selectedPoi = self.addressArray[indexPath.row];

        [self refreshAddressWithLocation:selectedPoi.location getPoi:NO];
//        // 执行选择回调
//        if (self.addressSelectionCallback) {
//            self.addressSelectionCallback(selectedPoi, self.currentTown);
//        }
//
//        // 返回上一页
//        [self.navigationController popViewControllerAnimated:YES];
    }
}

#pragma mark - Memory Management

- (void)dealloc {
    self.mapView.delegate = nil;
    self.searcher.delegate = nil;
}

@end
