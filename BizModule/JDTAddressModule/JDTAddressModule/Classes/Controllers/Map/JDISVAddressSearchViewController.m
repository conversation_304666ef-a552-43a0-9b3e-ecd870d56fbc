//
//  JDISVAddressSearchViewController.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/22.
//

#import "JDISVAddressSearchViewController.h"
#import "JDISVAddressModulUtil.h"
#import "JDISVAddressSearchCell.h"
#import "JDISVAddAddressViewController.h"

@import QMapKit;

@interface JDISVAddressSearchViewController () <KANavigationBarSearchDelegate, QMSSearchDelegate, UITableViewDelegate, UITableViewDataSource, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, copy) NSArray *addrArray;

@property (nonatomic, assign) CGFloat navigationBarHeight;

@property (nonatomic, strong) QMSSearcher *searcher;
@property (nonatomic, copy) NSString *searchName;

@property (nonatomic, assign) BOOL isLoading;
@property (nonatomic, assign) BOOL isLoaded;
@property (nonatomic, assign) BOOL isError;

@end

@implementation JDISVAddressSearchViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    //自定义导航
    self.navigationBarHeight = [UIWindow ka_uikit_navigationHeight];
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, self.navigationBarHeight)];
    [self.view addSubview:navigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    navigationBar
    .decorator
    .backgroundColor() //背景色
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.navigationController popViewControllerAnimated:YES];
    }) //返回按钮
    .searchTitle(AddressL(@"address_map_search_hint"), self)
    .render();
    
    // table view
    UIView *container = [UIView new];
    container.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.emptyDataSetSource = self;
    self.tableView.emptyDataSetDelegate = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.rowHeight = UITableViewAutomaticDimension;
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVAddressSearchCell" bundle:[NSBundle isv_address_bundle]] forCellReuseIdentifier:@"cell"];
    [container addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(container);
    }];
    
    [self.view addSubview:container];
    [container mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.navigationBarHeight);
        make.leading.trailing.bottom.equalTo(self.view);
    }];
    
    CGFloat radius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R30"];
    container.layer.masksToBounds = YES;
    
    CGFloat contentWidth = [JDISVAddressModulUtil screenWidth];
    CGFloat contentHeight = [JDISVAddressModulUtil screenHeight] - [UIWindow ka_uikit_navigationHeight];
    [container jdcd_addRoundedCorners:(UIRectCornerTopLeft | UIRectCornerTopRight) withRadii:(CGSizeMake(radius, radius)) viewRect:CGRectMake(0, 0, contentWidth, contentHeight)];
    
    self.searcher = [QMSSearcher new];
    self.searcher.delegate = self;
    [self.tableView reloadData];
}

#pragma mark - action

// 基于用户的输入进行位置搜索
- (void)searchForAddressName {
    if (self.searchName && self.searchName.length > 0) {
        QMSPoiSearchOption *poiOpt = [QMSPoiSearchOption new];
        [poiOpt setKeyword:self.searchName];
        poiOpt.page_size = 20;
        [poiOpt setBoundaryByRegionWithCityName:self.cityName autoExtend:YES center: self.centerLocation.coordinate];
        [self.searcher searchWithPoiSearchOption:poiOpt];
        
        QMSSuggestionSearchOption *opt = [QMSSuggestionSearchOption new];
        opt.keyword = self.searchName;
        opt.region = self.cityName;
        opt.page_size = @20;
        [opt setLocationWithCoordinate:self.centerLocation.coordinate];
        [self.searcher searchWithSuggestionSearchOption:opt];
    }
}

// 将指定的坐标 转换成坐标所在地的位置文字描述
- (void)refreshAddressWithLocation:(CLLocationCoordinate2D)location {
    if (!CLLocationCoordinate2DIsValid(location)) {
        return;
    }

    // 创建逆地理编码搜索选项
    QMSReverseGeoCodeSearchOption *option = [[QMSReverseGeoCodeSearchOption alloc] init];
    [option setLocationWithCenterCoordinate:location];
    option.poi_options = @"address_format=short;radius=5000;page_size=20;page_index=1;policy=5";
    option.get_poi = NO;
    
    self.isLoading = YES;
    [self.searcher searchWithReverseGeoCodeSearchOption:option];
}

#pragma mark - QMSSearchDelegate

- (void)searchWithSearchOption:(QMSSearchOption *)searchOption didFailWithError:(NSError *)error {

}

- (void)searchWithPoiSearchOption:(QMSPoiSearchOption *)poiSearchOption didReceiveResult:(QMSPoiSearchResult *)poiSearchResult {
    self.addrArray = poiSearchResult.dataArray;
    [self.tableView reloadData];
}

- (void)searchWithSuggestionSearchOption:(QMSSuggestionSearchOption *)suggestionSearchOption didReceiveResult:(QMSSuggestionResult *)suggestionSearchResult {
    self.addrArray = suggestionSearchResult.dataArray;
    [self.tableView reloadData];
}

- (void)searchWithReverseGeoCodeSearchOption:(QMSReverseGeoCodeSearchOption *)reverseGeoCodeSearchOption
                              didReceiveResult:(QMSReverseGeoCodeSearchResult *)reverseGeoCodeSearchResult {
    self.isLoading = NO;

    if (reverseGeoCodeSearchResult && self.addressSelectionCallback) {
        self.addressSelectionCallback(reverseGeoCodeSearchResult);
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        // ？？ 需要添加提示语
    }
}

#pragma mark - UITableViewDelegate, UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.addrArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVAddressSearchCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell"];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    QMSSuggestionPoiData *data = self.addrArray[indexPath.row];
    NSString *title = data.title;
    NSString *detail = data.address;
    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:title attributes:@{ NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"], NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)],NSParagraphStyleAttributeName:[NSMutableParagraphStyle new] }];
    if ([data.title containsString:self.searchName]) {
        [attr addAttributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"] } range:[data.title rangeOfString:self.searchName]];
    }
    cell.addrTitle.attributedText = attr;
    cell.addrDetail.text = detail;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    QMSPoiData *data = self.addrArray[indexPath.row];
    if (self.addressSelectionCallback) {
        [self refreshAddressWithLocation: data.location];
    } else {
        JDISVAddAddressViewController *addAddr = [[JDISVAddAddressViewController alloc] init];
        //addAddr.location = data.location;
        [self.navigationController pushViewController:addAddr animated:YES];
    }
}

#pragma mark - KANavigationBarSearchDelegate

- (void)searchView:(KANavigationBarSearchView *)searchView beginEditing:(UITextField *)textField {
    
}

- (void)searchView:(KANavigationBarSearchView *)searchView didChange:(UITextField *)textField {
    if (textField.markedTextRange == nil) {
        self.searchName = textField.text;
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(searchForAddressName) object:nil];
        [self performSelector:@selector(searchForAddressName) withObject:nil afterDelay:0.5];
    } else {
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(searchForAddressName) object:nil];
        self.searchName = nil;
        self.addrArray = nil;
        [self.tableView reloadData];
    }
}

- (void)searchView:(KANavigationBarSearchView *)searchView endEditing:(UITextField *)textField {
    
}

- (void)searchView:(KANavigationBarSearchView *)searchView clearClick:(UITextField *)textField {
    self.searchName = nil;
    self.addrArray = nil;
    [self.tableView reloadData];
}

#pragma mark - DZNEmptyDataSetSource, DZNEmptyDataSetDelegate

- (UIView *)customViewForEmptyDataSet:(UIScrollView *)scrollView {
    {
        for (UIView *subView in scrollView.subviews) {
            if ([NSStringFromClass([subView class]) isEqualToString:@"DZNEmptyDataSetView"]) {
                
                CGRect frame = subView.frame;
                frame.origin.y = 0;
                subView.frame = frame;
                
                UIView *view = [subView valueForKey:@"contentView"];
                if (view) {
                    [subView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|[contentView]|" options:0 metrics:nil views:@{@"contentView":view}]];
                }
            }
        }
    }
    UIView *container = [UIView new];
    UIView *errorView;
    if (self.searchName && self.searchName.length > 0) {
        KAEmptyView *empty = [[KAEmptyView alloc] initWithFrame:(CGRectZero) type:(KAEmptyViewTypeNotAction)];
        empty.coverImage = [JDISV_RESOURCE_MANAGER imageWithImageType:(JDISVImageTypeNoDataSearchResult)];
        empty.decrible = AddressL(@"address_map_search_empty");
        errorView = empty;
    } else {
        KAEmptyView *empty = [[KAEmptyView alloc] initWithFrame:(CGRectZero) type:(KAEmptyViewTypeNotAction)];
        empty.coverImage = [JDISV_RESOURCE_MANAGER imageWithImageType:(JDISVImageTypeNoDataSearchResult)];
        empty.decrible = AddressL(@"address_search_key_empty_tip");
        errorView = empty;
        empty.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    
    [container addSubview:errorView];
    [errorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(container);
    }];
    return container;
}

@end
