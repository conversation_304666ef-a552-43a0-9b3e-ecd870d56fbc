//
//  JDISVAddAddressViewController.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/12.
//


#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <CoreLocation/CLLocation.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>
#import <JDISVThemeModule/UIButton+JDISVTheme.h>
#import <JDISVPermissionsModule/JDISVPermissionsModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVQQLocService.h"
#import "JDISVLocationManager.h"
#import "JDISVAddressService.h"
#import "JDISVAddAddressViewController.h"
#import "JDISVAddressModulUtil.h"
#import "JDISVAddressTagView.h"
#import "JDISVAdressInfoView.h"
#import "JDISVAddressCascaderViewController.h"
#import "JDISVAddressFullCascaderViewController.h"
#import "JDISVAddAddressViewModel.h"
#import "ADRTencentMapController.h"
#import "JDISVAddressService.h"
#import "JDTAreaModel.h"

@interface JDISVAddAddressViewController ()<JDISVAdressInfoViewDelegate>

@property (strong, nonatomic)  UIScrollView *scrollView;

@property (weak, nonatomic) IBOutlet UIButton *sureButton;

@property (nonatomic, strong,nullable) JDISVAdressInfoView *infoView;

@property (nonatomic, strong,nullable) JDISVAddressTagView *tagView;

@property (nonatomic, strong) JDISVAddAddressViewModel *viewModel;

@property (nonatomic, assign) CLLocationCoordinate2D QQlocation;

@property (nonatomic, assign) BOOL keyboardEnable;
@property (nonatomic, assign) BOOL keyboardEnableAutoToolbar;
@property (nonatomic, assign) BOOL keyboardShouldResignOnTouchOutside;

@end

@implementation JDISVAddAddressViewController

- (instancetype)init {
    self = [super initWithNibName:@"JDISVAddAddressViewController" bundle:[NSBundle isv_address_bundle]];
    if (self) {
        _viewModel = [[JDISVAddAddressViewModel alloc] init];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    [self setupViews];
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    navigationBar
    .decorator
    .backgroundColor() //背景色
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.navigationController popViewControllerAnimated:YES];
    }) //返回按钮
    .title(AddressL(@"address_list_add"), NSTextAlignmentCenter) //标题
    .render();
    
//    self.hideTopTip = YES;
//    self.hideTopLocation = YES;
//    @weakify(self)
//    [JDCDPermission authorizeWithType:JDCDPermissionType_Location completion:^(BOOL granted, BOOL firstTime) {
//        @strongify(self)
//        self.hideTopLocation = !granted;
//        [[JDISVQQLocService inst] getLoc:self callBack:^(NSDictionary*  _Nullable object) {
//            if([object isKindOfClass:NSDictionary.class]){
////                NSValue *value = object[@"coor"];
////                self.QQlocation = [value coordinateValue];
//                [[[JDISVAddressService shareInstance] reverseGeoCodeWithLocation:self.QQlocation] subscribeNext:^(JDISVAddressResReverseCodeItem * _Nullable x) {
//                    @strongify(self)
//                    self.reverseItem = x;
////                    self.detailLabel.text = x.addressDetail;
//                    NSString* province = x.provinceName;
//                    NSString* city = x.cityName;
//                    NSString* dis = x.districtName;
//                    NSString* street = x.townName;
//                    NSString* addr = [NSString stringWithFormat:@"%@%@%@%@",province?:@"",city?:@""
//                                      ,dis?:@""
//                                      ,street?:@""];
////                    self.proviceLabel.text = addr;
//                } error:^(NSError * _Nullable error) {
//                    
//                }];
//            }
//        }];
//    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.navigationController setNavigationBarHidden:YES];
    
    [self storeIQKeyboardSetting];
    [self setIQKeyboardSetting];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self restoreIQKeyboardSetting];
}

- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];
    
    CGFloat r1 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    CGFloat r2 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R2"];
    [self.infoView jdisv_addCorners:JDISVCornersMake(r1, r1, r2, r2) rect:self.infoView.bounds];
    [self.tagView jdisv_addCorners:JDISVCornersMake(r1, r1, r2, r2) rect:self.tagView.bounds];
}

- (void)setupViews {
    @weakify(self);
    self.title = AddressL(@"address_detail_title_add");
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo([UIWindow ka_uikit_navigationHeight]);
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(self.sureButton.mas_top).offset(-5);
    }];
    self.scrollView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
        
    [self.scrollView addSubview:self.infoView];
    
    self.infoView.detailAddrEditBegin = ^{
        @strongify(self)
        [self.infoView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(240);
        }];
    };

    [self.scrollView addSubview:self.tagView];
   
    [self.sureButton renderB1];
    self.scrollView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    self.sureButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium];
    [self.sureButton setTitle:AddressL(@"address_comfir_button_title") forState:UIControlStateNormal];
    RACSignal* merge = [RACSignal merge:@[/*RACObserve(self, hideTopTip),*/
//                                          RACObserve(self, hideTopLocation),
                                          RACObserve(self.infoView, currentHeight)]];
    
    [merge subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        [self rebuidMas];
    }];
}

- (IBAction)sureButtonClick:(UIButton *)sender {
    self.viewModel.name = self.infoView.name;
    self.viewModel.phone = self.infoView.phone;
    
    self.viewModel.detail = self.infoView.detail;
    
    if (self.tagView.isCustom) {
        self.viewModel.tagSource = @2;
    } else {
        self.viewModel.tagSource = @1;
    }
    self.viewModel.tag = self.tagView.tagText;
    self.viewModel.isDefault = self.tagView.isDefault;
    
    
    if (self.viewModel.name.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_name_hint")];
        return;
    }
    
    NSMutableCharacterSet *characterSet = [NSMutableCharacterSet alphanumericCharacterSet];
    [characterSet addCharactersInRange:NSMakeRange(19968, (40869-19968))];
    NSRange nameRange = [self.viewModel.name rangeOfCharacterFromSet:[characterSet invertedSet]];
    BOOL invalidateName = nameRange.length > 0;
    if (self.viewModel.name.length > 20 || invalidateName) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_name_illegal")];
        return;
    }
    
    if (self.viewModel.phone.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_phone_empty")];
        return;
    }
    
    NSRange phoneRange = [self.viewModel.phone rangeOfCharacterFromSet:[[NSCharacterSet characterSetWithCharactersInString:@"0123456789"] invertedSet]];
    BOOL invalidatePhone = phoneRange.length > 0;
    if (self.viewModel.phone.length != 11 || invalidatePhone) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_phone_illegal")];
        [self.infoView markPhoneRed];
        return;
    }
    
    if (self.infoView.area.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_title_select_area")];
        return;
    }
    
    if (self.infoView.detail.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_input_more")];
        return;
    }
    [self userSubmitAddress];
}

-(void)userSubmitAddress{
    @weakify(self);
    [PlatformService showLoadingInView:self.view];
    [self.viewModel addressAddWithBlock:^(NSNumber *addrId, NSString *msg) {
        @strongify(self)
    
        if (addrId) {
            if (self.addressAddSuccessCallback) {
                self.addressAddSuccessCallback(addrId);
            }
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:AddressL(@"address_add_address_success")];
            [self.navigationController popViewControllerAnimated:YES];
        } else {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:msg ?: AddressL(@"address_add_address_error")];
        }
        [PlatformService dismissInView:self.view];
    }];
}

-(void)userNotUserTraslateAddr{
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_complete_dialog_hint")];
    @weakify(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @strongify(self)
        [self openFullCascader:NO];
    });
}

/// MARK: - JDISVAdressInfoViewDelegate
- (void)locationButtonClick:(UIButton *)sender {
    [self.view endEditing:YES];
    @weakify(self)
    JDISVLocationManager *manager = [JDISVLocationManager shareInstance];
    __block BOOL isShow = NO;
    [manager GetLocationPermissionVerifactionWithController:self completion:^(JDISVLocationPermissionStatus permissionStatus) {
        if (permissionStatus == JDISVLocationPermissionStatusAuthorizedAlways || permissionStatus == JDISVLocationPermissionStatusAuthorizedWhenInUse ) {
            @strongify(self)
            if(isShow)
                return;
            isShow = YES;
//            JDISVAddressMapViewController *mapController = [[JDISVAddressMapViewController alloc] init];
//            mapController.addressSelectedCallback = ^(CLLocationCoordinate2D location) {
//                @strongify(self)
//                [self.viewModel reverseGeoCodeWithLocation:location block:^{
//                    @strongify(self)
////                    self.viewModel.needUpdateLocation = NO;
//                    self.viewModel.location = location;
//                    self.viewModel.address = nil;
//                    [self.infoView setArea:self.viewModel.area];
//                    [self.infoView setDetail:self.viewModel.detail];
//                    if (self.viewModel.detail.length < 6) {
//                        [self.infoView showErrorTip];
//                        [self.infoView mas_updateConstraints:^(MASConstraintMaker *make) {
//                            make.height.mas_equalTo(260);
//                        }];
//                    }
//                }];
//            };
            ADRTencentMapController *mapController = [[ADRTencentMapController alloc] init];
            mapController.addressSelectionCallback = ^(QMSReverseGeoCodeSearchResult * _Nonnull selectedAddress) {
                if (selectedAddress && selectedAddress.ad_info && selectedAddress.address_reference){
                    [self queryParentListByAreaId:false districtId: selectedAddress.ad_info.adcode townId : selectedAddress.address_reference.town.id_];
                    self.viewModel.area = [NSString stringWithFormat:@"%@,%@,%@,%@", selectedAddress.ad_info.province, selectedAddress.ad_info.city, selectedAddress.ad_info.district, selectedAddress.address_reference.town.title];
                    self.viewModel.detail = selectedAddress.address;
                    [self.infoView setArea:self.viewModel.area];
                    [self.infoView setDetail:self.viewModel.detail];
                }
            };
            [self.navigationController pushViewController:mapController animated:YES];
        }else {
            [KAAlert alert].config
            .renderW4(AddressL(@"address_map_permssion_alert_title"), AddressL(@"address_map_permssion_alert_content"))
            .ka_addAction(AddressL(@"address_cancel"), ^(UIButton * _Nonnull button) {
                [button renderB5];
            }, ^{})
            .ka_addAction(AddressL(@"address_confirm"), ^(UIButton * _Nonnull button) {
                [button renderB3];
            }, ^{
                NSURL *url = [[NSURL alloc] initWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }).jdcd_show();
        }
    }];
}

-(void)queryParentListByAreaId:(BOOL)showLoading districtId : (NSString *)districtId townId: (NSString *)townId{
    __weak typeof(self) weakSelf = self;
//    if(showLoading)
//        [self showLoading];
    
    [[[JDISVAddressService shareInstance] queryParentListByAreaId: districtId townId: townId] subscribeNext:^(JDTAreaModel * _Nullable x) {
        __strong typeof(self) sSelf = weakSelf;
        
        sSelf.viewModel.address = [[JDTAddressItemModel alloc] init];
        
        if(x && x.children && x.children.count > 0){
            sSelf.viewModel.address.provinceId = x.children[0].areaId;
            sSelf.viewModel.address.provinceName =  x.children[0].areaName;
            
            if(x.children[0].children && x.children[0].children.count > 0) {
                sSelf.viewModel.address.cityId = x.children[0].children[0].areaId;
                sSelf.viewModel.address.cityName = x.children[0].children[0].areaName;
                
                if(x.children[0].children[0].children && x.children[0].children[0].children.count > 0) {
                    sSelf.viewModel.address.districtId = x.children[0].children[0].children[0].areaId;
                    sSelf.viewModel.address.districtName = x.children[0].children[0].children[0].areaName;
                    
                    if(x.children[0].children[0].children[0].children && x.children[0].children[0].children[0].children.count > 0){
                        sSelf.viewModel.address.townId = x.children[0].children[0].children[0].children[0].areaId;
                        sSelf.viewModel.address.townName = x.children[0].children[0].children[0].children[0].areaName;
                    }
                }
            }
        }
        
    } error:^(NSError * _Nullable error) {
        NSLog(@"111");
    }];
}

- (void)areaButtonClick:(nonnull UIButton *)sender {
    [self.view endEditing:YES];
    if(self.fromMySetting){
        [self openFullCascader:YES];
    }else{
        [self openCascader];
    }
}

-(void)openFullCascader:(BOOL)noSelect{
    
    JDISVAddressFullCascaderViewController *cascaderController;
    if(noSelect){
        cascaderController = [[JDISVAddressFullCascaderViewController alloc]
                              initWithAddress:nil
                              noSelect:noSelect];
    }else{
        cascaderController =
        [[JDISVAddressFullCascaderViewController alloc]
         initWithAddress:self.viewModel.address
         noSelect:noSelect];
    }
    cascaderController.convert = NO;

    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    delegate.defultHeader.titleLabel.text = AddressL(@"address_complete_dialog_hint");
    cascaderController.transitioningDelegate = delegate;
    @weakify(self)
    [cascaderController.delegate subscribeNext:^(JDTAddressItemModel*  _Nullable x) {
        @strongify(self)
        self.viewModel.address = x;
        NSString* tip;
        if ([x.townId isEqualToString:@"-10000"]) {
            tip = [NSString stringWithFormat:@"%@%@%@", x.provinceName ?: @"",x.cityName ?: @"",x.districtName ?: @""];
        } else {
            tip = [NSString stringWithFormat:@"%@%@%@%@", x.provinceName ?: @"",x.cityName ?: @"",x.districtName ?: @"",x.townName ?: @""];
        }
        [self.infoView setArea:tip];
    }];
    [self presentViewController:cascaderController animated:YES completion:nil];
}

-(void)openCascader{
    JDISVAddressCascaderViewController *cascaderController  = [[JDISVAddressCascaderViewController alloc] initWithAddress:nil limit:0];
    cascaderController.convert = NO;
    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    delegate.defultHeader.titleLabel.text = AddressL(@"address_label_title");
    cascaderController.transitioningDelegate = delegate;
    @weakify(self)
    [cascaderController.delegate subscribeNext:^(JDTAddressItemModel * _Nullable x) {
        @strongify(self)
        self.viewModel.address = x;
        [self.infoView setArea:[NSString stringWithFormat:@"%@%@%@%@", self.viewModel.address.provinceName ?: @"",self.viewModel.address.cityName ?: @"",self.viewModel.address.districtName ?: @"",self.viewModel.address.townName ?: @""]];
    }];
    [self presentViewController:cascaderController animated:YES completion:nil];
}

-(void)rebuidMas{
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    CGFloat w2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    CGFloat top = w2;
    
    [self.infoView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(self.infoView.currentHeight);
        make.top.equalTo(self.scrollView).offset(top);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
    }];
    
    top+=self.infoView.currentHeight+w2;
    [self.tagView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(193);
        make.top.equalTo(self.scrollView).offset(top);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
        make.width.equalTo(self.view).offset(-2*w3);
    }];
    _scrollView.contentSize = CGSizeMake([UIScreen mainScreen].bounds.size.width, top+193+w2);
    [self.view layoutSubviews];
    [self.view layoutIfNeeded];
    [self.infoView setArea:self.infoView.area];
    
}

/// MARK: - Accessor

- (JDISVAddressTagView *)tagView {
    if(!_tagView){
        _tagView = (JDISVAddressTagView *)[JDISVAddressTagView isv_address_viewFromNibNamed:@"JDISVAddressTagView"];
        _tagView.clipsToBounds = YES;
    }
    return _tagView;
    
}

- (JDISVAdressInfoView *)infoView {
    if(!_infoView){
        _infoView = (JDISVAdressInfoView *)[JDISVAdressInfoView isv_address_viewFromNibNamed:@"JDISVAdressInfoView"];
        _infoView.clipsToBounds = YES;
        _infoView.delegate = self;
        _infoView.canLoc = [self infoViewCanLoc];
        _infoView.forAddAddress = YES;
    }
    return _infoView;
}

-(BOOL)infoViewCanLoc{
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:@"KaAddressAddPageId"];
    NSNumber* feature = config[@"features"][@"floors"][@"KaAddressFormFloor"][@"gisFeature"];
    return feature.boolValue;
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
    }
    return _scrollView;
}

#pragma mark - IQKeyboard
- (void)setIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = YES;
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = NO;
}

- (void)storeIQKeyboardSetting {
    self.keyboardEnable = [IQKeyboardManager sharedManager].enable;
    self.keyboardEnableAutoToolbar = [IQKeyboardManager sharedManager].enableAutoToolbar;
    self.keyboardShouldResignOnTouchOutside = [IQKeyboardManager sharedManager].shouldResignOnTouchOutside;
}

- (void)restoreIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = self.keyboardEnable;
    [IQKeyboardManager sharedManager].enableAutoToolbar = self.keyboardEnableAutoToolbar;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = self.keyboardShouldResignOnTouchOutside;
}

@end
