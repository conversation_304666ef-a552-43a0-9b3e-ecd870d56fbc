<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="JDISVAddressTagView">
            <rect key="frame" x="0.0" y="0.0" width="536" height="213"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标签" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jX1-qd-FwG">
                    <rect key="frame" x="15" y="23" width="30" height="16"/>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IHN-Pi-71t">
                    <rect key="frame" x="97" y="18" width="234" height="30"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rzm-Gq-W2X">
                            <rect key="frame" x="0.0" y="0.0" width="70" height="30"/>
                            <color key="backgroundColor" red="0.93333333330000001" green="0.1960784314" blue="0.2274509804" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="30" id="LQ6-TF-XK2"/>
                                <constraint firstAttribute="width" constant="70" id="esb-AP-5J0"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="家">
                                <color key="titleColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <state key="selected">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="homeTagButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="9V1-LD-w9i"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5Bo-ZC-H2m">
                            <rect key="frame" x="164" y="0.0" width="70" height="30"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="70" id="VYA-va-MAw"/>
                                <constraint firstAttribute="height" constant="30" id="xS1-r2-ysy"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="学校">
                                <color key="titleColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <state key="selected">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="schoolTagButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="E8D-Cu-7eI"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HYh-t5-ohn">
                            <rect key="frame" x="82" y="0.0" width="70" height="30"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="70" id="41m-Su-ha3"/>
                                <constraint firstAttribute="height" constant="30" id="hN2-TZ-D7d"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="公司">
                                <color key="titleColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <state key="selected">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="companyTagButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="Ii5-qo-r7q"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="5Bo-ZC-H2m" secondAttribute="trailing" id="7e2-ih-XVG"/>
                        <constraint firstItem="rzm-Gq-W2X" firstAttribute="top" secondItem="IHN-Pi-71t" secondAttribute="top" id="Jos-Q6-u8l"/>
                        <constraint firstItem="HYh-t5-ohn" firstAttribute="top" secondItem="IHN-Pi-71t" secondAttribute="top" id="MQ2-ON-OZG"/>
                        <constraint firstItem="rzm-Gq-W2X" firstAttribute="leading" secondItem="IHN-Pi-71t" secondAttribute="leading" id="dtC-oM-19q"/>
                        <constraint firstItem="5Bo-ZC-H2m" firstAttribute="leading" secondItem="HYh-t5-ohn" secondAttribute="trailing" constant="12" id="rMe-i4-eiv"/>
                        <constraint firstItem="HYh-t5-ohn" firstAttribute="leading" secondItem="rzm-Gq-W2X" secondAttribute="trailing" constant="12" id="rRN-lw-Woz"/>
                        <constraint firstAttribute="height" constant="30" id="vUr-Rt-bDr"/>
                        <constraint firstItem="5Bo-ZC-H2m" firstAttribute="top" secondItem="IHN-Pi-71t" secondAttribute="top" id="ySt-kO-Q2u"/>
                    </constraints>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HMe-S9-r12">
                    <rect key="frame" x="97" y="60" width="70" height="30"/>
                    <color key="backgroundColor" red="0.93333333330000001" green="0.1960784314" blue="0.2274509804" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="MFJ-qG-wZX"/>
                        <constraint firstAttribute="width" constant="70" id="pwZ-uz-Yhm"/>
                    </constraints>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <connections>
                        <action selector="addTagButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="LjW-HZ-bQm"/>
                    </connections>
                </button>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8gs-iM-dwt">
                    <rect key="frame" x="97" y="60" width="234" height="30"/>
                    <subviews>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="标签最多填写5个字符" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="MTN-CU-4mM">
                            <rect key="frame" x="0.0" y="0.0" width="170" height="30"/>
                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <textInputTraits key="textInputTraits"/>
                        </textField>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kcI-Dn-bPk">
                            <rect key="frame" x="182" y="0.0" width="52" height="30"/>
                            <color key="backgroundColor" red="0.93333333330000001" green="0.1960784314" blue="0.2274509804" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="30" id="4NC-Tj-Av2"/>
                                <constraint firstAttribute="width" constant="52" id="af7-pc-iqR"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="确定"/>
                            <connections>
                                <action selector="sureButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="Da3-9q-OgP"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="0.95686274509803915" green="0.95686274509803915" blue="0.95686274509803915" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="MTN-CU-4mM" firstAttribute="leading" secondItem="8gs-iM-dwt" secondAttribute="leading" id="F8E-4N-8cO"/>
                        <constraint firstItem="kcI-Dn-bPk" firstAttribute="leading" secondItem="MTN-CU-4mM" secondAttribute="trailing" constant="12" id="TxN-5Q-cVI"/>
                        <constraint firstAttribute="width" constant="234" id="crx-K2-Bfj"/>
                        <constraint firstAttribute="height" constant="30" id="heY-Vb-Xfu"/>
                        <constraint firstAttribute="bottom" secondItem="MTN-CU-4mM" secondAttribute="bottom" id="ivi-Jq-gay"/>
                        <constraint firstItem="MTN-CU-4mM" firstAttribute="top" secondItem="8gs-iM-dwt" secondAttribute="top" id="jnz-Gf-nXv"/>
                        <constraint firstItem="kcI-Dn-bPk" firstAttribute="top" secondItem="8gs-iM-dwt" secondAttribute="top" id="pUJ-N7-9kb"/>
                        <constraint firstAttribute="trailing" secondItem="kcI-Dn-bPk" secondAttribute="trailing" id="tjV-Eh-eNl"/>
                        <constraint firstAttribute="bottom" secondItem="kcI-Dn-bPk" secondAttribute="bottom" id="txW-eX-i2b"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6GS-tA-bAg">
                    <rect key="frame" x="97" y="60" width="116" height="30"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zd5-U5-cO1">
                            <rect key="frame" x="0.0" y="0.0" width="65" height="30"/>
                            <color key="backgroundColor" red="0.30196078431372547" green="0.51764705882352935" blue="0.99607843137254903" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="65" id="lzT-Jd-jNO"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="    Button    ">
                                <color key="titleColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                            <connections>
                                <action selector="customTagButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="201-52-8di"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MYA-dt-fOf">
                            <rect key="frame" x="65" y="0.0" width="51" height="30"/>
                            <color key="backgroundColor" red="0.93333333330000001" green="0.1960784314" blue="0.2274509804" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="51" id="6j1-MV-erh"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="    编辑    ">
                                <color key="titleColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                            <connections>
                                <action selector="editButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="ycp-NZ-tDy"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="zd5-U5-cO1" firstAttribute="leading" secondItem="6GS-tA-bAg" secondAttribute="leading" id="1Va-0S-auJ"/>
                        <constraint firstItem="MYA-dt-fOf" firstAttribute="top" secondItem="6GS-tA-bAg" secondAttribute="top" id="6Tm-hz-cjI"/>
                        <constraint firstAttribute="bottom" secondItem="MYA-dt-fOf" secondAttribute="bottom" id="93D-QS-xNP"/>
                        <constraint firstItem="MYA-dt-fOf" firstAttribute="leading" secondItem="zd5-U5-cO1" secondAttribute="trailing" id="GYo-EJ-dlS"/>
                        <constraint firstAttribute="trailing" secondItem="MYA-dt-fOf" secondAttribute="trailing" id="U5l-Gv-U1e"/>
                        <constraint firstAttribute="bottom" secondItem="zd5-U5-cO1" secondAttribute="bottom" id="em6-dh-5vA"/>
                        <constraint firstItem="zd5-U5-cO1" firstAttribute="top" secondItem="6GS-tA-bAg" secondAttribute="top" id="gKo-l1-Rhb"/>
                        <constraint firstAttribute="height" constant="30" id="k3A-rv-5et"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wxo-B1-OBJ">
                    <rect key="frame" x="0.0" y="108" width="536" height="85"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="提示：每次下单会默认推荐使用该地址提示：每次下单会默认推荐使用该地" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cUm-Gl-Tjp">
                            <rect key="frame" x="15" y="50" width="456" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="8Ad-zP-jSa"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="BR7-Oi-dHy">
                            <rect key="frame" x="479" y="43" width="51" height="31"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="31" id="9Rs-yC-s5Z"/>
                                <constraint firstAttribute="width" constant="49" id="jpc-re-zoa"/>
                            </constraints>
                            <color key="onTintColor" red="0.93333333330000001" green="0.1960784314" blue="0.2274509804" alpha="1" colorSpace="calibratedRGB"/>
                            <connections>
                                <action selector="switchtValueChanged:" destination="iN0-l3-epB" eventType="valueChanged" id="TvG-XT-KuX"/>
                            </connections>
                        </switch>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0i0-o8-V5F">
                            <rect key="frame" x="18" y="0.0" width="518" height="0.5"/>
                            <color key="backgroundColor" red="0.95686274509803915" green="0.95686274509803915" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="0.5" id="2ag-kh-gVC"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="设置默认地址" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hy1-j9-nV2">
                            <rect key="frame" x="15" y="21" width="83.5" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="BR7-Oi-dHy" secondAttribute="trailing" constant="8" id="3uO-XH-cDb"/>
                        <constraint firstItem="BR7-Oi-dHy" firstAttribute="leading" secondItem="cUm-Gl-Tjp" secondAttribute="trailing" constant="8" id="8If-wd-9fI"/>
                        <constraint firstItem="Hy1-j9-nV2" firstAttribute="leading" secondItem="wxo-B1-OBJ" secondAttribute="leading" constant="15" id="9B1-m6-oVz"/>
                        <constraint firstAttribute="bottom" secondItem="cUm-Gl-Tjp" secondAttribute="bottom" constant="18" id="Jhm-Ez-iem"/>
                        <constraint firstAttribute="trailing" secondItem="0i0-o8-V5F" secondAttribute="trailing" id="LlV-QG-GcI"/>
                        <constraint firstItem="0i0-o8-V5F" firstAttribute="top" secondItem="wxo-B1-OBJ" secondAttribute="top" id="QAS-ds-xmP"/>
                        <constraint firstItem="0i0-o8-V5F" firstAttribute="leading" secondItem="wxo-B1-OBJ" secondAttribute="leading" constant="18" id="SDc-yy-beH"/>
                        <constraint firstAttribute="bottom" secondItem="BR7-Oi-dHy" secondAttribute="bottom" constant="11" id="XSb-YT-EdF"/>
                        <constraint firstAttribute="height" constant="85" id="bbd-CO-ZXZ"/>
                        <constraint firstItem="cUm-Gl-Tjp" firstAttribute="leading" secondItem="wxo-B1-OBJ" secondAttribute="leading" constant="15" id="nqu-iK-1f4"/>
                        <constraint firstItem="cUm-Gl-Tjp" firstAttribute="top" secondItem="Hy1-j9-nV2" secondAttribute="bottom" constant="12" id="uBv-XJ-6KA"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="wxo-B1-OBJ" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="2Fa-dg-ZuK"/>
                <constraint firstItem="8gs-iM-dwt" firstAttribute="leading" secondItem="IHN-Pi-71t" secondAttribute="leading" id="CUs-u2-Fuh"/>
                <constraint firstItem="jX1-qd-FwG" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="23" id="FdO-G1-RUk"/>
                <constraint firstItem="8gs-iM-dwt" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="97" id="NlW-1Z-Nqo"/>
                <constraint firstItem="HMe-S9-r12" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="97" id="RS1-0F-NbZ"/>
                <constraint firstItem="6GS-tA-bAg" firstAttribute="top" secondItem="IHN-Pi-71t" secondAttribute="bottom" constant="12" id="SBv-s1-UAJ"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="wxo-B1-OBJ" secondAttribute="trailing" id="SqC-IU-ruU"/>
                <constraint firstItem="8gs-iM-dwt" firstAttribute="top" secondItem="IHN-Pi-71t" secondAttribute="bottom" constant="12" id="arn-Qo-hYx"/>
                <constraint firstItem="jX1-qd-FwG" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="15" id="hQQ-Mc-x63"/>
                <constraint firstItem="HMe-S9-r12" firstAttribute="top" secondItem="IHN-Pi-71t" secondAttribute="bottom" constant="12" id="ikh-XF-Iu3"/>
                <constraint firstItem="6GS-tA-bAg" firstAttribute="leading" secondItem="IHN-Pi-71t" secondAttribute="leading" id="kdQ-Dn-y8V"/>
                <constraint firstItem="IHN-Pi-71t" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="18" id="qBt-AR-XBp"/>
                <constraint firstItem="wxo-B1-OBJ" firstAttribute="top" secondItem="HMe-S9-r12" secondAttribute="bottom" constant="18" id="qqU-cW-cyl"/>
                <constraint firstItem="IHN-Pi-71t" firstAttribute="leading" secondItem="jX1-qd-FwG" secondAttribute="trailing" constant="52" id="tQW-2T-pWC"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="addTagButton" destination="HMe-S9-r12" id="Ams-uQ-Ibj"/>
                <outlet property="adddressSwitch" destination="BR7-Oi-dHy" id="Kw8-YH-zwy"/>
                <outlet property="addressTiplabel" destination="cUm-Gl-Tjp" id="Sju-yA-3pz"/>
                <outlet property="bottomContainerView" destination="wxo-B1-OBJ" id="Lzv-Zm-Uzd"/>
                <outlet property="bottomDefaultAddressLabel" destination="Hy1-j9-nV2" id="fwx-WQ-3Zp"/>
                <outlet property="companyTagButton" destination="HYh-t5-ohn" id="G0I-8T-21L"/>
                <outlet property="customTagButton" destination="zd5-U5-cO1" id="dUH-vv-s9A"/>
                <outlet property="customTagButtonWidth" destination="lzT-Jd-jNO" id="Ysr-Y0-Gn9"/>
                <outlet property="customTagEditView" destination="8gs-iM-dwt" id="2TA-CA-FDc"/>
                <outlet property="customTagView" destination="6GS-tA-bAg" id="EGj-Zf-nZr"/>
                <outlet property="editButton" destination="MYA-dt-fOf" id="q0E-BX-tIe"/>
                <outlet property="homeTagButton" destination="rzm-Gq-W2X" id="P4q-ic-yIi"/>
                <outlet property="schoolTagButton" destination="5Bo-ZC-H2m" id="9gr-OI-6Vr"/>
                <outlet property="sureButton" destination="kcI-Dn-bPk" id="4wm-Kt-nmT"/>
                <outlet property="tagContainerView" destination="IHN-Pi-71t" id="rtp-xI-U0t"/>
                <outlet property="tagLabel" destination="jX1-qd-FwG" id="5Cx-ts-d2b"/>
                <outlet property="tagTextField" destination="MTN-CU-4mM" id="mmz-vs-SXR"/>
            </connections>
            <point key="canvasLocation" x="-114.49275362318842" y="259.48660714285711"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
