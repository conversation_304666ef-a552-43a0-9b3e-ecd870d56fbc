<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="JDISVAdressInfoView">
            <rect key="frame" x="0.0" y="0.0" width="510" height="464"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="z4I-pc-xFz">
                    <rect key="frame" x="0.0" y="0.0" width="510" height="60"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="收货人" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jfi-Id-wY7">
                            <rect key="frame" x="15" y="0.0" width="42" height="60"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请填写收货人姓名" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="Z5L-x2-kZX">
                            <rect key="frame" x="125" y="0.0" width="370" height="60"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                        </textField>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uBU-Eb-BhY">
                            <rect key="frame" x="15" y="59.5" width="495" height="0.5"/>
                            <color key="backgroundColor" red="0.95686274509803915" green="0.95686274509803915" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="0.5" id="dUe-1U-Q3W"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="Z5L-x2-kZX" secondAttribute="trailing" constant="15" id="0KW-DA-VHu"/>
                        <constraint firstItem="Jfi-Id-wY7" firstAttribute="leading" secondItem="z4I-pc-xFz" secondAttribute="leading" constant="15" id="7DQ-Mg-X2B"/>
                        <constraint firstAttribute="height" constant="60" id="JwO-jd-Z2i"/>
                        <constraint firstAttribute="trailing" secondItem="uBU-Eb-BhY" secondAttribute="trailing" id="Nt6-Se-iHY"/>
                        <constraint firstItem="uBU-Eb-BhY" firstAttribute="leading" secondItem="z4I-pc-xFz" secondAttribute="leading" constant="15" id="hJ3-Ei-kO0"/>
                        <constraint firstItem="Jfi-Id-wY7" firstAttribute="top" secondItem="z4I-pc-xFz" secondAttribute="top" id="kj3-vO-RfL"/>
                        <constraint firstAttribute="bottom" secondItem="Z5L-x2-kZX" secondAttribute="bottom" id="mu0-kr-eL5"/>
                        <constraint firstAttribute="bottom" secondItem="Jfi-Id-wY7" secondAttribute="bottom" id="nFK-kK-ChV"/>
                        <constraint firstAttribute="bottom" secondItem="uBU-Eb-BhY" secondAttribute="bottom" id="tTu-DK-tf3"/>
                        <constraint firstItem="Z5L-x2-kZX" firstAttribute="leading" secondItem="z4I-pc-xFz" secondAttribute="leading" constant="125" id="xhq-gj-ZVX"/>
                        <constraint firstItem="Z5L-x2-kZX" firstAttribute="top" secondItem="z4I-pc-xFz" secondAttribute="top" id="z24-vz-6Eg"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K5X-kh-D9V">
                    <rect key="frame" x="0.0" y="60" width="510" height="60"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="手机号码" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CmD-im-t7q">
                            <rect key="frame" x="15" y="0.0" width="56" height="60"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请填写收货人手机号码" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="7Jf-Ls-z5E">
                            <rect key="frame" x="125" y="0.0" width="370" height="60"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                        </textField>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EIE-rB-8l7">
                            <rect key="frame" x="15" y="59.5" width="495" height="0.5"/>
                            <color key="backgroundColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="0.5" id="QVC-3B-GPy"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="EIE-rB-8l7" secondAttribute="trailing" id="6So-0o-flm"/>
                        <constraint firstItem="EIE-rB-8l7" firstAttribute="leading" secondItem="K5X-kh-D9V" secondAttribute="leading" constant="15" id="71s-Xj-h3B"/>
                        <constraint firstItem="CmD-im-t7q" firstAttribute="leading" secondItem="K5X-kh-D9V" secondAttribute="leading" constant="15" id="Wur-T0-Rcv"/>
                        <constraint firstAttribute="bottom" secondItem="7Jf-Ls-z5E" secondAttribute="bottom" id="bRI-cV-zcO"/>
                        <constraint firstItem="7Jf-Ls-z5E" firstAttribute="top" secondItem="K5X-kh-D9V" secondAttribute="top" id="g5g-BL-XU6"/>
                        <constraint firstAttribute="bottom" secondItem="CmD-im-t7q" secondAttribute="bottom" id="hKH-c8-TBS"/>
                        <constraint firstAttribute="height" constant="60" id="iTl-rF-d0W"/>
                        <constraint firstAttribute="trailing" secondItem="7Jf-Ls-z5E" secondAttribute="trailing" constant="15" id="mcC-Dk-ewL"/>
                        <constraint firstItem="7Jf-Ls-z5E" firstAttribute="leading" secondItem="K5X-kh-D9V" secondAttribute="leading" constant="125" id="myn-TG-KEu"/>
                        <constraint firstItem="CmD-im-t7q" firstAttribute="top" secondItem="K5X-kh-D9V" secondAttribute="top" id="wks-vn-qfc"/>
                        <constraint firstAttribute="bottom" secondItem="EIE-rB-8l7" secondAttribute="bottom" id="y74-7e-c1E"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3OM-Q7-ON4">
                    <rect key="frame" x="0.0" y="120" width="510" height="60"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="所在地区" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ohZ-GW-0Ot">
                            <rect key="frame" x="15" y="0.0" width="56" height="60"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textField opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="省市区县、乡镇等" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="dIc-uX-SBb">
                            <rect key="frame" x="125" y="0.0" width="317" height="60"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                        </textField>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NGZ-YI-TNP">
                            <rect key="frame" x="125" y="1" width="317" height="59"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ypc-Tf-WyP">
                            <rect key="frame" x="442" y="0.0" width="68" height="59.5"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="68" id="TEL-yv-gKW"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <state key="normal" title="定位">
                                <color key="titleColor" systemColor="labelColor"/>
                            </state>
                            <connections>
                                <action selector="locationButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="hrn-tR-Fk7"/>
                            </connections>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZQc-h6-MlI">
                            <rect key="frame" x="15" y="59.5" width="495" height="0.5"/>
                            <color key="backgroundColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="0.5" id="TbZ-MV-pMr"/>
                            </constraints>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SvB-lf-GuG">
                            <rect key="frame" x="125" y="0.0" width="317" height="59.5"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <connections>
                                <action selector="areaButtonClick:" destination="iN0-l3-epB" eventType="touchUpInside" id="2hZ-vI-rkJ"/>
                            </connections>
                        </button>
                    </subviews>
                    <constraints>
                        <constraint firstItem="ZQc-h6-MlI" firstAttribute="leading" secondItem="3OM-Q7-ON4" secondAttribute="leading" constant="15" id="090-gr-V2O"/>
                        <constraint firstItem="NGZ-YI-TNP" firstAttribute="bottom" secondItem="dIc-uX-SBb" secondAttribute="bottom" id="552-Uj-xHS"/>
                        <constraint firstItem="NGZ-YI-TNP" firstAttribute="leading" secondItem="dIc-uX-SBb" secondAttribute="leading" id="7TW-0f-wFz"/>
                        <constraint firstItem="Ypc-Tf-WyP" firstAttribute="leading" secondItem="dIc-uX-SBb" secondAttribute="trailing" id="Diz-K8-BQ7"/>
                        <constraint firstAttribute="height" constant="60" id="GCA-6R-iTO"/>
                        <constraint firstAttribute="trailing" secondItem="Ypc-Tf-WyP" secondAttribute="trailing" id="K0k-r4-UV4"/>
                        <constraint firstItem="SvB-lf-GuG" firstAttribute="top" secondItem="3OM-Q7-ON4" secondAttribute="top" id="KxU-6p-ppd"/>
                        <constraint firstItem="NGZ-YI-TNP" firstAttribute="top" secondItem="dIc-uX-SBb" secondAttribute="top" constant="1" id="LLv-0R-sdk"/>
                        <constraint firstItem="ohZ-GW-0Ot" firstAttribute="leading" secondItem="3OM-Q7-ON4" secondAttribute="leading" constant="15" id="PEb-CT-g3J"/>
                        <constraint firstItem="Ypc-Tf-WyP" firstAttribute="top" secondItem="3OM-Q7-ON4" secondAttribute="top" id="T9P-Ca-FJV"/>
                        <constraint firstItem="ZQc-h6-MlI" firstAttribute="top" secondItem="Ypc-Tf-WyP" secondAttribute="bottom" id="VMm-I6-v5t"/>
                        <constraint firstAttribute="trailing" secondItem="ZQc-h6-MlI" secondAttribute="trailing" id="Y6S-MP-Cla"/>
                        <constraint firstAttribute="bottom" secondItem="ohZ-GW-0Ot" secondAttribute="bottom" id="bTP-iY-xNz"/>
                        <constraint firstAttribute="bottom" secondItem="dIc-uX-SBb" secondAttribute="bottom" id="bTr-87-VYP"/>
                        <constraint firstItem="ZQc-h6-MlI" firstAttribute="top" secondItem="SvB-lf-GuG" secondAttribute="bottom" id="ckP-MA-swA"/>
                        <constraint firstItem="dIc-uX-SBb" firstAttribute="leading" secondItem="3OM-Q7-ON4" secondAttribute="leading" constant="125" id="dtp-hs-AEv"/>
                        <constraint firstItem="dIc-uX-SBb" firstAttribute="top" secondItem="3OM-Q7-ON4" secondAttribute="top" id="h3M-ZM-zbf"/>
                        <constraint firstItem="SvB-lf-GuG" firstAttribute="width" secondItem="dIc-uX-SBb" secondAttribute="width" id="iy8-FL-SOb"/>
                        <constraint firstItem="SvB-lf-GuG" firstAttribute="leading" secondItem="dIc-uX-SBb" secondAttribute="leading" id="jRN-Wi-aTb"/>
                        <constraint firstItem="NGZ-YI-TNP" firstAttribute="trailing" secondItem="dIc-uX-SBb" secondAttribute="trailing" id="m5G-WL-VZA"/>
                        <constraint firstAttribute="bottom" secondItem="ZQc-h6-MlI" secondAttribute="bottom" id="t2l-gj-CO7"/>
                        <constraint firstItem="ohZ-GW-0Ot" firstAttribute="top" secondItem="3OM-Q7-ON4" secondAttribute="top" id="wdm-3c-y15"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="S9u-VY-rCO">
                    <rect key="frame" x="0.0" y="180" width="510" height="60"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="详细地址" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QQY-KY-orn">
                            <rect key="frame" x="15" y="0.0" width="56" height="60"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="60" id="IwQ-pP-aGt"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="街道、楼牌号等" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="hxT-IR-jRq">
                            <rect key="frame" x="125" y="0.0" width="370" height="60"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                        </textField>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iXK-0Y-QgV">
                            <rect key="frame" x="15" y="59.5" width="495" height="0.5"/>
                            <color key="backgroundColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="0.5" id="NBo-y5-33Z"/>
                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="0.5" id="qhm-s2-flY"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="iXK-0Y-QgV" secondAttribute="bottom" id="6R2-mo-ul4"/>
                        <constraint firstAttribute="height" constant="60" id="G7H-8K-dUR"/>
                        <constraint firstItem="hxT-IR-jRq" firstAttribute="leading" secondItem="S9u-VY-rCO" secondAttribute="leading" constant="125" id="NqJ-wz-ryP"/>
                        <constraint firstItem="hxT-IR-jRq" firstAttribute="top" secondItem="S9u-VY-rCO" secondAttribute="top" id="OH7-WE-7Ja"/>
                        <constraint firstItem="QQY-KY-orn" firstAttribute="top" secondItem="S9u-VY-rCO" secondAttribute="top" id="PAu-bq-cb7"/>
                        <constraint firstItem="iXK-0Y-QgV" firstAttribute="leading" secondItem="S9u-VY-rCO" secondAttribute="leading" constant="15" id="PKD-8e-lwJ"/>
                        <constraint firstAttribute="trailing" secondItem="hxT-IR-jRq" secondAttribute="trailing" constant="15" id="S1D-f0-hNZ"/>
                        <constraint firstAttribute="trailing" secondItem="iXK-0Y-QgV" secondAttribute="trailing" id="UTq-oK-iBX"/>
                        <constraint firstItem="QQY-KY-orn" firstAttribute="leading" secondItem="S9u-VY-rCO" secondAttribute="leading" constant="15" id="hjy-Ua-fPL"/>
                        <constraint firstAttribute="bottom" secondItem="hxT-IR-jRq" secondAttribute="bottom" id="xR7-Dp-c8A"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="S9u-VY-rCO" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="5an-CX-KiL"/>
                <constraint firstAttribute="trailing" secondItem="3OM-Q7-ON4" secondAttribute="trailing" id="6oX-TC-dgr"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="S9u-VY-rCO" secondAttribute="trailing" id="7FP-WG-Bl9"/>
                <constraint firstItem="K5X-kh-D9V" firstAttribute="top" secondItem="z4I-pc-xFz" secondAttribute="bottom" id="Bs4-Hm-VcG"/>
                <constraint firstItem="z4I-pc-xFz" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="Hzc-ve-iVC"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="z4I-pc-xFz" secondAttribute="trailing" id="Jky-Re-k1P"/>
                <constraint firstItem="3OM-Q7-ON4" firstAttribute="top" secondItem="K5X-kh-D9V" secondAttribute="bottom" id="K2R-yb-4Bc"/>
                <constraint firstItem="3OM-Q7-ON4" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="iik-da-pXV"/>
                <constraint firstItem="z4I-pc-xFz" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="m0M-PR-7C1"/>
                <constraint firstAttribute="trailing" secondItem="K5X-kh-D9V" secondAttribute="trailing" id="pnz-dp-1a1"/>
                <constraint firstItem="S9u-VY-rCO" firstAttribute="top" secondItem="3OM-Q7-ON4" secondAttribute="bottom" id="wFz-QV-p00"/>
                <constraint firstItem="K5X-kh-D9V" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="yf6-l0-0tC"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="ArelLabel" destination="NGZ-YI-TNP" id="VvX-AG-OfZ"/>
                <outlet property="areaLabel" destination="ohZ-GW-0Ot" id="2Nv-nc-85l"/>
                <outlet property="areaTextField" destination="dIc-uX-SBb" id="d4q-f3-puy"/>
                <outlet property="detailAddressTextField" destination="hxT-IR-jRq" id="m78-FN-LEJ"/>
                <outlet property="detailBackView" destination="S9u-VY-rCO" id="Tn1-P6-Z1A"/>
                <outlet property="detailLabel" destination="QQY-KY-orn" id="3wL-pd-nrf"/>
                <outlet property="lastLine" destination="iXK-0Y-QgV" id="16x-h7-pqF"/>
                <outlet property="locationButton" destination="Ypc-Tf-WyP" id="SMQ-OH-fT6"/>
                <outlet property="locationButtonWidth" destination="TEL-yv-gKW" id="Ddy-l6-G1V"/>
                <outlet property="nameLabel" destination="Jfi-Id-wY7" id="E3W-Pe-VgG"/>
                <outlet property="nameTextField" destination="Z5L-x2-kZX" id="ZgX-pm-EXK"/>
                <outlet property="phoneTextField" destination="7Jf-Ls-z5E" id="yV4-xk-lfY"/>
                <outlet property="phonelabel" destination="CmD-im-t7q" id="bp4-Yh-kXR"/>
            </connections>
            <point key="canvasLocation" x="-114.49275362318842" y="66.294642857142861"/>
        </view>
    </objects>
    <resources>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
