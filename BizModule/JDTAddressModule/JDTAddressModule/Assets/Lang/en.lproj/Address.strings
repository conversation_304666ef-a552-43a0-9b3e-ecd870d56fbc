"address_resolve_failed_manual_input" = "Recognition failure, please enter manually";
"address_complete_dialog_hint" = "Please select the location of the address";
"address_detail_title_edit" = "Edit address";
"address_list_add" = "Add delivery address";
"address_detail_specific_hint" = "Street, building number, etc.";
"address_home_tag" = "Home";
"address_detail_location_label" = "Region";
"address_map_permssion_alert_go" = "To settings";
"address_cancel" = "Cancel";
"address_map_search_empty" = "No search results";
"address_default_level_names" = "Beijing | Beijing | Dongcheng District | Donghuamen Street";
"address_detail_exit_dialog_discard" = "Do not save";
"address_recognize_expand" = "Address paste board";
"address_detail_locate" = "Position";
"address_empty_address_tips" = "The address is null, please add the delivery address";
"address_add_address_error" = "Failed to add address";
"address_update_address_error" = "Failed to modify the address";
"address_detail_name_hint" = "Please enter the name of receiver";
"address_recognize_hint" = "Please check whether the identified address information is accurate, and supplement it in time in case of any omission";
"address_recognize_max_length" = "The address can contain a maximum of 120 characters ~";
"address_confirm" = "Confirm";
"address_submit_recognize" = "Submit";
"address_detail_phone_label" = "Phone number";
"address_map_permssion_alert_content" = "You have not enabled the APP location permission and cannot use this function. Please go to “Settings” to enable it";
"address_list_menu_set_default" = "Set as default";
"address_map_location_empty" = "No address searched";
"address_title_select_area" = "Please select the region";
"address_common_network_error" = "Network request failed";
"address_delete_address_success" = "Address deletion succeeded";
"address_select_city_no_location_title" = "Location failed";
"address_popup_select" = "Please select";
"address_default_label" = "Default";
"address_title_my_address" = "My address";
"address_detail_tag_label" = "Tag";
"address_detail_region_hint" = "Province, city, district, county, township, town, etc.";
"address_map_relocate" = "Relocate";
"address_delete_address_error" = "Failed to delete address";
"address_set_default_address_error" = "Default address setting failed";
"address_detail_complete_hint" = "Complete the detailed address";
"address_search_district_hint" = "Enter the city name to search";
"address_list_toast_can_not_delete" = "The selected address cannot be deleted";
"address_title_complete_dialog" = "According to the division of national administrative regions, your address is identified as %@. Change the address for faster distribution or not?'";
"address_detail_phone_hint" = "Please fill in the phone number of receiver";
"address_list_menu_delete" = "Delete";
"address_detail_select_address_lv_title" = "Address selection";
"address_popup_choose_another_address" = "Select another address";
"address_detail_specific_label" = "Detailed address";
"address_detail_exit_dialog_save" = "Save";
"address_default_city_name" = "Beijing";
"address_select_none" = "Do not select";
"address_map_locating" = "Locating";
"address_detail_default_hint" = "Tips: The address will be recommended for each order by default";
"address_detail_tag_hint" = "Tag can contain a maximum of 20 characters";
"address_company_tag" = "Company";
"address_hint_recognize" = "Your receipt information can be identified quickly by pasting receiver name, mobile phone number and delivery address, have a try";
"address_detail_name_label" = "Receiver";
"address_map_search_hint" = "Community/school, etc.";
"address_select_city_locate_loading" = "Locating";
"address_detail_phone_illegal" = "Only 11-digit numbers can be entered";
"address_select_all" = "All";
"address_use_location" = "Apply location";
"address_resolve_4th_region_failed" = "Parsing failed. Please select the location of the address";
"address_retry" = "Retry ";
"address_update_address_success" = "Address modified successfully";
"address_add_address_success" = "Address added successfully";
"address_add_new_btn" = "New delivery address";
"address_resolve_4th_region_succeed" = "Identified successfully";
"address_detail_exit_dialog_title" = "Do you want to save the information modified this time";
"address_detail_phone_empty" = "Please enter the phone number";
"address_map_permssion_alert_title" = "Location service not enabled";
"address_map_current_location" = "Current location";
"address_detail_default_label" = "Set default address";
"address_school_tag" = "School";
"address_clear_recognize" = "Clear";
"address_error_tips" = "Page loading failed";
"address_tab_gat" = "Hong Kong, Macao and Taiwan";
"address_tab_mainland" = "Chinese Mainland";
"address_set_default_address_success" = "Default address setting succeeded";
"address_detail_name_illegal" = "Please enter a maximum of 200 characters under the receiver";
"address_detail_specific_illegal" = "Only 1~300 characters can be entered for the detailed address";
"address_list_delete_address_title" = "Do you want to delete this address?";
"address_load_failed" = "Data loading failed";
"address_detail_tag_edit" = "Edit";
"address_detail_title_add" = "Newly delivery address";
"address_exchanged" = "Switch address";
"address_exchanged_comfire" = "Do you want to switch";
"address_excahnged_des" = "You have changed the address when browsing the product. Please select whether to match the existing address or create a new address.";
"address_comfir_button_title" = "OK";
"address_not_modifiable" = "This multilevel associative address cannot be modified";
"address_inside" = "A maximum of %@ |%@";
"address_search_key_empty_tip" = "Please enter keywords for query";
"address_input_more" = "Please enter the detailed address";
"address_label_title" = "URL";
"address_request_error" = "The longitude and latitude cannot be converted for the current address";
"address_title_complete_dialog" = "According to the division of national administrative regions, your address is identified as %@. Change the address for faster distribution or not?'";

"next_time" = "Next Time";
"billing_address_add_question_add_delivery_address" = "Also set as delivery address？";
"Yes" = "Yes";
