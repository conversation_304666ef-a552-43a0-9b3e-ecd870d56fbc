//
//  ORDERListModel.h
//  JDTOrderListModule
//
//  Created by lvchenzhu.1 on 2025/6/12.
//

#import <Foundation/Foundation.h>
#import "JDTGloablDefines.h"

NS_ASSUME_NONNULL_BEGIN
// 操作按钮类型
typedef NS_ENUM(NSUInteger, ORDERListItemButtonType) {
    ORDERListItemButtonTypeToPay = 1,               // 去支付
    ORDERListItemButtonTypeQueryLogistics = 2,      // 查询物流
    ORDERListItemButtonTypeBuyAgain = 3,            // 再次购买
    ORDERListItemButtonTypeCancelOrder = 4,         // 取消订单
    //ORDERListItemButtonTypeContinuePay = 5,       // 继续支付
    //ORDERListItemButtonTypeCustomerService = 6,   // 联系客服
    ORDERListItemButtonTypeApplyAfterSale = 7,      // 申请售后
    //ORDERListItemButtonTypeApplyRefund = 8,       // 申请退款
    //ORDERListItemButtonTypeModifyOrder = 9,       // 修改订单
    ORDERListItemButtonTypeDeleteOrder = 10,        // 删除订单
    //ORDERListItemButtonTypePickUpCode = 11,       // 领取核销码
    ORDERListItemButtonTypeConfirmReceipt = 12,     // 确认收货
    //ORDERListItemButtonTypeReview = 13,           // 评价
    ORDERListItemButtonTypeIssueInvoice = 14,     // 开发票
    ORDERListItemButtonTypeViewInvoice = 15       // 查看发票
};
// 订单状态
typedef NS_ENUM(NSUInteger, ORDERListItemStateType) {
    ORDERListItemStateTypeWaitPay = 1,              // 待支付
    ORDERListItemStateTypeWaitPayConfirm = 5,       // 等待付款确认
    ORDERListItemStateTypePause = 10,               // 处理中
    ORDERListItemStateTypeWaitDelivery = 15,        // 待发货
    ORDERListItemStateTypeWaitReceipt = 20,         // 待收货
    ORDERListItemStateTypeFinished = 30,            // 已完成
    ORDERListItemStateTypeLocked = -2,              // 已锁定
    ORDERListItemStateTypeNotUse = 25,              // 未使用
    ORDERListItemStateTypeCanceled = -1,            // 已取消
};
// loc 商品核销码状态
typedef NS_ENUM(NSUInteger, ORDERListItemLocCodeState) {
    ORDERListItemLocCodeStateWaitForSending = 0,    // 等待发码
    ORDERListItemLocCodeStateNotUse = 10,           // 未使用
    ORDERListItemLocCodeStateLocked = 20,           // 锁定
    ORDERListItemLocCodeStateUsed = 30,             // 已使用
    ORDERListItemLocCodeStateExpired = 40,          // 已过期
    ORDERListItemLocCodeStateRefund = 50,           // 已退款
};

@interface ORDERListItemLocCodeModel : NSObject
/// 核销码数字
@property (nonatomic, copy) NSString *code;
/// 状态
@property (nonatomic, assign) ORDERListItemLocCodeState codeState;
/// 状态名称
@property (nonatomic, copy) NSString *codeStateDesc;
/// 生效开始时间
@property (nonatomic, copy) NSString *validStartTime;
/// 生效结束时间
@property (nonatomic, copy) NSString *validEndTime;

@end

@interface ORDERListItemLocStoreHouseModel : NSObject

@property (nonatomic, assign) NSInteger storehouseId;

@property (nonatomic, copy) NSString *storehouseName;

@property (nonatomic, copy) NSString *image;

@property (nonatomic, copy) NSString *contactPhone;

@property (nonatomic, copy) NSString *fullAddress;

@property (nonatomic, copy) NSString *latitude;

@property (nonatomic, copy) NSString *longitude;

@property (nonatomic, copy) NSString *openingStartTime;

@property (nonatomic, copy) NSString *openingEndTime;

@end

@interface ORDERListItemConsigneeModel : NSObject

@property (nonatomic, copy) NSString *addressId;

@property (nonatomic, copy) NSString *firstName;

@property (nonatomic, copy) NSString *lastName;

@property (nonatomic, copy) NSString *fullName;

@property (nonatomic, copy) NSString *emailDes;

@property (nonatomic, copy) NSString *mobileDes;
/// 国家区域码：+86
@property (nonatomic, copy) NSString *mobileCountry;

@property (nonatomic, copy) NSString *countrySymbol;

@property (nonatomic, copy) NSString *provinceId;

@property (nonatomic, copy) NSString *cityId;

@property (nonatomic, copy) NSString *districtId;

@property (nonatomic, copy) NSString *townId;
/// 邮编
@property (nonatomic, copy) NSString *zip;
/// 详细地址
@property (nonatomic, copy) NSString *address;

@end

@interface ORDERListItemSkuDetailModel : NSObject

@property (nonatomic, copy) NSString *skuId;

@property (nonatomic, copy) NSString *skuUuid;

@property (nonatomic, copy) NSString *skuName;
/// 购买数量
@property (nonatomic, strong) NSNumber *num;

@property (nonatomic, strong) NSNumber *shouldPayAmount;

@property (nonatomic, strong) NSNumber *originalPrice;

@property (nonatomic, copy) NSString *skuImgUrl;
/// "重量": "45g"
@property (nonatomic, strong) NSDictionary <NSString *, NSString *> *saleAttr;
/// 用户留言（备注）
@property (nonatomic, copy) NSString *buyerRemark;

@end

@interface ORDERListItemUMSModel : NSObject

@property (nonatomic, copy) NSString *content;

@property (nonatomic, copy) NSString *msgTime;
/// 操作人
@property (nonatomic, copy) NSString *operatorName;
/// 节点
@property (nonatomic, assign) NSInteger nodeFlag;

@end

@interface ORDERListItemShipmentOrderModel : NSObject

@property (nonatomic, strong) NSNumber *shipmentId;

@property (nonatomic, copy) NSString *orderId;
/// 承运商 id
@property (nonatomic, strong) NSNumber *carrierId;
/// 承运商
@property (nonatomic, copy) NSString *carrierName;
/// 运单号
@property (nonatomic, copy) NSString *waybillNum;
/// 商品总数量
@property (nonatomic, strong) NSNumber *skuNum;
/// 包裹状态
@property (nonatomic, assign) NSInteger state;
/// 物流轨迹
@property (nonatomic, copy) NSArray <ORDERListItemUMSModel *> *orderUms;

@end

@interface ORDERListItemButtonModel : NSObject

@property (nonatomic, assign) BOOL canClick;

@property (nonatomic, copy) NSString *showLabel;

@property (nonatomic, assign) ORDERListItemButtonType showLabelId;

@property (nonatomic, assign) NSInteger seq;

@end

@interface ORDERListItemAmountModel : NSObject
/// 实际支付
@property (nonatomic, strong) NSNumber *shouldPayAmount;
/// 商品总价
@property (nonatomic, strong) NSNumber *totalAmount;
/// 运费
@property (nonatomic, strong) NSNumber *totalFreightAmount;
/// 运费抵扣
@property (nonatomic, strong) NSNumber *totalFreightDiscountAmount;
/// 促销金额
@property (nonatomic, strong) NSNumber *totalPromoDiscount;
/// 优惠券抵扣
@property (nonatomic, strong) NSNumber *totalVoucherDiscount;
/// 优惠码抵扣
@property (nonatomic, strong) NSNumber *totalCouponCodeDiscount;
/// 积分抵扣
@property (nonatomic, strong) NSNumber *totalPointDiscountAmount;
///  消费券优惠金额
@property (nonatomic, strong) NSNumber *totalConsumptionVoucherAmount;

@end

@interface ORDERListItemStateModel : NSObject
/// 状态文字表述
@property (nonatomic, copy) NSString *stateName;

@property (nonatomic, assign) ORDERListItemStateType state;

@end

@interface ORDERListItemModel : NSObject

@property (nonatomic, copy) NSString *tenantCode;

@property (nonatomic, copy) NSString *orderId;

@property (nonatomic, copy) NSString *parentOrderId;
/// 剩余支付时间，单位是 ms
@property (nonatomic, assign) NSInteger remainingPayTime;

@property (nonatomic, strong) ORDERListItemStateModel *orderState;
/// 1：普通订单 2：loc 订单
@property (nonatomic, assign) ProductType orderType;

@property (nonatomic, copy) NSString *orderTypeDesc;
/// 支付方式 1-在线支付 2-COD,
@property (nonatomic, assign) NSInteger payType;

@property (nonatomic, copy) NSString *payTypeDesc;
/// 配送方式：1 快递运输
@property (nonatomic, assign) NSInteger deliveryType;

@property (nonatomic, copy) NSString *deliveryTypeDesc;

@property (nonatomic, copy) NSString *payTime;

@property (nonatomic, copy) NSString *createTime;

@property (nonatomic, strong) ORDERListItemAmountModel *orderAmount;

@property (nonatomic, copy) NSArray <ORDERListItemButtonModel *> *buttons;

@property (nonatomic, copy) NSString *venderId;

@property (nonatomic, copy) NSString *shopId;

@property (nonatomic, copy) NSString *shopName;
/// 物流进度
@property (nonatomic, copy) NSArray <ORDERListItemShipmentOrderModel *> *shipmentOrder;

@property (nonatomic, copy) NSArray <ORDERListItemSkuDetailModel *> *orderItem;

@property (nonatomic, strong) ORDERListItemConsigneeModel *orderConsignee;

@property (nonatomic, copy) NSArray <ORDERListItemLocCodeModel *> *locCodes;

@property (nonatomic, copy) NSArray <ORDERListItemLocStoreHouseModel *> *storehouseInfos;
/// 是否已支付
@property (nonatomic, assign) BOOL paid;
/// 取消码
@property (nonatomic, strong) NSNumber *cancelCode;
/// 取消描述
@property (nonatomic, copy) NSString *cancelCodeDesc;
/// 取消备注
@property (nonatomic, copy) NSString *cancelRemark;
/// 取消时间
@property (nonatomic, copy) NSString *cancelTime;
/// 是否申请过取消
@property (nonatomic, assign) BOOL appliedCancel;

@end

@interface ORDERListModel : NSObject

@property (nonatomic, copy) NSArray <ORDERListItemModel *> *orderList;

@property (nonatomic, assign) NSInteger currentPage;

@property (nonatomic, assign) NSInteger pageSize;

@property (nonatomic, assign) NSInteger totalNum;

@property (nonatomic, assign) NSInteger totalPages;

@end

NS_ASSUME_NONNULL_END
