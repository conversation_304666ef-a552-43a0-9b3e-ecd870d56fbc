//
//  ORDERDetailEnumsModel.h
//  JDTOrderDetailModule
//
//  Created by lvchenzhu.1 on 2025/6/23.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface ORDERDetailEnumItemModel : NSObject

@property (nonatomic, copy) NSString *code;

@property (nonatomic, copy) NSString *desc;
/// 本地字段
@property (nonatomic, assign) BOOL selected;

@end


@interface ORDERDetailEnumsModel : NSObject

@property (nonatomic, copy) NSArray <ORDERDetailEnumItemModel *> *CancelReasonEnum;

@property (nonatomic, copy) NSArray <ORDERDetailEnumItemModel *> *PayType;

@property (nonatomic, copy) NSArray <ORDERDetailEnumItemModel *> *DeliveryType;

@end

NS_ASSUME_NONNULL_END
